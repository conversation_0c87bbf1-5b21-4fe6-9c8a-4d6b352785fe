<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="16dp">

    <ImageView
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:src="@android:drawable/ic_dialog_alert"
        app:tint="@color/colorError"
        android:contentDescription="Error icon" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="¡Ups!"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginTop="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ha ocurrido un error al cargar esta sección."
        android:textAlignment="center"
        android:layout_marginTop="8dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Por favor, intenta de nuevo más tarde."
        android:textAlignment="center"
        android:layout_marginTop="8dp" />

</LinearLayout> 