<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="240dp"
    android:layout_height="280dp"
    android:layout_margin="8dp"
    app:cardCornerRadius="24dp"
    app:cardElevation="8dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/ripple">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Imagen de fondo del evento -->
        <ImageView
            android:id="@+id/event_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/event_image_description"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/backgrounds/scenic" />

        <!-- Gradiente para mejorar la legibilidad del texto -->
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/gradient_overlay"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Chip de categoría -->
        <com.google.android.material.chip.Chip
            android:id="@+id/event_category_chip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="Destacado"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:chipBackgroundColor="@color/primary"
            app:chipMinHeight="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Contenedor de información del evento -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <!-- Fecha del evento -->
            <TextView
                android:id="@+id/event_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="10 de Septiembre, 2023" />

            <!-- Título del evento -->
            <TextView
                android:id="@+id/event_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:ellipsize="end"
                android:fontFamily="@font/montserrat"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="22sp"
                android:textStyle="bold"
                tools:text="Festival de la Virgen de Itatí 2023" />

            <!-- Ubicación del evento -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:contentDescription="Ubicación"
                    android:src="@android:drawable/ic_menu_mylocation"
                    app:tint="@color/white" />

                <TextView
                    android:id="@+id/event_location"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:fontFamily="@font/montserrat"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    tools:text="Basílica de Itatí" />
            </LinearLayout>

            <!-- Botón de ver detalles eliminado -->
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>