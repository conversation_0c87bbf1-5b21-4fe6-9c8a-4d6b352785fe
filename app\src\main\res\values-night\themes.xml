<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.ItatiExplore" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/dark_primary</item>
        <item name="colorPrimaryVariant">@color/dark_primary</item>
        <item name="colorPrimaryContainer">@color/dark_primary</item>
        <item name="colorOnPrimary">@color/dark_primary_foreground</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/dark_secondary</item>
        <item name="colorSecondaryVariant">@color/dark_secondary</item>
        <item name="colorSecondaryContainer">@color/dark_secondary</item>
        <item name="colorOnSecondary">@color/dark_secondary_foreground</item>
        
        <!-- Accent color for components -->
        <item name="colorAccent">@color/dark_accent</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/dark_destructive</item>
        <item name="colorOnError">@color/dark_destructive_foreground</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/dark_background</item>
        <item name="colorOnBackground">@color/dark_foreground</item>
        
        <!-- Surface colors -->
        <item name="colorSurface">@color/dark_card</item>
        <item name="colorOnSurface">@color/dark_card_foreground</item>
        <item name="colorSurfaceVariant">@color/dark_muted</item>
        <item name="colorOnSurfaceVariant">@color/dark_muted_foreground</item>
        
        <!-- Status bar and navigation bar -->
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/dark_card</item>
        <item name="android:windowLightStatusBar" tools:targetApi="23">false</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">false</item>
        
        <!-- Shape and typography customization -->
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.ItatiExplore.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.ItatiExplore.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.ItatiExplore.LargeComponent</item>
        
        <!-- Component styles -->
        <item name="materialButtonStyle">@style/Widget.ItatiExplore.Button.Dark</item>
        <item name="materialCardViewStyle">@style/Widget.ItatiExplore.CardView.Dark</item>
        <item name="bottomNavigationStyle">@style/Widget.ItatiExplore.BottomNavigation.Dark</item>
        <item name="toolbarStyle">@style/Widget.ItatiExplore.Toolbar.Dark</item>
        <item name="tabStyle">@style/Widget.ItatiExplore.TabLayout.Dark</item>
        <item name="chipStyle">@style/Widget.ItatiExplore.Chip.Dark</item>
        <item name="textInputStyle">@style/Widget.ItatiExplore.TextInputLayout.Dark</item>
    </style>
    
    <!-- Component styles for dark theme -->
    <style name="Widget.ItatiExplore.Button.Dark" parent="Widget.ItatiExplore.Button">
        <item name="backgroundTint">@color/dark_primary</item>
        <item name="android:textColor">@color/dark_primary_foreground</item>
    </style>
    
    <style name="Widget.ItatiExplore.CardView.Dark" parent="Widget.ItatiExplore.CardView">
        <item name="cardBackgroundColor">@color/dark_card</item>
    </style>
    
    <style name="Widget.ItatiExplore.BottomNavigation.Dark" parent="Widget.ItatiExplore.BottomNavigation">
        <item name="backgroundTint">@color/dark_card</item>
        <item name="itemIconTint">@color/dark_primary</item>
        <item name="itemTextColor">@color/dark_primary</item>
    </style>
    
    <style name="Widget.ItatiExplore.Toolbar.Dark" parent="Widget.ItatiExplore.Toolbar">
        <item name="android:background">@color/dark_card</item>
        <item name="titleTextColor">@color/dark_foreground</item>
        <item name="subtitleTextColor">@color/dark_muted_foreground</item>
        <item name="colorControlNormal">@color/dark_foreground</item>
    </style>
    
    <style name="Widget.ItatiExplore.TabLayout.Dark" parent="Widget.ItatiExplore.TabLayout">
        <item name="android:background">@color/dark_card</item>
        <item name="tabTextColor">@color/dark_muted_foreground</item>
        <item name="tabSelectedTextColor">@color/dark_primary</item>
        <item name="tabIndicatorColor">@color/dark_primary</item>
    </style>
    
    <style name="Widget.ItatiExplore.Chip.Dark" parent="Widget.ItatiExplore.Chip">
        <item name="chipBackgroundColor">@color/dark_muted</item>
        <item name="chipStrokeColor">@color/dark_primary</item>
        <item name="chipIconTint">@color/dark_primary</item>
        <item name="android:textColor">@color/dark_foreground</item>
    </style>
    
    <style name="Widget.ItatiExplore.TextInputLayout.Dark" parent="Widget.ItatiExplore.TextInputLayout">
        <item name="boxStrokeColor">@color/dark_primary</item>
        <item name="hintTextColor">@color/dark_primary</item>
        <item name="android:textColorHint">@color/dark_muted_foreground</item>
    </style>
</resources>