<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Estilos modernos para formas -->
    <style name="ShapeAppearance.ItatiExplore.SmallComponent" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>

    <style name="ShapeAppearance.ItatiExplore.MediumComponent" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <style name="ShapeAppearance.ItatiExplore.LargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>

    <style name="ShapeAppearance.ItatiExplore.TopRoundedCorners" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>

    <style name="ShapeAppearance.App.TopRounded" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">24dp</item>
        <item name="cornerSizeTopRight">24dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>

    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <!-- Estilos modernos para componentes -->
    <style name="Widget.ItatiExplore.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">28dp</item>
        <item name="android:paddingEnd">28dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="android:fontFamily">@font/montserrat_medium</item>
        <item name="android:elevation">4dp</item>
    </style>

    <style name="Widget.ItatiExplore.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">28dp</item>
        <item name="android:paddingEnd">28dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:fontFamily">@font/montserrat_medium</item>
    </style>

    <style name="Widget.ItatiExplore.Button.TextButton" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/primary</item>
        <item name="rippleColor">@color/ripple</item>
    </style>

    <style name="Widget.ItatiExplore.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardElevation">8dp</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="rippleColor">@color/ripple</item>
        <item name="strokeColor">@color/border</item>
        <item name="strokeWidth">1dp</item>
    </style>

    <style name="Widget.ItatiExplore.BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
        <item name="itemRippleColor">@color/ripple</item>
        <item name="backgroundTint">@color/surface</item>
        <item name="elevation">8dp</item>
    </style>

    <style name="Widget.ItatiExplore.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/on_primary</item>
        <item name="subtitleTextColor">@color/on_primary</item>
        <item name="colorControlNormal">@color/on_primary</item>
    </style>

    <style name="Widget.ItatiExplore.TabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabTextColor">@color/on_surface_medium</item>
        <item name="tabSelectedTextColor">@color/primary</item>
        <item name="tabIndicatorColor">@color/primary</item>
        <item name="tabIndicatorHeight">3dp</item>
        <item name="tabRippleColor">@color/ripple</item>
    </style>

    <style name="Widget.ItatiExplore.Chip" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="chipBackgroundColor">@color/surface_variant</item>
        <item name="chipStrokeColor">@color/primary</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipIconTint">@color/primary</item>
        <item name="rippleColor">@color/ripple</item>
        <item name="checkedIconVisible">false</item>
    </style>

    <style name="Widget.ItatiExplore.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="android:textColorHint">@color/on_surface_medium</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
    </style>

    <!-- Estilos de tipografía -->
    <style name="TextAppearance.ItatiExplore.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="android:textSize">96sp</item>
    </style>

    <style name="Widget.ItatiExplore.Chip.Filter" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="android:textAppearance">@style/TextAppearance.ItatiExplore.Body2</item>
        <item name="android:fontFamily">@font/montserrat_medium</item>
        <item name="chipBackgroundColor">@drawable/modern_chip_background</item>
        <item name="chipStrokeColor">@color/border</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="android:textColor">@color/chip_text_color</item>
        <item name="rippleColor">@color/ripple</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipCornerRadius">20dp</item>
        <item name="chipMinHeight">40dp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="android:textSize">60sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="android:textSize">48sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Headline4" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="android:textSize">34sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Headline5" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textSize">24sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Headline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">20sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Subtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Subtitle2" parent="TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">12sp</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Button" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="TextAppearance.ItatiExplore.Overline" parent="TextAppearance.MaterialComponents.Overline">
        <item name="android:textSize">10sp</item>
    </style>

    <!-- Estilo para títulos de sección que se ajusten correctamente a los bordes -->
    <style name="TextAppearance.ItatiExplore.SectionTitle" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/montserrat</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
</resources>