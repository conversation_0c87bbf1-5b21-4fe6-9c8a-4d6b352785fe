{"logs": [{"outputFile": "com.Rages.itatiexplore.app-mergeReleaseResources-58:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eee50e8311d47e456960a4e70d71cc7f\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "100,170,254,338,434,536,638,6166", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "165,249,333,429,531,633,727,6250"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\ItatiExplore\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,63,54,59,83,76,90,69", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "100,3518,3099,3344,4588,4208,4949,3826", "endLines": "51,67,57,61,88,81,94,74", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "3044,3816,3334,3508,4939,4578,5266,4198"}, "to": {"startLines": "9,86,91,95,98,104,110,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "732,6372,6675,6915,7084,7440,7815,8137", "endLines": "58,90,94,97,103,109,114,120", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "3400,6670,6910,7079,7435,7810,8132,8509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\041807292bbdd7c1d354fe5fd3376c8c\\transformed\\material-1.11.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,85,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3405,3480,3591,3680,3781,3888,3995,4094,4201,4304,4392,4516,4618,4720,4836,4938,5052,5180,5296,5418,5554,5674,5808,5928,6040,6255,8514,8638,8768,8890,9028,9162,9278", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "3475,3586,3675,3776,3883,3990,4089,4196,4299,4387,4511,4613,4715,4831,4933,5047,5175,5291,5413,5549,5669,5803,5923,6035,6161,6367,8633,8763,8885,9023,9157,9273,9393"}}]}]}