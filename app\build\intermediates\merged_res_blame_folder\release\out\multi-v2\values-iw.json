{"logs": [{"outputFile": "com.Rages.itatiexplore.app-mergeReleaseResources-58:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eee50e8311d47e456960a4e70d71cc7f\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,11557", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,11634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73313f5160b05cc046f11947ceee314d\\transformed\\preference-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,258,334,459,628,709", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "169,253,329,454,623,704,783"}, "to": {"startLines": "62,65,129,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6019,6244,11281,11432,11740,11909,11990", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "6083,6323,11352,11552,11904,11985,12064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d3278740ff7f0b8c5a39439b87b9fdb4\\transformed\\browser-1.4.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "63,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6088,6416,6516,6622", "endColumns": "90,99,105,101", "endOffsets": "6174,6511,6617,6719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f94c6f8749721534875926cd062b186\\transformed\\navigation-ui-2.7.7\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,119", "endOffsets": "156,276"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "11055,11161", "endColumns": "105,119", "endOffsets": "11156,11276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5409d61ae460dbbf7f5d5450a39e324f\\transformed\\play-services-base-18.5.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3948,4051,4205,4330,4434,4573,4698,4810,5031,5167,5271,5416,5539,5673,5818,5878,5938", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "4046,4200,4325,4429,4568,4693,4805,4908,5162,5266,5411,5534,5668,5813,5873,5933,6014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b94a72bf5ecd12be9995f18ede9759a3\\transformed\\play-services-basement-18.5.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4913", "endColumns": "117", "endOffsets": "5026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2ba638d762ff4542c3e65fea48f8e860\\transformed\\core-1.9.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "133", "startColumns": "4", "startOffsets": "11639", "endColumns": "100", "endOffsets": "11735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\041807292bbdd7c1d354fe5fd3376c8c\\transformed\\material-1.11.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1082,1170,1240,1303,1395,1458,1518,1577,1640,1701,1755,1857,1914,1973,2027,2095,2206,2287,2369,2501,2572,2645,2769,2857,2933,2986,3040,3106,3179,3255,3341,3419,3489,3564,3646,3714,3815,3900,3970,4060,4151,4225,4298,4387,4438,4519,4586,4668,4753,4815,4879,4942,5010,5104,5199,5289,5386,5443,5501", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "306,383,458,535,635,726,819,932,1012,1077,1165,1235,1298,1390,1453,1513,1572,1635,1696,1750,1852,1909,1968,2022,2090,2201,2282,2364,2496,2567,2640,2764,2852,2928,2981,3035,3101,3174,3250,3336,3414,3484,3559,3641,3709,3810,3895,3965,4055,4146,4220,4293,4382,4433,4514,4581,4663,4748,4810,4874,4937,5005,5099,5194,5284,5381,5438,5496,5571"}, "to": {"startLines": "2,36,37,38,39,40,41,42,43,64,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3242,3319,3394,3471,3571,3662,3755,3868,6179,6328,6724,6794,6857,6949,7012,7072,7131,7194,7255,7309,7411,7468,7527,7581,7649,7760,7841,7923,8055,8126,8199,8323,8411,8487,8540,8594,8660,8733,8809,8895,8973,9043,9118,9200,9268,9369,9454,9524,9614,9705,9779,9852,9941,9992,10073,10140,10222,10307,10369,10433,10496,10564,10658,10753,10843,10940,10997,11357", "endLines": "6,36,37,38,39,40,41,42,43,64,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,130", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "356,3314,3389,3466,3566,3657,3750,3863,3943,6239,6411,6789,6852,6944,7007,7067,7126,7189,7250,7304,7406,7463,7522,7576,7644,7755,7836,7918,8050,8121,8194,8318,8406,8482,8535,8589,8655,8728,8804,8890,8968,9038,9113,9195,9263,9364,9449,9519,9609,9700,9774,9847,9936,9987,10068,10135,10217,10302,10364,10428,10491,10559,10653,10748,10838,10935,10992,11050,11427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\58d1699a96be4f829e35b4b9d6e21979\\transformed\\credentials-1.3.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,115", "endOffsets": "159,275"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3017,3126", "endColumns": "108,115", "endOffsets": "3121,3237"}}]}]}