<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="24dp"
    app:cardElevation="6dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/ripple">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Imagen principal con esquinas redondeadas -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/location_image"
            android:layout_width="match_parent"
            android:layout_height="180dp"
            android:scaleType="centerCrop"
            android:contentDescription="Imagen del lugar turístico"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:shapeAppearanceOverlay="@style/ShapeAppearance.App.TopRounded"
            tools:src="@tools:sample/backgrounds/scenic" />

        <!-- Gradiente para mejorar legibilidad del texto -->
        <View
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@drawable/gradient_overlay"
            app:layout_constraintBottom_toBottomOf="@id/location_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Chip de categoría con estilo mejorado -->
        <com.google.android.material.chip.Chip
            android:id="@+id/location_category_chip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:textColor="@color/white"
            android:textSize="11sp"
            android:fontFamily="@font/montserrat_medium"
            app:chipBackgroundColor="@color/primary"
            app:chipMinHeight="28dp"
            app:chipStrokeWidth="0dp"
            app:chipStartPadding="12dp"
            app:chipEndPadding="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Religioso" />

        <!-- Nombre del lugar sobre la imagen con mejor legibilidad -->
        <TextView
            android:id="@+id/location_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="20dp"
            android:fontFamily="@font/montserrat"
            android:textColor="@color/white"
            android:textSize="22sp"
            android:textStyle="bold"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintBottom_toBottomOf="@id/location_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Basílica de Itatí" />

        <!-- Contenido de información con mejor espaciado -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingTop="16dp"
            android:paddingBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/location_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- Dirección del lugar con mejor diseño -->
            <ImageView
                android:id="@+id/icon_location"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_location"
                app:tint="@color/primary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/location_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/on_surface_medium"
                android:textSize="13sp"
                app:layout_constraintStart_toEndOf="@id/icon_location"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/icon_location"
                app:layout_constraintBottom_toBottomOf="@id/icon_location"
                tools:text="Av. Virgen de Itatí 1234, Itatí, Corrientes" />

            <!-- Horario del lugar con mejor diseño -->
            <ImageView
                android:id="@+id/icon_time"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginTop="12dp"
                android:src="@android:drawable/ic_menu_recent_history"
                app:tint="@color/primary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/location_address" />

            <TextView
                android:id="@+id/location_hours"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/on_surface_medium"
                android:textSize="13sp"
                app:layout_constraintStart_toEndOf="@id/icon_time"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/icon_time"
                app:layout_constraintBottom_toBottomOf="@id/icon_time"
                tools:text="Abierto: 8:00 - 20:00 hs" />

            <!-- Línea divisoria sutil -->
            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="@color/gray_200"
                app:layout_constraintTop_toBottomOf="@id/icon_time"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Descripción breve con mejor diseño -->
            <TextView
                android:id="@+id/location_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/on_surface"
                android:textSize="14sp"
                android:lineSpacingExtra="4dp"
                android:maxLines="3"
                android:ellipsize="end"
                app:layout_constraintTop_toBottomOf="@id/divider"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="Uno de los santuarios más importantes de Argentina, dedicado a la Virgen de Itatí, patrona de la provincia de Corrientes. Recibe miles de peregrinos cada año." />

            <!-- Botón de ver detalles con mejor diseño -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="16dp"
                android:text="Ver Detalles"
                android:textColor="@color/primary"
                android:textSize="12sp"
                android:fontFamily="@font/montserrat_medium"
                android:paddingHorizontal="16dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                app:cornerRadius="20dp"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp"
                app:icon="@android:drawable/ic_menu_info_details"
                app:iconSize="16dp"
                app:iconTint="@color/primary"
                app:iconGravity="textStart"
                app:iconPadding="8dp"
                app:layout_constraintTop_toBottomOf="@id/location_description"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Categoría original (oculta para mantener compatibilidad) -->
            <TextView
                android:id="@+id/location_category"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>