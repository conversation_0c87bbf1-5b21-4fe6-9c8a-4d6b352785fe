<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.EventsFragment">

    <!-- Panel de búsqueda flotante superior -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/search_panel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:cardBackgroundColor="#22ffffff"
        app:cardCornerRadius="25dp"
        app:cardElevation="0dp"
        app:strokeWidth="1dp"
        app:strokeColor="#44ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="20dp"
            android:paddingVertical="12dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_search"
                app:tint="@color/primary"
                android:layout_marginEnd="12dp" />

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/search_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Buscar eventos..."
                android:textColor="@color/white"
                android:textColorHint="@color/muted_foreground"
                android:background="@android:color/transparent"
                android:fontFamily="@font/montserrat"
                android:textSize="16sp"
                android:maxLines="1"
                android:imeOptions="actionSearch" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/filter_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:icon="@android:drawable/ic_menu_sort_by_size"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="20dp"
                app:iconTint="@color/accent"
                app:backgroundTint="#44ffffff"
                app:cornerRadius="20dp"
                app:strokeWidth="0dp" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Panel de filtros flotante -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/filter_panel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        app:cardBackgroundColor="#22ffffff"
        app:cardCornerRadius="25dp"
        app:cardElevation="0dp"
        app:strokeWidth="1dp"
        app:strokeColor="#44ffffff"
        app:layout_constraintTop_toBottomOf="@id/search_panel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:clipToPadding="false">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/filter_chip_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:singleSelection="true"
                app:chipSpacingHorizontal="12dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_all"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Todos"
                    android:checked="true"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_today"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Hoy"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_week"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Esta semana"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_religious"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Religiosos"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>
    </com.google.android.material.card.MaterialCardView>

    <!-- Lista de eventos con scroll -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="20dp"
        android:clipToPadding="false"
        android:paddingHorizontal="20dp"
        android:paddingBottom="100dp"
        app:layout_constraintTop_toBottomOf="@id/filter_panel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="5"
        tools:listitem="@layout/item_event" />

    <!-- Eventos destacados horizontales -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/featured_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:clipToPadding="false"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        app:layout_constraintTop_toBottomOf="@id/filter_panel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="3"
        tools:listitem="@layout/item_featured_event" />

    <!-- Indicador de carga -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/loading_overlay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:cardBackgroundColor="#44ffffff"
        app:cardCornerRadius="25dp"
        app:cardElevation="0dp"
        app:strokeWidth="1dp"
        app:strokeColor="#66ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="24dp">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                app:indicatorColor="@color/primary"
                app:indicatorSize="48dp"
                app:trackThickness="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="Cargando eventos..."
                android:textColor="@color/white"
                android:textSize="14sp"
                android:fontFamily="@font/montserrat" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Vista vacía -->
    <LinearLayout
        android:id="@+id/empty_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@android:drawable/ic_menu_my_calendar"
            app:tint="@color/muted_foreground"
            android:alpha="0.6" />

        <TextView
            android:id="@+id/empty_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="No hay eventos"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="@font/montserrat" />

        <TextView
            android:layout_width="280dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Intenta con otros filtros o vuelve más tarde"
            android:textAlignment="center"
            android:textColor="@color/muted_foreground"
            android:textSize="14sp"
            android:fontFamily="@font/montserrat" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>