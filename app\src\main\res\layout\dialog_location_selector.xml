<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Seleccionar ubicación"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/primary"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/search_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:startIconDrawable="@drawable/ic_search"
        app:layout_constraintTop_toBottomOf="@id/dialog_title">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/search_location"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Buscar lugar"
            android:imeOptions="actionSearch"
            android:inputType="text" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/recent_locations_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Lugares recientes"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/search_layout" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/locations_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/recent_locations_title"
        tools:listitem="@layout/item_location" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_cancel"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Cancelar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/locations_recycler_view" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_select_on_map"
        style="@style/Widget.MaterialComponents.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Seleccionar en mapa"
        app:icon="@drawable/ic_map"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/locations_recycler_view" />
</androidx.constraintlayout.widget.ConstraintLayout>