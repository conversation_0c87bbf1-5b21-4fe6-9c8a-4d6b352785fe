<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainAppActivity">

    <!-- Contenido principal -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Contenedor de fragmentos que ocupa toda la pantalla -->
            <FrameLayout
                android:id="@+id/fragment_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Botón flotante para mostrar/ocultar la navegación -->
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_menu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:contentDescription="Menú de navegación"
                android:src="@android:drawable/ic_menu_compass"
                app:backgroundTint="@color/primary"
                app:fabSize="normal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:tint="@color/white"
                app:borderWidth="0dp"
                app:elevation="6dp" />

            <!-- Contenedor para mostrar información del marcador seleccionado -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/marker_info_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="12dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="12dp"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- Imagen del lugar - Ahora más grande -->
                    <ImageView
                        android:id="@+id/marker_image"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:scaleType="centerCrop"
                        android:contentDescription="Imagen del lugar"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:src="@tools:sample/backgrounds/scenic" />

                    <!-- Gradiente sobre la imagen -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:background="@drawable/image_gradient"
                        app:layout_constraintBottom_toBottomOf="@id/marker_image" />

                    <!-- Categoría (sobre la imagen) -->
                    <TextView
                        android:id="@+id/marker_category"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="16dp"
                        android:background="@drawable/category_pill_background"
                        android:fontFamily="@font/montserrat"
                        android:paddingStart="12dp"
                        android:paddingTop="6dp"
                        android:paddingEnd="12dp"
                        android:paddingBottom="6dp"
                        android:textAppearance="?attr/textAppearanceCaption"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Categoría" />

                    <!-- Título sobre la imagen -->
                    <TextView
                        android:id="@+id/marker_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="16dp"
                        android:fontFamily="@font/montserrat"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:shadowColor="#80000000"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        app:layout_constraintBottom_toBottomOf="@id/marker_image"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:text="Nombre del lugar" />

                    <!-- Contenido de texto -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        app:layout_constraintTop_toBottomOf="@id/marker_image"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <!-- Descripción -->
                        <TextView
                            android:id="@+id/marker_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/montserrat"
                            android:maxLines="4"
                            android:ellipsize="end"
                            android:textAppearance="?attr/textAppearanceBody2"
                            android:textColor="@color/on_surface_medium"
                            tools:text="Descripción del lugar que puede ser bastante larga y ocupar varias líneas de texto. Aquí se mostrará un resumen de la descripción." />

                        <!-- Calificación del lugar -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Calificación:"
                                android:fontFamily="@font/montserrat"
                                android:textStyle="bold"
                                android:textSize="14sp"
                                android:layout_marginEnd="4dp"/>

                            <RatingBar
                                android:id="@+id/marker_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="?android:attr/ratingBarStyleSmall"
                                android:numStars="5"
                                android:isIndicator="true"
                                android:stepSize="0.1"
                                android:layout_marginEnd="8dp"/>

                            <TextView
                                android:id="@+id/marker_rating_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/montserrat"
                                android:textSize="14sp"
                                tools:text="4.5/5"/>
                        </LinearLayout>

                        <!-- Botones de acción -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:orientation="horizontal">

                            <!-- Botón para ver comentarios (antes era Compartir) -->
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_share"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginEnd="8dp"
                                android:text="Ver comentarios"
                                app:icon="@drawable/ic_comments"
                                android:textColor="@color/primary" />

                            <!-- Botón para ver detalles -->
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_view_details"
                                style="@style/Widget.MaterialComponents.Button"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Cómo llegar"
                                android:backgroundTint="@color/primary"
                                android:textColor="@color/white"
                                app:icon="@android:drawable/ic_menu_directions" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Botón para cerrar -->
                    <ImageButton
                        android:id="@+id/btn_close_marker_info"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_margin="8dp"
                        android:background="@drawable/circle_background"
                        android:contentDescription="Cerrar"
                        android:src="@android:drawable/ic_menu_close_clear_cancel"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/white" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.card.MaterialCardView>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Menú de navegación lateral -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigation_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/white"
        app:headerLayout="@layout/nav_header"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/on_surface"
        app:itemIconPadding="16dp"
        app:itemHorizontalPadding="24dp"
        app:itemShapeFillColor="@color/nav_item_background_color"
        app:itemShapeInsetStart="8dp"
        app:itemShapeInsetEnd="8dp"
        app:itemShapeAppearance="@style/ShapeAppearance.ItatiExplore.SmallComponent"
        app:menu="@menu/drawer_menu" />

</androidx.drawerlayout.widget.DrawerLayout>