<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".MainAppActivity">

    <!-- Fondo con gradiente animado -->
    <View
        android:id="@+id/animated_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/modern_gradient_header" />

    <!-- Contenedor principal con efecto glass -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="8dp">

        <!-- <PERSON><PERSON> superior flotante con glass effect -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/top_bar_glass"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:cardBackgroundColor="#33ffffff"
            app:cardCornerRadius="30dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="#44ffffff"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="20dp">

                <!-- Logo/Título -->
                <TextView
                    android:id="@+id/app_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Itatí"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/montserrat"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <!-- Botón de menú -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/menu_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:insetLeft="0dp"
                    android:insetTop="0dp"
                    android:insetRight="0dp"
                    android:insetBottom="0dp"
                    app:icon="@android:drawable/ic_menu_sort_by_size"
                    app:iconGravity="textStart"
                    app:iconPadding="0dp"
                    app:iconSize="20dp"
                    app:iconTint="@color/white"
                    app:backgroundTint="#44ffffff"
                    app:cornerRadius="20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Contenedor de fragmentos -->
        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:layout_constraintTop_toBottomOf="@id/top_bar_glass"
            app:layout_constraintBottom_toTopOf="@id/bottom_nav_glass"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Navegación inferior flotante con glass effect -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bottom_nav_glass"
            android:layout_width="0dp"
            android:layout_height="70dp"
            android:layout_marginBottom="16dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            app:cardBackgroundColor="#33ffffff"
            app:cardCornerRadius="35dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="#44ffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingHorizontal="20dp">

                <!-- Botón Mapa -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/nav_map"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Mapa"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:fontFamily="@font/montserrat"
                    app:icon="@drawable/ic_map"
                    app:iconGravity="top"
                    app:iconSize="20dp"
                    app:iconTint="@color/primary"
                    app:backgroundTint="#44ffffff"
                    app:cornerRadius="25dp"
                    app:strokeWidth="0dp" />

                <!-- Botón Eventos -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/nav_events"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="4dp"
                    android:text="Eventos"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:fontFamily="@font/montserrat"
                    app:icon="@drawable/ic_events"
                    app:iconGravity="top"
                    app:iconSize="20dp"
                    app:iconTint="@color/accent"
                    app:backgroundTint="#44ffffff"
                    app:cornerRadius="25dp"
                    app:strokeWidth="0dp" />

                <!-- Botón Transporte -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/nav_transport"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="4dp"
                    android:text="Viajes"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:fontFamily="@font/montserrat"
                    app:icon="@drawable/ic_transport"
                    app:iconGravity="top"
                    app:iconSize="20dp"
                    app:iconTint="@color/chart_3"
                    app:backgroundTint="#44ffffff"
                    app:cornerRadius="25dp"
                    app:strokeWidth="0dp" />

                <!-- Botón Más -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/nav_more"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Más"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:fontFamily="@font/montserrat"
                    app:icon="@android:drawable/ic_menu_more"
                    app:iconGravity="top"
                    app:iconSize="20dp"
                    app:iconTint="@color/chart_4"
                    app:backgroundTint="#44ffffff"
                    app:cornerRadius="25dp"
                    app:strokeWidth="0dp" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- FAB central flotante -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="50dp"
            android:contentDescription="Acción principal"
            android:src="@drawable/ic_add"
            app:backgroundTint="@color/primary"
            app:fabSize="normal"
            app:tint="@color/black"
            app:borderWidth="0dp"
            app:elevation="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Panel de información flotante con glass effect -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/marker_info_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:visibility="gone"
            app:cardBackgroundColor="#22ffffff"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="#44ffffff"
            app:layout_constraintBottom_toTopOf="@id/bottom_nav_glass"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Título del lugar -->
                <TextView
                    android:id="@+id/marker_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Nombre del lugar"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/montserrat"
                    android:layout_marginBottom="8dp" />

                <!-- Categoría -->
                <TextView
                    android:id="@+id/marker_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Categoría"
                    android:textColor="@color/primary"
                    android:textSize="14sp"
                    android:fontFamily="@font/montserrat"
                    android:layout_marginBottom="8dp" />

                <!-- Descripción -->
                <TextView
                    android:id="@+id/marker_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Descripción del lugar"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:fontFamily="@font/montserrat"
                    android:alpha="0.8"
                    android:maxLines="3"
                    android:ellipsize="end"
                    android:layout_marginBottom="16dp" />

                <!-- Botones de acción -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_share"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="Ver más"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:fontFamily="@font/montserrat"
                        app:backgroundTint="#44ffffff"
                        app:cornerRadius="20dp"
                        app:strokeWidth="0dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_view_details"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="Ir"
                        android:textColor="@color/black"
                        android:textSize="12sp"
                        android:fontFamily="@font/montserrat"
                        app:backgroundTint="@color/primary"
                        app:cornerRadius="20dp"
                        app:strokeWidth="0dp" />

                </LinearLayout>

                <!-- Botón cerrar -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_close_marker_info"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="end"
                    android:layout_marginTop="-60dp"
                    android:layout_marginEnd="10dp"
                    android:insetLeft="0dp"
                    android:insetTop="0dp"
                    android:insetRight="0dp"
                    android:insetBottom="0dp"
                    app:icon="@android:drawable/ic_menu_close_clear_cancel"
                    app:iconGravity="textStart"
                    app:iconPadding="0dp"
                    app:iconSize="16dp"
                    app:iconTint="@color/white"
                    app:backgroundTint="#44ffffff"
                    app:cornerRadius="15dp" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>