1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Rages.itatiexplore"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permisos necesarios -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission
14-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:8:5-108
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:8:79-105
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:9:5-79
17-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:9:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:10:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:10:22-78
19    <!-- Permisos adicionales para osmdroid -->
20    <uses-permission
20-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:12:5-107
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:12:22-77
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:12:78-104
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
23-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:13:5-76
23-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:13:22-73
24    <!-- Permisos para Credential Manager -->
25    <uses-permission android:name="android.permission.CREDENTIAL_MANAGER" />
25-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:15:5-77
25-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:15:22-74
26    <!-- Permiso para el portapapeles -->
27    <uses-permission android:name="android.permission.CLIPBOARD_READ" />
27-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:17:5-73
27-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:17:22-70
28    <uses-permission android:name="android.permission.CLIPBOARD_WRITE" />
28-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:18:5-74
28-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:18:22-71
29    <!-- Permisos para trabajo en segundo plano -->
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:20:5-68
30-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:20:22-65
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:21:5-77
31-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:21:22-74
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:22:5-77
32-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:22:22-74
33
34    <!-- Required by older versions of Google Play services to create IID tokens -->
35    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
35-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
35-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
36    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
36-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
36-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
37-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
37-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
38-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
38-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
39    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
39-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0520211f63b9720dfc2ff83c65be1ac4\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
39-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0520211f63b9720dfc2ff83c65be1ac4\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
40    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
40-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
40-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
41
42    <permission
42-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba638d762ff4542c3e65fea48f8e860\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
43        android:name="com.Rages.itatiexplore.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba638d762ff4542c3e65fea48f8e860\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba638d762ff4542c3e65fea48f8e860\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.Rages.itatiexplore.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba638d762ff4542c3e65fea48f8e860\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba638d762ff4542c3e65fea48f8e860\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
47
48    <supports-screens
48-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:9:5-12:40
49        android:anyDensity="true"
49-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:10:9-34
50        android:largeScreens="true"
50-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:11:9-36
51        android:normalScreens="true" />
51-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:12:9-37
52
53    <uses-feature
53-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:14:5-16:36
54        android:name="android.hardware.location.network"
54-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:15:9-57
55        android:required="false" />
55-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:16:9-33
56    <uses-feature
56-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:17:5-19:36
57        android:name="android.hardware.location.gps"
57-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:18:9-53
58        android:required="false" />
58-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:19:9-33
59    <uses-feature
59-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:20:5-22:36
60        android:name="android.hardware.telephony"
60-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:21:9-50
61        android:required="false" />
61-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:22:9-33
62    <uses-feature
62-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:23:5-25:36
63        android:name="android.hardware.wifi"
63-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:24:9-45
64        android:required="false" />
64-->[org.osmdroid:osmdroid-android:6.1.16] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e21dce00f583e811c0ae7b8172ae4\transformed\osmdroid-android-6.1.16\AndroidManifest.xml:25:9-33
65
66    <application
66-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:24:5-100:19
67        android:allowBackup="true"
67-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:25:9-35
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba638d762ff4542c3e65fea48f8e860\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
69        android:dataExtractionRules="@xml/data_extraction_rules"
69-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:26:9-65
70        android:debuggable="true"
71        android:extractNativeLibs="false"
72        android:fullBackupContent="@xml/backup_rules"
72-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:27:9-54
73        android:icon="@mipmap/ic_launcher"
73-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:28:9-43
74        android:label="@string/app_name"
74-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:29:9-41
75        android:networkSecurityConfig="@xml/network_security_config"
75-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:36:9-69
76        android:roundIcon="@mipmap/ic_launcher_round"
76-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:30:9-54
77        android:supportsRtl="true"
77-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:31:9-35
78        android:testOnly="true"
79        android:theme="@style/Theme.ItatiExplore"
79-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:32:9-50
80        android:usesCleartextTraffic="false" >
80-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:35:9-45
81
82        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages. -->
83        <meta-data
83-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:39:9-41:68
84            android:name="com.google.firebase.messaging.default_notification_icon"
84-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:40:13-83
85            android:resource="@drawable/ic_stat_ic_notification" />
85-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:41:13-65
86
87        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming notification message. -->
88        <meta-data
88-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:44:9-46:53
89            android:name="com.google.firebase.messaging.default_notification_color"
89-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:45:13-84
90            android:resource="@color/colorAccent" />
90-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:46:13-50
91
92        <!-- Actividad principal que muestra la pantalla de inicio -->
93        <activity
93-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:49:9-57:20
94            android:name="com.Rages.itatiexplore.MainActivity"
94-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:50:13-41
95            android:exported="true"
95-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:52:13-36
96            android:screenOrientation="portrait" >
96-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:51:13-49
97            <intent-filter>
97-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:53:13-56:29
98                <action android:name="android.intent.action.MAIN" />
98-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:54:17-69
98-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:54:25-66
99
100                <category android:name="android.intent.category.LAUNCHER" />
100-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:55:17-77
100-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:55:27-74
101            </intent-filter>
102        </activity>
103
104        <!-- Actividad de la aplicación principal -->
105        <activity
105-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:60:9-64:48
106            android:name="com.Rages.itatiexplore.MainAppActivity"
106-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:61:13-44
107            android:exported="false"
107-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:63:13-37
108            android:label="@string/app_name"
108-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:64:13-45
109            android:screenOrientation="portrait" />
109-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:62:13-49
110
111        <!-- Actividad de detalles de eventos -->
112        <activity
112-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:67:9-72:51
113            android:name="com.Rages.itatiexplore.EventDetailActivity"
113-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:68:13-48
114            android:exported="false"
114-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:70:13-37
115            android:label="Detalles del Evento"
115-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:72:13-48
116            android:parentActivityName="com.Rages.itatiexplore.MainAppActivity"
116-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:71:13-58
117            android:screenOrientation="portrait" />
117-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:69:13-49
118
119        <!-- Actividad de agregar lugar -->
120        <activity
120-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:75:9-80:57
121            android:name="com.Rages.itatiexplore.activities.AddPlaceActivity"
121-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:76:13-56
122            android:exported="false"
122-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:78:13-37
123            android:parentActivityName="com.Rages.itatiexplore.MainAppActivity"
123-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:79:13-58
124            android:screenOrientation="portrait"
124-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:77:13-49
125            android:theme="@style/Theme.ItatiExplore" />
125-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:80:13-54
126
127        <!-- Actividad de detalles de transporte -->
128        <activity
128-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:83:9-89:57
129            android:name="com.Rages.itatiexplore.activities.TransportDetailActivity"
129-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:84:13-63
130            android:exported="false"
130-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:86:13-37
131            android:label="Detalles del Transporte"
131-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:88:13-52
132            android:parentActivityName="com.Rages.itatiexplore.MainAppActivity"
132-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:87:13-58
133            android:screenOrientation="portrait"
133-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:85:13-49
134            android:theme="@style/Theme.ItatiExplore" />
134-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:89:13-54
135
136        <!-- Firebase Cloud Messaging Service -->
137        <service
137-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:92:9-98:19
138            android:name="com.Rages.itatiexplore.MyFirebaseMessagingService"
138-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:93:13-55
139            android:exported="false" >
139-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:94:13-37
140            <intent-filter>
140-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:95:13-97:29
141                <action android:name="com.google.firebase.MESSAGING_EVENT" />
141-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:96:17-78
141-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:96:25-75
142            </intent-filter>
143        </service>
144
145        <activity
145-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
146            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
146-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
147            android:excludeFromRecents="true"
147-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
148            android:exported="true"
148-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
149            android:launchMode="singleTask"
149-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
150            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
150-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
151            <intent-filter>
151-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
152                <action android:name="android.intent.action.VIEW" />
152-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
152-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
153
154                <category android:name="android.intent.category.DEFAULT" />
154-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
154-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
155                <category android:name="android.intent.category.BROWSABLE" />
155-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
155-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
156
157                <data
157-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:41:17-44:51
158                    android:host="firebase.auth"
158-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:42:21-49
159                    android:path="/"
159-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:43:21-37
160                    android:scheme="genericidp" />
160-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:44:21-48
161            </intent-filter>
162        </activity>
163        <activity
163-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
164            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
164-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
165            android:excludeFromRecents="true"
165-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
166            android:exported="true"
166-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
167            android:launchMode="singleTask"
167-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
168            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
168-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
169            <intent-filter>
169-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
170                <action android:name="android.intent.action.VIEW" />
170-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
170-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
171
172                <category android:name="android.intent.category.DEFAULT" />
172-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
172-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
173                <category android:name="android.intent.category.BROWSABLE" />
173-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
173-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
174
175                <data
175-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:41:17-44:51
176                    android:host="firebase.auth"
176-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:42:21-49
177                    android:path="/"
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:43:21-37
178                    android:scheme="recaptcha" />
178-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:44:21-48
179            </intent-filter>
180        </activity>
181
182        <service
182-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:66:9-72:19
183            android:name="com.google.firebase.components.ComponentDiscoveryService"
183-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:67:13-84
184            android:directBootAware="true"
184-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
185            android:exported="false" >
185-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
186            <meta-data
186-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
187                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
187-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7094153e2c9d821d31fb07ed026e95aa\transformed\firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
189            <meta-data
189-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59c4357cf614340b2403249202a414ac\transformed\firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
190                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
190-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59c4357cf614340b2403249202a414ac\transformed\firebase-config-22.1.0\AndroidManifest.xml:30:17-128
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59c4357cf614340b2403249202a414ac\transformed\firebase-config-22.1.0\AndroidManifest.xml:31:17-82
192            <meta-data
192-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59c4357cf614340b2403249202a414ac\transformed\firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
193                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
193-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59c4357cf614340b2403249202a414ac\transformed\firebase-config-22.1.0\AndroidManifest.xml:33:17-117
194                android:value="com.google.firebase.components.ComponentRegistrar" />
194-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59c4357cf614340b2403249202a414ac\transformed\firebase-config-22.1.0\AndroidManifest.xml:34:17-82
195            <meta-data
195-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\475d81a786c5b28378db7356f14add6f\transformed\firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
196                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
196-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\475d81a786c5b28378db7356f14add6f\transformed\firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\475d81a786c5b28378db7356f14add6f\transformed\firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
198            <meta-data
198-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\475d81a786c5b28378db7356f14add6f\transformed\firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
199                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
199-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\475d81a786c5b28378db7356f14add6f\transformed\firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\475d81a786c5b28378db7356f14add6f\transformed\firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
201            <meta-data
201-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
202                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
202-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
204            <meta-data
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
205                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
205-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
207            <meta-data
207-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
208                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
208-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0c55ff6ac0511902bfada4606298537\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
210            <meta-data
210-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b27925cff417ea918f5c1747ab37095\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
211                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
211-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b27925cff417ea918f5c1747ab37095\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b27925cff417ea918f5c1747ab37095\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
213            <meta-data
213-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b27925cff417ea918f5c1747ab37095\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
214                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
214-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b27925cff417ea918f5c1747ab37095\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b27925cff417ea918f5c1747ab37095\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
216            <meta-data
216-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f793ce1972ea786a3208cfc1d2fe5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
217                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
217-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f793ce1972ea786a3208cfc1d2fe5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91f793ce1972ea786a3208cfc1d2fe5e\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
219            <meta-data
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
220                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
222            <meta-data
222-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125dfd8311d96658bd48eb6785550100\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
223                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
223-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125dfd8311d96658bd48eb6785550100\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125dfd8311d96658bd48eb6785550100\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
225            <meta-data
225-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6b6d2adafd71b39b2100275b37d0662\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
226                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
226-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6b6d2adafd71b39b2100275b37d0662\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6b6d2adafd71b39b2100275b37d0662\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
228        </service>
229        <service
229-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:24:9-32:19
230            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
230-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:25:13-94
231            android:enabled="true"
231-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:26:13-35
232            android:exported="false" >
232-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:27:13-37
233            <meta-data
233-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:29:13-31:104
234                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
234-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:30:17-76
235                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
235-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:31:17-101
236        </service>
237
238        <activity
238-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:34:9-41:20
239            android:name="androidx.credentials.playservices.HiddenActivity"
239-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:35:13-76
240            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
240-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:36:13-87
241            android:enabled="true"
241-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:37:13-35
242            android:exported="false"
242-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:38:13-37
243            android:fitsSystemWindows="true"
243-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:39:13-45
244            android:theme="@style/Theme.Hidden" >
244-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee9c1fb2447552988641b5a3fff6438\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:40:13-48
245        </activity>
246
247        <receiver
247-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
248            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
248-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
249            android:exported="true"
249-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
250            android:permission="com.google.android.c2dm.permission.SEND" >
250-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
251            <intent-filter>
251-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
252                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
252-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
252-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
253            </intent-filter>
254
255            <meta-data
255-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
256                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
256-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
257                android:value="true" />
257-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
258        </receiver>
259        <!--
260             FirebaseMessagingService performs security checks at runtime,
261             but set to not exported to explicitly avoid allowing another app to call it.
262        -->
263        <service
263-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
264            android:name="com.google.firebase.messaging.FirebaseMessagingService"
264-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
265            android:directBootAware="true"
265-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
266            android:exported="false" >
266-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1657bb1597d018b38291152afe60e792\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
267            <intent-filter android:priority="-500" >
267-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:95:13-97:29
268                <action android:name="com.google.firebase.MESSAGING_EVENT" />
268-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:96:17-78
268-->C:\Users\<USER>\AndroidStudioProjects\ItatiExplore\app\src\main\AndroidManifest.xml:96:25-75
269            </intent-filter>
270        </service>
271
272        <activity
272-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
273            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
273-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
274            android:excludeFromRecents="true"
274-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
275            android:exported="false"
275-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
276            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
276-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
277        <!--
278            Service handling Google Sign-In user revocation. For apps that do not integrate with
279            Google Sign-In, this service will never be started.
280        -->
281        <service
281-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
282            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
282-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
283            android:exported="true"
283-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
284            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
284-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
285            android:visibleToInstantApps="true" />
285-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36519643b92c12574e23108c10e9cfa0\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
286
287        <receiver
287-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
288            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
288-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
289            android:enabled="true"
289-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
290            android:exported="false" >
290-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
291        </receiver>
292
293        <service
293-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
294            android:name="com.google.android.gms.measurement.AppMeasurementService"
294-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
295            android:enabled="true"
295-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
296            android:exported="false" />
296-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
297        <service
297-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
298            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
298-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
299            android:enabled="true"
299-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
300            android:exported="false"
300-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
301            android:permission="android.permission.BIND_JOB_SERVICE" />
301-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82be8d4317586a7bc8fcc2f106bb8bd7\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
302
303        <provider
303-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
304            android:name="com.google.firebase.provider.FirebaseInitProvider"
304-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
305            android:authorities="com.Rages.itatiexplore.firebaseinitprovider"
305-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
306            android:directBootAware="true"
306-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
307            android:exported="false"
307-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
308            android:initOrder="100" />
308-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f50c1abcdc824390e6cdeec1d79e4fe\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
309
310        <activity
310-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5409d61ae460dbbf7f5d5450a39e324f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
311            android:name="com.google.android.gms.common.api.GoogleApiActivity"
311-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5409d61ae460dbbf7f5d5450a39e324f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
312            android:exported="false"
312-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5409d61ae460dbbf7f5d5450a39e324f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
313            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
313-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5409d61ae460dbbf7f5d5450a39e324f\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
314
315        <meta-data
315-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94a72bf5ecd12be9995f18ede9759a3\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
316            android:name="com.google.android.gms.version"
316-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94a72bf5ecd12be9995f18ede9759a3\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
317            android:value="@integer/google_play_services_version" />
317-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94a72bf5ecd12be9995f18ede9759a3\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
318
319        <provider
319-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
320            android:name="androidx.startup.InitializationProvider"
320-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
321            android:authorities="com.Rages.itatiexplore.androidx-startup"
321-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
322            android:exported="false" >
322-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
323            <meta-data
323-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
324                android:name="androidx.emoji2.text.EmojiCompatInitializer"
324-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
325                android:value="androidx.startup" />
325-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ff32551edaf6ec2f411e1440bba0f42\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
326            <meta-data
326-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d637702b3c0d46ac25bf3d045c031ce\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
327                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
327-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d637702b3c0d46ac25bf3d045c031ce\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
328                android:value="androidx.startup" />
328-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d637702b3c0d46ac25bf3d045c031ce\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
329            <meta-data
329-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
330                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
330-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
331                android:value="androidx.startup" />
331-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
332        </provider>
333
334        <uses-library
334-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ba625b679e01fe49419e1031df7fb05\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
335            android:name="android.ext.adservices"
335-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ba625b679e01fe49419e1031df7fb05\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
336            android:required="false" />
336-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ba625b679e01fe49419e1031df7fb05\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
337        <uses-library
337-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb469e7ac19447076989828dd9e3d56\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
338            android:name="androidx.window.extensions"
338-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb469e7ac19447076989828dd9e3d56\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
339            android:required="false" />
339-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb469e7ac19447076989828dd9e3d56\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
340        <uses-library
340-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb469e7ac19447076989828dd9e3d56\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
341            android:name="androidx.window.sidecar"
341-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb469e7ac19447076989828dd9e3d56\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
342            android:required="false" />
342-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb469e7ac19447076989828dd9e3d56\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
343
344        <service
344-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8719d683080e3508cf30cd31bc3b12c2\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
345            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
345-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8719d683080e3508cf30cd31bc3b12c2\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
346            android:exported="false" >
346-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8719d683080e3508cf30cd31bc3b12c2\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
347            <meta-data
347-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8719d683080e3508cf30cd31bc3b12c2\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
348                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
348-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8719d683080e3508cf30cd31bc3b12c2\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
349                android:value="cct" />
349-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8719d683080e3508cf30cd31bc3b12c2\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
350        </service>
351        <service
351-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
352            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
352-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
353            android:exported="false"
353-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
354            android:permission="android.permission.BIND_JOB_SERVICE" >
354-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
355        </service>
356
357        <receiver
357-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
358            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
358-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
359            android:exported="false" />
359-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da608812c82a9e9be5d8fb5232f3422f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
360        <receiver
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
361            android:name="androidx.profileinstaller.ProfileInstallReceiver"
361-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
362            android:directBootAware="false"
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
363            android:enabled="true"
363-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
364            android:exported="true"
364-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
365            android:permission="android.permission.DUMP" >
365-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
367                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
370                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
371            </intent-filter>
372            <intent-filter>
372-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
373                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
374            </intent-filter>
375            <intent-filter>
375-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
376                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
376-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
376-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41cc1979acaf33ae183601033be0360d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
377            </intent-filter>
378        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
379        <activity
379-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd813311b411c240d9501a99e4cb2505\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
380            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
380-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd813311b411c240d9501a99e4cb2505\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
381            android:exported="false"
381-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd813311b411c240d9501a99e4cb2505\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
382            android:stateNotNeeded="true"
382-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd813311b411c240d9501a99e4cb2505\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
383            android:theme="@style/Theme.PlayCore.Transparent" />
383-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd813311b411c240d9501a99e4cb2505\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
384    </application>
385
386</manifest>
