<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <array name="com_google_android_gms_fonts_certs">
        <item>@array/com_google_android_gms_fonts_certs_dev</item>
        <item>@array/com_google_android_gms_fonts_certs_prod</item>
    </array>
    <string-array name="com_google_android_gms_fonts_certs_dev">
        <item>
            MIIEqDCCA5CgAwIBAgIJANWFuGx90071MA0GCSqGSIb3DQEBBAUAMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTAeFw0wODA0MTUyMzM2NTZaFw0zNTA5MDEyMzM2NTZaMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBANbOLggKv+IxTdGNs8/TGFy0PTP6DHThvbbR24kT9ixcOd9W+EaBPWW+wPPKQmsHxajtWjmQwWfna8mZuSeJS48LIgAZlKkpFeVyxW0qMBujb8X8ETrWy550NaFtI6t9+u7hZeTfHwqNvacKhp1RbE6dBRGWynwMVX8XW8N1+UjFaq6GCJukT4qmpN2afb8sCjUigq0GuMwYXrFVee74bQgLHWGJwPmvmLHC69EH6kWr22ijx4OKXlSIx2xT1AsSHee70w5iDBiK4aph27yH3TxkXy9V89TDdexAcKk/cVHYNnDBapcavl7y0RiQ4biu8ymM8Ga/nmzhRKya6G0cGw8CAQOjgfwwgfkwHQYDVR0OBBYEFI0cxb6VTEM8YYY6FbBMvAPyT+CyMIHJBgNVHSMEgcEwgb6AFI0cxb6VTEM8YYY6FbBMvAPyT+CyoYGapIGXMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbYIJANWFuGx90071MAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQEEBQADggEBABnTDPEF+3iSP0wNfdIjIz1AlnrPzgAIHVvXxunW7SBrDhEglQZBbKJEk5kT0mtKoOD1JMrSu1xuTKEBahWRbqHsXclaXjoBADb0kkjVEJu/Lh5hgYZnOjvlba8Ld7HCKePCVePoTJBdI4fvugnL8TsgK05aIskyY0hKI9L8KfqfGTl1lzOv2KoWD0KWwtAWPoGChZxmQ+nBli+gwYMzM1vAkP+aayLe0a1EQimlOalO762r0GXO0ks+UeXde2Z4e+8S/pf7pITEI/tP+MxJTALw9QUWEv9lKTk+jkbqxbsh8nfBUapfKqYn0eidpwq2AzVp3juYl7//fKnaPhJD9gs=
        </item>
    </string-array>
    <string-array name="com_google_android_gms_fonts_certs_prod">
        <item>
            MIIEQzCCAyugAwIBAgIJAMLgh0ZkSjCNMA0GCSqGSIb3DQEBBAUAMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDAeFw0wODA4MjEyMzEzMzRaFw0zNjAxMDcyMzEzMzRaMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBAKtWLgDYO6IIrgqWbxJOKdoR8qtW0I9Y4sypEwPpt1TTcvZApxsdyxMJZ2JORland2qSGT2y5b+3JKkedxiLDmpHpDsz2WCbdxgxRczfey5YZnTJ4VZbH0xqWVW/8lGmPav5xVwnIiJS6HXk+BVKZF+JcWjAsb/GEuq/eFdpuzSqeYTcfi6idkyugwfYwXFU1+5fZKUaRKYCwkkFQVfcAs1fXA5V+++FGfvjJ/CxURaSxaBvGdGDhfXE28LWuT9ozCl5xw4Yq5OGazvV24mZVSoOO0yZ31j7kYvtwYK6NeADwbSxDdJEqO4k//0zOHKrUiGYXtqw/A0LFFtqoZKFjnkCAQOjgdkwgdYwHQYDVR0OBBYEFMd9jMIhF1Ylmn/Tgt9r45jk14alMIGmBgNVHSMEgZ4wgZuAFMd9jMIhF1Ylmn/Tgt9r45jk14aloXikdjB0MQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEUMBIGA1UEChMLR29vZ2xlIEluYy4xEDAOBgNVBAsTB0FuZHJvaWQxEDAOBgNVBAMTB0FuZHJvaWSCCQDC4IdGZEowjTAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBBAUAA4IBAQBt0lLO74UwLDYKqs6Tm8/yzKkEu116FmH4rkaymUIE0P9KaMftGlMexFlaYjzmB2OxZyl6euNXEsQH8gjwyxCUKRJNexBiGcCEyj6z+a1fuHHvkiaai+KL8W1EyNmgjmyy8AW7P+LLlkR+ho5zEHatRbM/YAnqGcFh5iZBqpknHf1SKMXFh4dd239FJ1jWYfbMDMy3NS5CTMQ2XFI1MvcyUTdZPErjQfTbQe3aDQsQcafEQPD+nqActifKZ0Np0IS9L9kR/wbNvyz6ENwPiTrjV2KRkEjH78ZMcUQXg0L3BYHJ3lc69Vs5Ddf9uUGGMYldX3WfMBEmh/9iFBDAaTCK
        </item>
    </string-array>
    <array name="preloaded_fonts" translatable="false">
        <item>@font/montserrat</item>
        <item>@font/montserrat_medium</item>
    </array>
    <color name="accent">#8b5cf6</color>
    <color name="accent_foreground">#ffffff</color>
    <color name="accent_light">#ede9fe</color>
    <color name="accent_variant">#ddd6fe</color>
    <color name="atraccion_color">#06b6d4</color>
    <color name="background">#fafbfc</color>
    <color name="background_light">#fafbfc</color>
    <color name="bano_color">#10b981</color>
    <color name="black">#000000</color>
    <color name="border">#e2e8f0</color>
    <color name="card">#ffffff</color>
    <color name="card_foreground">#1e293b</color>
    <color name="chart_1">#0ea5e9</color>
    <color name="chart_2">#06b6d4</color>
    <color name="chart_3">#8b5cf6</color>
    <color name="chart_4">#f97316</color>
    <color name="chart_5">#ef4444</color>
    <color name="chip_background">#FFFFFF</color>
    <color name="chip_selected_background">#E0E0E0</color>
    <color name="chip_stroke">#CCCCCC</color>
    <color name="chip_text">#333333</color>
    <color name="colorAccent">#0ea5e9</color>
    <color name="colorError">#ef4444</color>
    <color name="colorPrimary">#0ea5e9</color>
    <color name="colorPrimaryDark">#0284c7</color>
    <color name="danger">#ef4444</color>
    <color name="danger_light">#fecaca</color>
    <color name="dark_accent">#a855f7</color>
    <color name="dark_accent_foreground">#ffffff</color>
    <color name="dark_background">#0f172a</color>
    <color name="dark_border">#334155</color>
    <color name="dark_card">#1e293b</color>
    <color name="dark_card_foreground">#e2e8f0</color>
    <color name="dark_chart_1">#38bdf8</color>
    <color name="dark_chart_2">#22d3ee</color>
    <color name="dark_chart_3">#a855f7</color>
    <color name="dark_chart_4">#fb923c</color>
    <color name="dark_chart_5">#f87171</color>
    <color name="dark_destructive">#ef4444</color>
    <color name="dark_destructive_foreground">#ffffff</color>
    <color name="dark_foreground">#e2e8f0</color>
    <color name="dark_input">#334155</color>
    <color name="dark_muted">#1e293b</color>
    <color name="dark_muted_foreground">#64748b</color>
    <color name="dark_popover">#1e293b</color>
    <color name="dark_popover_foreground">#e2e8f0</color>
    <color name="dark_primary">#38bdf8</color>
    <color name="dark_primary_foreground">#0f172a</color>
    <color name="dark_ring">#38bdf8</color>
    <color name="dark_secondary">#334155</color>
    <color name="dark_secondary_foreground">#cbd5e1</color>
    <color name="dark_sidebar">#1e293b</color>
    <color name="dark_sidebar_accent">#c4b5fd</color>
    <color name="dark_sidebar_accent_foreground">#1e293b</color>
    <color name="dark_sidebar_border">#334155</color>
    <color name="dark_sidebar_foreground">#e2e8f0</color>
    <color name="dark_sidebar_primary">#38bdf8</color>
    <color name="dark_sidebar_primary_foreground">#0f172a</color>
    <color name="dark_sidebar_ring">#38bdf8</color>
    <color name="destructive">#ef4444</color>
    <color name="destructive_foreground">#ffffff</color>
    <color name="error">#ef4444</color>
    <color name="foreground">#1e293b</color>
    <color name="gold">#FFD700</color>
    <color name="gradient_center">#06b6d4</color>
    <color name="gradient_end">#8b5cf6</color>
    <color name="gradient_start">#0ea5e9</color>
    <color name="gray_100">#F5F5F5</color>
    <color name="gray_200">#EEEEEE</color>
    <color name="gray_300">#E0E0E0</color>
    <color name="gray_400">#BDBDBD</color>
    <color name="gray_50">#FAFAFA</color>
    <color name="gray_500">#9E9E9E</color>
    <color name="gray_600">#757575</color>
    <color name="gray_700">#616161</color>
    <color name="gray_800">#424242</color>
    <color name="gray_900">#212121</color>
    <color name="green_100">#C8E6C9</color>
    <color name="green_700">#388E3C</color>
    <color name="hotel_color">#0ea5e9</color>
    <color name="iglesia_color">#8b5cf6</color>
    <color name="input">#f1f5f9</color>
    <color name="muted">#f8fafc</color>
    <color name="muted_foreground">#64748b</color>
    <color name="on_background">#1e293b</color>
    <color name="on_primary">#FFFFFF</color>
    <color name="on_secondary">#475569</color>
    <color name="on_surface">#1e293b</color>
    <color name="on_surface_medium">#64748b</color>
    <color name="overlay">#80000000</color>
    <color name="popover">#ffffff</color>
    <color name="popover_foreground">#1e293b</color>
    <color name="primary">#0ea5e9</color>
    <color name="primary_foreground">#ffffff</color>
    <color name="primary_light">#38bdf8</color>
    <color name="primary_variant">#0ea5e9</color>
    <color name="purple_200">#c4b5fd</color>
    <color name="purple_500">#8b5cf6</color>
    <color name="purple_700">#7c3aed</color>
    <color name="red_100">#FFCDD2</color>
    <color name="red_700">#D32F2F</color>
    <color name="restaurante_color">#f97316</color>
    <color name="ring">#0ea5e9</color>
    <color name="ripple">#1F0ea5e9</color>
    <color name="scrim">#52000000</color>
    <color name="secondary">#f1f5f9</color>
    <color name="secondary_foreground">#475569</color>
    <color name="secondary_light">#f8fafc</color>
    <color name="secondary_variant">#f1f5f9</color>
    <color name="shadow">#1A000000</color>
    <color name="shadow_2xl">#40000000</color>
    <color name="shadow_2xs">#0D000000</color>
    <color name="shadow_lg">#1A000000</color>
    <color name="shadow_md">#1A000000</color>
    <color name="shadow_sm">#1A000000</color>
    <color name="shadow_xl">#1A000000</color>
    <color name="shadow_xs">#0D000000</color>
    <color name="sidebar">#f8fafc</color>
    <color name="sidebar_accent">#ddd6fe</color>
    <color name="sidebar_accent_foreground">#1e293b</color>
    <color name="sidebar_border">#e2e8f0</color>
    <color name="sidebar_foreground">#1e293b</color>
    <color name="sidebar_primary">#0ea5e9</color>
    <color name="sidebar_primary_foreground">#ffffff</color>
    <color name="sidebar_ring">#0ea5e9</color>
    <color name="success">#10b981</color>
    <color name="success_light">#86efac</color>
    <color name="surface">#FFFFFF</color>
    <color name="surface_variant">#f1f5f9</color>
    <color name="teal_200">#a7f3d0</color>
    <color name="teal_700">#0f766e</color>
    <color name="transporte_color">#6366f1</color>
    <color name="white">#FFFFFF</color>
    <item name="category_atraccion" type="id"/>
    <item name="category_bano" type="id"/>
    <item name="category_comida" type="id"/>
    <item name="category_hotel" type="id"/>
    <item name="category_iglesia" type="id"/>
    <item name="category_restaurante" type="id"/>
    <item name="category_todos" type="id"/>
    <item name="category_transporte" type="id"/>
    <string name="about">Acerca de</string>
    <string name="add">Añadir</string>
    <string name="add_event">Añadir evento</string>
    <string name="add_new_item">Agregar nuevo</string>
    <string name="all">Todos</string>
    <string name="all_events">Todos los eventos</string>
    <string name="app_name">Itati Explore</string>
    <string name="attractions_text">Basílica de Itatí: Imponente edificio de estilo neoclásico que alberga la imagen de la Virgen.\n\nPlaza principal: Espacio verde ideal para pasear y relajarse.\n\nMuseo de la Virgen: Exhibe objetos históricos relacionados con la devoción mariana.</string>
    <string name="attractions_title">Atracciones</string>
    <string name="back_button">Regresar</string>
    <string name="bathrooms">Baños</string>
    <string name="channel_description">Notificaciones de ItatiExplore</string>
    <string name="channel_name">Notificaciones Itati</string>
    <string name="churches">Iglesias</string>
    <string name="comments">Comentarios</string>
    <string name="contact_text">Oficina de Turismo: Avenida San Martín 1234\nTeléfono: +54 3786 42-1234\nEmail: <EMAIL>\nWeb: www.itati.gob.ar</string>
    <string name="contact_title">Contacto</string>
    <string name="dark_mode">Modo oscuro</string>
    <string name="default_web_client_id" translatable="false">1068770676881-ca311b809s8nm7t2mcua2fvmfnh85qug.apps.googleusercontent.com</string>
    <string name="donations">Donaciones</string>
    <string name="enter_as_guest">Enter as Guest</string>
    <string name="error_loading_fragment">Error al cargar el fragmento</string>
    <string name="event_image_description">Imagen del evento</string>
    <string name="events">Eventos</string>
    <string name="firebase_web_client_id" translatable="false">1068770676881-ca311b809s8nm7t2mcua2fvmfnh85qug.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">1068770676881</string>
    <string name="google_api_key" translatable="false">AIzaSyAALSonlAsNd61HzqkDCUDSAsF6Cuw3vUI</string>
    <string name="google_app_id" translatable="false">1:1068770676881:android:2d3b32613ea1ab299c6f30</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAALSonlAsNd61HzqkDCUDSAsF6Cuw3vUI</string>
    <string name="google_storage_bucket" translatable="false">itatiexplore.firebasestorage.app</string>
    <string name="help_feedback">Ayuda y comentarios</string>
    <string name="history_text">Fundada en el siglo XVII, Itatí tiene una rica historia vinculada a la presencia jesuítica en la región. En 1615 se encontró la imagen de la Virgen de Itatí, que desde entonces es venerada y ha convertido a la ciudad en un importante centro de peregrinación.</string>
    <string name="history_title">Historia</string>
    <string name="hotels">Hoteles</string>
    <string name="ic_add">Icono de añadir</string>
    <string name="ic_donate">Donar</string>
    <string name="ic_money">Dinero</string>
    <string name="ic_my_location">Mi ubicación</string>
    <string name="ic_person">Persona</string>
    <string name="ic_reply">Responder</string>
    <string name="ic_send">Enviar</string>
    <string name="ic_thumb_up">Me gusta</string>
    <string name="ic_zoom_in">Acercar</string>
    <string name="ic_zoom_out">Alejar</string>
    <string name="info">Información</string>
    <string name="itati_description">Itatí es una ciudad histórica ubicada en la provincia de Corrientes, Argentina. Es conocida por su basílica y por ser un importante centro religioso y turístico de la región.</string>
    <string name="itati_image_description">Imagen de Itatí</string>
    <string name="itati_title">Itatí, Corrientes</string>
    <string name="language">Idioma</string>
    <string name="map">Mapa</string>
    <string name="msg_token_fmt">FCM registration token: %s</string>
    <string name="not_signed_in">Not signed in</string>
    <string name="notifications">Notificaciones</string>
    <string name="profile">Perfil</string>
    <string name="project_id" translatable="false">itatiexplore</string>
    <string name="rate_app">Calificar app</string>
    <string name="restaurants">Restaurantes</string>
    <string name="search">Buscar</string>
    <string name="search_places">Buscar lugares</string>
    <string name="settings">Configuración</string>
    <string name="share">Compartir</string>
    <string name="technical_issues">We\'re experiencing some technical issues. Please try again later.</string>
    <string name="this_month">Este mes</string>
    <string name="this_week">Esta semana</string>
    <string name="today">Hoy</string>
    <string name="transport">Transporte</string>
    <string name="update_transport">Actualizar Transporte</string>
    <string name="view_data">Ver Datos</string>
    <string name="view_details">Cómo llegar</string>
    <string name="welcome_message">¡Bienvenido a Itatí Explore! Descubre la belleza y cultura de nuestra ciudad.</string>
    <string name="welcome_message_user">¡Bienvenido, %1$s!</string>
    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearance.App.TopRounded" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">24dp</item>
        <item name="cornerSizeTopRight">24dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearance.ItatiExplore.LargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>
    <style name="ShapeAppearance.ItatiExplore.MediumComponent" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>
    <style name="ShapeAppearance.ItatiExplore.SmallComponent" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>
    <style name="ShapeAppearance.ItatiExplore.TopRoundedCorners" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Button" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="android:textSize">96sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="android:textSize">60sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="android:textSize">48sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Headline4" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="android:textSize">34sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Headline5" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textSize">24sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Headline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">20sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Overline" parent="TextAppearance.MaterialComponents.Overline">
        <item name="android:textSize">10sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.SectionTitle" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/montserrat</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Subtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.ItatiExplore.Subtitle2" parent="TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Theme.ItatiExplore" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary</item>
        <item name="colorPrimaryContainer">@color/primary</item>
        <item name="colorOnPrimary">@color/primary_foreground</item>
        
        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary</item>
        <item name="colorSecondaryContainer">@color/secondary</item>
        <item name="colorOnSecondary">@color/secondary_foreground</item>
        
        
        <item name="colorAccent">@color/accent</item>
        
        
        <item name="colorError">@color/destructive</item>
        <item name="colorOnError">@color/destructive_foreground</item>
        
        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/foreground</item>
        
        
        <item name="colorSurface">@color/card</item>
        <item name="colorOnSurface">@color/card_foreground</item>
        <item name="colorSurfaceVariant">@color/muted</item>
        <item name="colorOnSurfaceVariant">@color/muted_foreground</item>
        
        
        <item name="android:statusBarColor">@color/card</item>
        <item name="android:navigationBarColor">@color/card</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="23">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">true</item>
        
        
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.ItatiExplore.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.ItatiExplore.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.ItatiExplore.LargeComponent</item>
        
        
        <item name="textAppearanceHeadline1">@style/TextAppearance.ItatiExplore.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.ItatiExplore.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.ItatiExplore.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.ItatiExplore.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.ItatiExplore.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.ItatiExplore.Headline6</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.ItatiExplore.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.ItatiExplore.Subtitle2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.ItatiExplore.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.ItatiExplore.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.ItatiExplore.Caption</item>
        <item name="textAppearanceButton">@style/TextAppearance.ItatiExplore.Button</item>
        <item name="textAppearanceOverline">@style/TextAppearance.ItatiExplore.Overline</item>
        
        
        <item name="materialButtonStyle">@style/Widget.ItatiExplore.Button</item>
        <item name="materialCardViewStyle">@style/Widget.ItatiExplore.CardView</item>
        <item name="bottomNavigationStyle">@style/Widget.ItatiExplore.BottomNavigation</item>
        <item name="toolbarStyle">@style/Widget.ItatiExplore.Toolbar</item>
        <item name="tabStyle">@style/Widget.ItatiExplore.TabLayout</item>
        <item name="chipStyle">@style/Widget.ItatiExplore.Chip</item>
        <item name="textInputStyle">@style/Widget.ItatiExplore.TextInputLayout</item>
    </style>
    <style name="Widget.ItatiExplore.BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
        <item name="itemRippleColor">@color/ripple</item>
        <item name="backgroundTint">@color/surface</item>
        <item name="elevation">8dp</item>
    </style>
    <style name="Widget.ItatiExplore.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">28dp</item>
        <item name="android:paddingEnd">28dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="android:fontFamily">@font/montserrat_medium</item>
        <item name="android:elevation">4dp</item>
    </style>
    <style name="Widget.ItatiExplore.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">28dp</item>
        <item name="android:paddingEnd">28dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:fontFamily">@font/montserrat_medium</item>
    </style>
    <style name="Widget.ItatiExplore.Button.TextButton" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/primary</item>
        <item name="rippleColor">@color/ripple</item>
    </style>
    <style name="Widget.ItatiExplore.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardElevation">8dp</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="rippleColor">@color/ripple</item>
        <item name="strokeColor">@color/border</item>
        <item name="strokeWidth">1dp</item>
    </style>
    <style name="Widget.ItatiExplore.Chip" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="chipBackgroundColor">@color/surface_variant</item>
        <item name="chipStrokeColor">@color/primary</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipIconTint">@color/primary</item>
        <item name="rippleColor">@color/ripple</item>
        <item name="checkedIconVisible">false</item>
    </style>
    <style name="Widget.ItatiExplore.Chip.Filter" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="android:textAppearance">@style/TextAppearance.ItatiExplore.Body2</item>
        <item name="android:fontFamily">@font/montserrat_medium</item>
        <item name="chipBackgroundColor">@drawable/modern_chip_background</item>
        <item name="chipStrokeColor">@color/border</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="android:textColor">@color/chip_text_color</item>
        <item name="rippleColor">@color/ripple</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipCornerRadius">20dp</item>
        <item name="chipMinHeight">40dp</item>
    </style>
    <style name="Widget.ItatiExplore.TabLayout" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabTextColor">@color/on_surface_medium</item>
        <item name="tabSelectedTextColor">@color/primary</item>
        <item name="tabIndicatorColor">@color/primary</item>
        <item name="tabIndicatorHeight">3dp</item>
        <item name="tabRippleColor">@color/ripple</item>
    </style>
    <style name="Widget.ItatiExplore.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="android:textColorHint">@color/on_surface_medium</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
    </style>
    <style name="Widget.ItatiExplore.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/on_primary</item>
        <item name="subtitleTextColor">@color/on_primary</item>
        <item name="colorControlNormal">@color/on_primary</item>
    </style>
</resources>