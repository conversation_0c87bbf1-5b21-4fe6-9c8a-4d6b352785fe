<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.EventsFragment">

    <!-- <PERSON><PERSON><PERSON><PERSON> con título y logo -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/header_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="0dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/title_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/primary"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingBottom="16dp">

                    <!-- Título con logo y botones -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="16dp"
                        android:paddingTop="16dp"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="8dp"
                            android:src="@android:drawable/ic_menu_my_calendar"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/events_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Eventos"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textStyle="bold" />

                        <!-- Botón de búsqueda -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/search_button"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton.Icon"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="8dp"
                            android:contentDescription="Buscar eventos"
                            android:insetLeft="0dp"
                            android:insetTop="0dp"
                            android:insetRight="0dp"
                            android:insetBottom="0dp"
                            app:cornerRadius="20dp"
                            app:icon="@android:drawable/ic_menu_search"
                            app:iconGravity="textStart"
                            app:iconPadding="0dp"
                            app:iconSize="20dp"
                            app:iconTint="@color/white"
                            app:strokeColor="@color/white"
                            app:strokeWidth="1dp" />

                        <!-- Botón de filtro -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/filter_button"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton.Icon"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:contentDescription="Filtrar eventos"
                            android:insetLeft="0dp"
                            android:insetTop="0dp"
                            android:insetRight="0dp"
                            android:insetBottom="0dp"
                            app:cornerRadius="20dp"
                            app:icon="@android:drawable/ic_menu_sort_by_size"
                            app:iconGravity="textStart"
                            app:iconPadding="0dp"
                            app:iconSize="20dp"
                            app:iconTint="@color/white"
                            app:strokeColor="@color/white"
                            app:strokeWidth="1dp" />
                    </LinearLayout>

                    <!-- Subtítulo -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Descubre actividades y celebraciones en Itatí"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:paddingHorizontal="16dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Panel de búsqueda (inicialmente oculto) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/search_panel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/header_card">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/search_layout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:hint="Buscar eventos"
                    app:endIconMode="clear_text"
                    app:startIconDrawable="@android:drawable/ic_menu_search"
                    app:startIconTint="@color/primary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/search_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:maxLines="1" />
                </com.google.android.material.textfield.TextInputLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Panel de filtros (inicialmente oculto) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/filter_panel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/search_panel">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat"
                        android:text="Filtrar por:"
                        android:textColor="@color/primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/filter_chip_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        app:chipSpacingHorizontal="8dp"
                        app:chipSpacingVertical="8dp"
                        app:singleSelection="true">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_all"
                            style="@style/Widget.ItatiExplore.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            android:text="@string/all_events" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_today"
                            style="@style/Widget.ItatiExplore.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/today" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_week"
                            style="@style/Widget.ItatiExplore.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/this_week" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_month"
                            style="@style/Widget.ItatiExplore.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/this_month" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_religious"
                            style="@style/Widget.ItatiExplore.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Religiosos" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_cultural"
                            style="@style/Widget.ItatiExplore.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Culturales" />
                    </com.google.android.material.chip.ChipGroup>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Contenido principal con scroll -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Sección de eventos destacados -->
            <LinearLayout
                android:id="@+id/featured_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="@font/montserrat"
                    android:text="Destacados"
                    android:textColor="@color/on_background"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/featured_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_featured_event" />
            </LinearLayout>

            <!-- Sección de próximos eventos -->
            <LinearLayout
                android:id="@+id/upcoming_events_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="16dp"
                android:paddingBottom="80dp">

                <TextView
                    android:id="@+id/upcoming_events_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="@font/montserrat"
                    android:text="Próximos Eventos"
                    android:textColor="@color/on_background"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                    
                <!-- Mensaje cuando no hay eventos que coincidan con el filtro -->
                <TextView
                    android:id="@+id/no_events_message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:fontFamily="@font/montserrat"
                    android:text="Intenta con otros filtros o vuelve más tarde"
                    android:textAlignment="center"
                    android:textColor="@color/on_surface_medium"
                    android:textSize="16sp"
                    android:visibility="gone" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingBottom="16dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_event" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Indicador de carga -->
    <FrameLayout
        android:id="@+id/loading_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="24dp">

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:indicatorColor="@color/primary"
                    app:indicatorSize="56dp"
                    app:trackThickness="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/montserrat"
                    android:text="Cargando eventos..."
                    android:textColor="@color/on_surface"
                    android:textSize="16sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </FrameLayout>

    <!-- Vista vacía con ilustración -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/empty_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:id="@+id/empty_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:alpha="0.7"
                android:contentDescription="No hay eventos"
                android:src="@android:drawable/ic_menu_my_calendar" />

            <TextView
                android:id="@+id/empty_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/montserrat"
                android:text="No hay eventos disponibles"
                android:textColor="@color/on_background"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/empty_secondary_text"
                android:layout_width="280dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/montserrat"
                android:text="Intenta con otros filtros o vuelve más tarde para ver nuevos eventos"
                android:textAlignment="center"
                android:textColor="@color/on_surface_medium"
                android:textSize="16sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_refresh"
                style="@style/Widget.MaterialComponents.Button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/montserrat"
                android:text="Actualizar"
                android:textColor="@color/white"
                app:backgroundTint="@color/primary"
                app:cornerRadius="24dp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>