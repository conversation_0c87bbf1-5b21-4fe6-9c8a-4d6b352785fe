{"logs": [{"outputFile": "com.Rages.itatiexplore.app-mergeReleaseResources-58:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5409d61ae460dbbf7f5d5450a39e324f\\transformed\\play-services-base-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4068,4174,4330,4456,4566,4720,4847,4959,5191,5340,5447,5607,5734,5883,6025,6093,6158", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "4169,4325,4451,4561,4715,4842,4954,5056,5335,5442,5602,5729,5878,6020,6088,6153,6233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73313f5160b05cc046f11947ceee314d\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "62,65,129,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6238,6486,11729,11889,12229,12398,12484", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "6304,6568,11804,12036,12393,12479,12561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d3278740ff7f0b8c5a39439b87b9fdb4\\transformed\\browser-1.4.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "63,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6309,6665,6765,6878", "endColumns": "110,99,112,97", "endOffsets": "6415,6760,6873,6971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f94c6f8749721534875926cd062b186\\transformed\\navigation-ui-2.7.7\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "11499,11606", "endColumns": "106,122", "endOffsets": "11601,11724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\58d1699a96be4f829e35b4b9d6e21979\\transformed\\credentials-1.3.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,119", "endOffsets": "161,281"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3099,3210", "endColumns": "110,119", "endOffsets": "3205,3325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2ba638d762ff4542c3e65fea48f8e860\\transformed\\core-1.9.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "133", "startColumns": "4", "startOffsets": "12128", "endColumns": "100", "endOffsets": "12224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\041807292bbdd7c1d354fe5fd3376c8c\\transformed\\material-1.11.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1125,1217,1285,1348,1451,1511,1577,1633,1704,1764,1818,1930,1987,2048,2102,2178,2303,2389,2472,2610,2691,2774,2905,2993,3071,3125,3181,3247,3321,3399,3488,3570,3645,3721,3796,3867,3974,4064,4137,4229,4325,4397,4473,4569,4622,4704,4771,4858,4945,5007,5071,5134,5203,5308,5418,5514,5622,5680,5740", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "316,392,468,548,655,748,842,973,1054,1120,1212,1280,1343,1446,1506,1572,1628,1699,1759,1813,1925,1982,2043,2097,2173,2298,2384,2467,2605,2686,2769,2900,2988,3066,3120,3176,3242,3316,3394,3483,3565,3640,3716,3791,3862,3969,4059,4132,4224,4320,4392,4468,4564,4617,4699,4766,4853,4940,5002,5066,5129,5198,5303,5413,5509,5617,5675,5735,5815"}, "to": {"startLines": "2,36,37,38,39,40,41,42,43,64,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3330,3406,3482,3562,3669,3762,3856,3987,6420,6573,6976,7044,7107,7210,7270,7336,7392,7463,7523,7577,7689,7746,7807,7861,7937,8062,8148,8231,8369,8450,8533,8664,8752,8830,8884,8940,9006,9080,9158,9247,9329,9404,9480,9555,9626,9733,9823,9896,9988,10084,10156,10232,10328,10381,10463,10530,10617,10704,10766,10830,10893,10962,11067,11177,11273,11381,11439,11809", "endLines": "6,36,37,38,39,40,41,42,43,64,66,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,130", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "366,3401,3477,3557,3664,3757,3851,3982,4063,6481,6660,7039,7102,7205,7265,7331,7387,7458,7518,7572,7684,7741,7802,7856,7932,8057,8143,8226,8364,8445,8528,8659,8747,8825,8879,8935,9001,9075,9153,9242,9324,9399,9475,9550,9621,9728,9818,9891,9983,10079,10151,10227,10323,10376,10458,10525,10612,10699,10761,10825,10888,10957,11062,11172,11268,11376,11434,11494,11884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b94a72bf5ecd12be9995f18ede9759a3\\transformed\\play-services-basement-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5061", "endColumns": "129", "endOffsets": "5186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eee50e8311d47e456960a4e70d71cc7f\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,12041", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,12123"}}]}]}