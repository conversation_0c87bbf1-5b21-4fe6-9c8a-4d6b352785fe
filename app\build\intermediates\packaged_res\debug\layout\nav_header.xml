<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="220dp"
    android:background="@drawable/modern_gradient_header"
    android:padding="20dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/nav_header_logo"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:contentDescription="Logo de Itatí Explore"
        android:padding="4dp"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintBottom_toTopOf="@+id/nav_header_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:shapeAppearanceOverlay="@style/CircleImageView"
        app:strokeColor="@color/white"
        app:strokeWidth="2dp" />

    <TextView
        android:id="@+id/nav_header_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/montserrat"
        android:text="Itatí Explore"
        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
        android:textColor="@color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/nav_header_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nav_header_logo" />

    <TextView
        android:id="@+id/nav_header_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/montserrat"
        android:text="Descubre la magia de Itatí"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nav_header_title" />

    <!-- Firma Rages -->
    <TextView
        android:id="@+id/nav_header_signature"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:fontFamily="@font/montserrat"
        android:text="Rages"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>