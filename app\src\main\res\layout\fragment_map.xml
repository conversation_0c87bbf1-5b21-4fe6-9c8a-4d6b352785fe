<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".fragments.MapFragment">

    <!-- <PERSON><PERSON><PERSON><PERSON> con título y logo -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/header_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="0dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/title_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/modern_gradient_header"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingBottom="20dp"
            android:paddingTop="8dp">

            <!-- Título con logo -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_map"
                    app:tint="@color/white" />

                <TextView
                    android:id="@+id/map_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Mapa de Itatí"
                    android:textColor="@android:color/white"
                    android:textSize="20sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="bold"
                    android:fontFamily="@font/montserrat" />
            </LinearLayout>

            <!-- Subtítulo -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Explora los lugares de interés"
                android:textColor="@color/white"
                android:textSize="15sp"
                android:paddingHorizontal="16dp"
                android:fontFamily="@font/montserrat"
                android:alpha="0.9"/>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Mapa OSMDroid -->
    <org.osmdroid.views.MapView
        android:id="@+id/osmmap"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/header_card"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Card para filtros de categorías -->
    <androidx.cardview.widget.CardView
        android:id="@+id/filter_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/header_card"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/modern_card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="4dp">

            <!-- Chips de categoría -->
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none"
                android:paddingVertical="4dp"
                android:paddingHorizontal="8dp"
                android:clipToPadding="false">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/category_chip_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:selectionRequired="true"
                    app:chipSpacingHorizontal="12dp"
                    app:chipSpacingVertical="8dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_todos"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Todos"
                        android:tag="todos"
                        android:checked="true"
                        style="@style/Widget.ItatiExplore.Chip.Filter" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_hoteles"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hoteles"
                        android:tag="hotel"
                        style="@style/Widget.ItatiExplore.Chip.Filter" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_restaurantes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Restaurantes"
                        android:tag="restaurante"
                        style="@style/Widget.ItatiExplore.Chip.Filter" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_iglesias"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Iglesias"
                        android:tag="iglesia"
                        style="@style/Widget.ItatiExplore.Chip.Filter" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_atracciones"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Atracciones"
                        android:tag="atraccion"
                        style="@style/Widget.ItatiExplore.Chip.Filter" />
                </com.google.android.material.chip.ChipGroup>
            </HorizontalScrollView>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Controles de mapa - Solo con botón de Mi Ubicación -->
    <androidx.cardview.widget.CardView
        android:id="@+id/map_controls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_my_location"
                android:layout_width="50dp"
                android:layout_height="50dp"
                app:icon="@drawable/baseline_my_location_24"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="28dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:padding="8dp"
                app:cornerRadius="8dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Card de información de lugar (inicialmente oculta) -->
    <androidx.cardview.widget.CardView
        android:id="@+id/place_info_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Categoría -->
            <TextView
                android:id="@+id/place_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Restaurante"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:paddingHorizontal="10dp"
                android:paddingVertical="4dp"
                android:background="@drawable/category_pill_background"
                android:layout_margin="12dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <!-- Botón de cerrar -->
            <ImageButton
                android:id="@+id/btn_close_card"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_margin="12dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:contentDescription="Cerrar" />

            <!-- Imagen del lugar -->
            <ImageView
                android:id="@+id/place_image"
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:scaleType="centerCrop"
                android:src="@drawable/itati_placeholder"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:contentDescription="Imagen del lugar" />

            <!-- Título -->
            <TextView
                android:id="@+id/place_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Restaurante El Paraná"
                android:textStyle="bold"
                android:textSize="18sp"
                android:paddingHorizontal="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="4dp"
                app:layout_constraintTop_toBottomOf="@id/place_image" />

            <!-- Descripción -->
            <TextView
                android:id="@+id/place_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Descripción del lugar..."
                android:textSize="14sp"
                android:maxLines="2"
                android:ellipsize="end"
                android:paddingHorizontal="16dp"
                android:paddingBottom="8dp"
                app:layout_constraintTop_toBottomOf="@id/place_title" />

            <!-- Acciones -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                app:layout_constraintTop_toBottomOf="@id/place_description"
                app:layout_constraintBottom_toBottomOf="parent">

                <Button
                    android:id="@+id/btn_share"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Compartir"
                    android:layout_marginEnd="8dp"
                    android:drawableStart="@android:drawable/ic_menu_share"
                    android:drawablePadding="4dp"
                    style="@style/Widget.MaterialComponents.Button.TextButton" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <Button
                    android:id="@+id/btn_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ver ubicación"
                    style="@style/Widget.MaterialComponents.Button" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <!-- Indicador de carga -->
    <ProgressBar
        android:id="@+id/map_loading_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>