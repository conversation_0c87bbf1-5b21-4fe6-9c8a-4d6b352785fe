plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.Rages.itatiexplore'
    compileSdk 34

    defaultConfig {
        applicationId "com.Rages.itatiexplore"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"
        
        // Enable multidex
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
        // Ignorar incompatibilidades de metadatos de Kotlin en dependencias Firebase
        freeCompilerArgs += [
            "-Xsuppress-version-warnings",
            "-Xskip-metadata-version-check"
        ]
    }
    
    // Configuración para evitar que los errores de Lint detengan la compilación
    lintOptions {
        abortOnError false
        // Ignorar errores relacionados con módulos Kotlin
        disable 'InvalidPackage', 'GradleDependency', 'KotlinModule'
    }
    
    // Add packaging options to handle duplicate file entries
    packagingOptions {
        resources {
            excludes += [
                '/META-INF/*.kotlin_module',
                '/META-INF/DEPENDENCIES',
                '/META-INF/LICENSE',
                '/META-INF/LICENSE.txt',
                '/META-INF/NOTICE',
                '/META-INF/NOTICE.txt',
                '/META-INF/licenses/**',
                'META-INF/AL2.0',
                'META-INF/LGPL2.1',
                'META-INF/*.version'
            ]
            pickFirsts += ['META-INF/gradle/incremental.annotation.processors']
        }
    }
}

dependencies {
    // Add multidex support
    implementation 'androidx.multidex:multidex:2.0.1'
    
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // Navigation Component
    implementation 'androidx.navigation:navigation-fragment:2.7.7'
    implementation 'androidx.navigation:navigation-ui:2.7.7'
    
    // Preference
    implementation 'androidx.preference:preference:1.2.1'
    
    // Osmdroid (OpenStreetMap for Android) - Removida funcionalidad de rutas
    implementation 'org.osmdroid:osmdroid-android:6.1.16'
    
    // Mapbox para direcciones y navegación
    implementation 'com.mapbox.mapboxsdk:mapbox-sdk-services:5.8.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    
    // Lifecycle components
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.6.2'
    
    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    
    // CardView
    implementation 'androidx.cardview:cardview:1.0.0'
    
    // ViewPager2
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    
    // CircleImageView para fotos de perfil redondeadas
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    
    // Glide para cargar imágenes
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    
    // Firebase
    implementation platform('com.google.firebase:firebase-bom:33.12.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-config'
    
    // Google Sign In
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    
    // Credential Manager
    implementation 'androidx.credentials:credentials:1.3.0'
    implementation 'androidx.credentials:credentials-play-services-auth:1.3.0'
    implementation 'com.google.android.libraries.identity.googleid:googleid:1.1.1'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
} 