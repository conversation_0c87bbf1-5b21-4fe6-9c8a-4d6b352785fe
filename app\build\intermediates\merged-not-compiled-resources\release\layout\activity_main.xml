<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background"
    tools:context=".MainActivity">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/scrim" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/logo_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.15">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <ImageView
                    android:id="@+id/appLogo"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/app_name"
                    android:src="@android:drawable/sym_def_app_icon" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/montserrat_medium"
                    android:gravity="center"
                    android:text="@string/app_name"
                    android:textAppearance="?attr/textAppearanceHeadline5"
                    android:textColor="@color/primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/montserrat"
                    android:gravity="center"
                    android:text="@string/welcome_message"
                    android:textAppearance="?attr/textAppearanceSubtitle1"
                    android:textColor="@color/on_surface_medium" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/login_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/logo_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:id="@+id/status_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/montserrat_medium"
                    android:gravity="center"
                    android:text="@string/not_signed_in"
                    android:textAppearance="?attr/textAppearanceSubtitle2"
                    android:textColor="@color/on_surface" />

                <com.google.android.gms.common.SignInButton
                    android:id="@+id/sign_in_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/go_to_app_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/montserrat_medium"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/enter_as_guest"
                    android:textAllCaps="false"
                    app:cornerRadius="8dp" />
                    
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/sign_out_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/montserrat_medium"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="Cerrar sesión"
                    android:textAllCaps="false"
                    android:visibility="gone"
                    app:cornerRadius="8dp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/admin_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/login_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/montserrat_medium"
                    android:gravity="center"
                    android:text="Administración"
                    android:textAppearance="?attr/textAppearanceSubtitle2"
                    android:textColor="@color/on_surface" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/montserrat_medium"
                    android:gravity="center"
                    android:text="Cargar datos de transporte"
                    android:textAppearance="?attr/textAppearanceCaption"
                    android:textColor="@color/on_surface_medium" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/loadMinibusButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/montserrat"
                        android:text="Minibus"
                        android:textAllCaps="false"
                        android:textSize="12sp"
                        app:cornerRadius="8dp"
                        app:icon="@drawable/ic_add_transport"
                        app:iconGravity="textStart"
                        app:iconSize="18dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/loadBusButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/montserrat"
                        android:text="Colectivo"
                        android:textAllCaps="false"
                        android:textSize="12sp"
                        app:cornerRadius="8dp"
                        app:icon="@drawable/ic_add_transport"
                        app:iconGravity="textStart"
                        app:iconSize="18dp" />
                </LinearLayout>
                
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/loadTaxiButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/montserrat"
                        android:text="Taxi"
                        android:textAllCaps="false"
                        android:textSize="12sp"
                        app:cornerRadius="8dp"
                        app:icon="@drawable/ic_add_transport"
                        app:iconGravity="textStart"
                        app:iconSize="18dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/viewDataButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/montserrat"
                        android:text="Ver Datos"
                        android:textAllCaps="false"
                        android:textSize="12sp"
                        app:cornerRadius="8dp"
                        app:icon="@android:drawable/ic_menu_view"
                        app:iconGravity="textStart"
                        app:iconSize="18dp" />
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/montserrat"
            android:text="© 2024 Itatí Explore"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>