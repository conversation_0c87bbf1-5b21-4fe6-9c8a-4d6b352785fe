<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="45"
                android:startColor="@color/primary"
                android:endColor="@color/accent"
                android:type="linear" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/secondary" />
            <corners android:radius="20dp" />
            <stroke 
                android:width="1dp" 
                android:color="@color/border" />
        </shape>
    </item>
</selector>
