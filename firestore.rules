rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Autenticación: verifica si el usuario está autenticado
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Verificación de administrador: comprueba si el usuario es administrador
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Reglas para la colección de usuarios
    match /users/{userId} {
      // Permitir lectura y escritura solo al propio usuario
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      // Permitir lectura a administradores
      allow read: if isAdmin();
    }
    
    // Reglas para la colección de marcadores (puntos de interés)
    match /marcadores/{markerID} {
      // Cualquiera puede leer los marcadores
      allow read: if true;
      // Solo administradores pueden crear, actualizar o eliminar
      allow create, update, delete: if isAdmin();
    }
    
    // Reglas para la colección de categorías
    match /categorias/{categoryID} {
      // Cualquiera puede leer las categorías
      allow read: if true;
      // Solo administradores pueden modificar
      allow create, update, delete: if isAdmin();
    }
    
    // Reglas para la colección de reportes
    match /reportes/{reportId} {
      // Permitir crear reportes a usuarios autenticados
      allow create: if isAuthenticated();
      
      // Leer reportes: solo el usuario que reportó o administradores
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.reporterUserId || isAdmin());
      
      // Actualizar o borrar reportes: solo administradores
      allow update, delete: if isAdmin();
    }
    
    // Reglas para la colección de comentarios
    match /comentarios/{commentId} {
      // Todos pueden leer los comentarios
      allow read: if true;
      
      // Solo usuarios autenticados pueden crear comentarios
      allow create: if isAuthenticated() && 
                    request.resource.data.userId == request.auth.uid;
      
      // Solo el autor o un administrador puede actualizar o eliminar
      allow update, delete: if isAuthenticated() && 
        (request.auth.uid == resource.data.userId || isAdmin());
    }
    
    // Regla por defecto - denegar todo lo demás
    match /{document=**} {
      allow read, write: if false;
    }
  }
} 