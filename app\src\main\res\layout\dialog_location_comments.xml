<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white">

    <!-- <PERSON><PERSON><PERSON><PERSON> del diálogo -->
    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Comentarios del lugar"
        android:textAppearance="?attr/textAppearanceHeadline6"
        android:textColor="@color/primary"
        android:textStyle="bold"
        android:fontFamily="@font/montserrat"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginBottom="8dp" />
        
    <!-- Sub<PERSON><PERSON><PERSON><PERSON> con la ubicación -->
    <TextView
        android:id="@+id/dialog_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text=""
        android:textAppearance="?attr/textAppearanceSubtitle2"
        android:textColor="@color/gray_500"
        android:fontFamily="@font/montserrat"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginBottom="16dp" />

    <!-- RecyclerView para mostrar comentarios -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/dialog_comments_recycler"
        android:layout_width="match_parent"
        android:layout_height="350dp"
        android:background="@color/background_light"
        android:padding="8dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_comment" />

    <!-- Mensaje cuando no hay comentarios -->
    <TextView
        android:id="@+id/dialog_no_comments"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="No hay comentarios para este lugar"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="@color/gray_500"
        android:fontFamily="@font/montserrat"
        android:visibility="gone"
        android:padding="32dp" />

    <!-- Botón para cerrar el diálogo -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_close_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Cerrar"
        android:textColor="@android:color/white"
        android:fontFamily="@font/montserrat"
        app:backgroundTint="@color/primary"
        app:cornerRadius="8dp"
        app:icon="@android:drawable/ic_menu_close_clear_cancel"
        app:iconTint="@android:color/white" />

</LinearLayout> 