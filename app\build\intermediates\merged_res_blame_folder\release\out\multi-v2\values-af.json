{"logs": [{"outputFile": "com.Rages.itatiexplore.app-mergeReleaseResources-58:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5409d61ae460dbbf7f5d5450a39e324f\\transformed\\play-services-base-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3947,4054,4210,4336,4446,4596,4718,4839,5084,5250,5358,5515,5642,5781,5935,6001,6064", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "4049,4205,4331,4441,4591,4713,4834,4936,5245,5353,5510,5637,5776,5930,5996,6059,6138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d3278740ff7f0b8c5a39439b87b9fdb4\\transformed\\browser-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "62,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "6214,6559,6661,6774", "endColumns": "104,101,112,99", "endOffsets": "6314,6656,6769,6869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f94c6f8749721534875926cd062b186\\transformed\\navigation-ui-2.7.7\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,157", "endColumns": "101,116", "endOffsets": "152,269"}, "to": {"startLines": "126,127", "startColumns": "4,4", "startOffsets": "11294,11396", "endColumns": "101,116", "endOffsets": "11391,11508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b94a72bf5ecd12be9995f18ede9759a3\\transformed\\play-services-basement-18.5.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "4941", "endColumns": "142", "endOffsets": "5079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\73313f5160b05cc046f11947ceee314d\\transformed\\preference-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,345,484,653,732", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "171,259,340,479,648,727,804"}, "to": {"startLines": "61,64,128,130,133,134,135", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6143,6383,11513,11671,11992,12161,12240", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "6209,6466,11589,11805,12156,12235,12312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2ba638d762ff4542c3e65fea48f8e860\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "11891", "endColumns": "100", "endOffsets": "11987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\eee50e8311d47e456960a4e70d71cc7f\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,11810", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,11886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\58d1699a96be4f829e35b4b9d6e21979\\transformed\\credentials-1.3.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2996,3108", "endColumns": "111,121", "endOffsets": "3103,3225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\041807292bbdd7c1d354fe5fd3376c8c\\transformed\\material-1.11.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1055,1143,1209,1272,1358,1420,1481,1539,1605,1668,1723,1841,1898,1960,2015,2084,2203,2291,2374,2513,2596,2677,2805,2892,2969,3027,3078,3144,3213,3289,3375,3451,3525,3604,3677,3748,3851,3938,4009,4098,4188,4260,4335,4422,4473,4552,4619,4700,4784,4846,4910,4973,5043,5147,5250,5346,5446,5508,5563", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "269,350,430,508,603,691,791,905,986,1050,1138,1204,1267,1353,1415,1476,1534,1600,1663,1718,1836,1893,1955,2010,2079,2198,2286,2369,2508,2591,2672,2800,2887,2964,3022,3073,3139,3208,3284,3370,3446,3520,3599,3672,3743,3846,3933,4004,4093,4183,4255,4330,4417,4468,4547,4614,4695,4779,4841,4905,4968,5038,5142,5245,5341,5441,5503,5558,5635"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,63,65,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3230,3311,3391,3469,3564,3652,3752,3866,6319,6471,6874,6940,7003,7089,7151,7212,7270,7336,7399,7454,7572,7629,7691,7746,7815,7934,8022,8105,8244,8327,8408,8536,8623,8700,8758,8809,8875,8944,9020,9106,9182,9256,9335,9408,9479,9582,9669,9740,9829,9919,9991,10066,10153,10204,10283,10350,10431,10515,10577,10641,10704,10774,10878,10981,11077,11177,11239,11594", "endLines": "5,35,36,37,38,39,40,41,42,63,65,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,129", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,127,86,76,57,50,65,68,75,85,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76", "endOffsets": "319,3306,3386,3464,3559,3647,3747,3861,3942,6378,6554,6935,6998,7084,7146,7207,7265,7331,7394,7449,7567,7624,7686,7741,7810,7929,8017,8100,8239,8322,8403,8531,8618,8695,8753,8804,8870,8939,9015,9101,9177,9251,9330,9403,9474,9577,9664,9735,9824,9914,9986,10061,10148,10199,10278,10345,10426,10510,10572,10636,10699,10769,10873,10976,11072,11172,11234,11289,11666"}}]}]}