package com.Rages.itatiexplore;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.common.SignInButton;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.AuthCredential;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.auth.GoogleAuthProvider;
import com.google.firebase.firestore.DocumentReference;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;
import com.google.firebase.firestore.SetOptions;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.Rages.itatiexplore.firebase.CredentialManagerAuthHelper;
import com.Rages.itatiexplore.firebase.FirestoreManager;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private static final int RC_SIGN_IN = 9001;

    // Firebase components
    private FirebaseAuth mAuth;
    private GoogleSignInClient mGoogleSignInClient;
    private FirebaseRemoteConfig mFirebaseRemoteConfig;
    private FirebaseFirestore db;
    
    // Credential Manager auth helper
    private CredentialManagerAuthHelper mCredentialManagerAuthHelper;

    // UI components
    private SignInButton signInButton;
    private Button goToAppButton;
    private TextView statusTextView;
    private Button viewDataButton;
    private Button loadMinibusButton;
    private Button loadBusButton;
    private Button loadTaxiButton;
    private Button signOutButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        Log.d(TAG, "Iniciando MainActivity");

        // Initialize UI components
        signInButton = findViewById(R.id.sign_in_button);
        goToAppButton = findViewById(R.id.go_to_app_button);
        statusTextView = findViewById(R.id.status_text_view);
        signOutButton = findViewById(R.id.sign_out_button);
        viewDataButton = findViewById(R.id.viewDataButton);
        loadMinibusButton = findViewById(R.id.loadMinibusButton);
        loadBusButton = findViewById(R.id.loadBusButton);
        loadTaxiButton = findViewById(R.id.loadTaxiButton);
        
        // Initialize Firebase
        initializeFirebaseAuth();
        initializeFirestore();
        initializeRemoteConfig();
        
        Log.d(TAG, "Comprobando compatibilidad con Credential Manager");
        
        // Initialize the new Credential Manager Auth Helper (requires API level 26+)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            try {
                Log.d(TAG, "Inicializando Credential Manager para APIs 26+");
                
                // Intentar obtener el client ID de Google
                String clientId;
                try {
                    clientId = getString(R.string.default_web_client_id);
                    Log.d(TAG, "Client ID obtenido: " + (clientId != null ? (clientId.substring(0, Math.min(10, clientId.length())) + "...") : "null"));
                } catch (Exception e) {
                    Log.e(TAG, "Error al obtener default_web_client_id", e);
                    Toast.makeText(this, "Error al obtener ID de cliente", Toast.LENGTH_SHORT).show();
                    fallbackToTraditionalSignIn();
                    return;
                }
                
                // Verificar que el clientId no esté vacío
                if (clientId == null || clientId.trim().isEmpty()) {
                    Log.e(TAG, "El cliente ID de Google es nulo o vacío, usando autenticación tradicional");
                    Toast.makeText(this, "ID de cliente no configurado", Toast.LENGTH_SHORT).show();
                    fallbackToTraditionalSignIn();
                    return;
                }
                
                // Intentar crear el helper de Credential Manager
                try {
                    Log.d(TAG, "Creando instancia de CredentialManagerAuthHelper");
                    mCredentialManagerAuthHelper = new CredentialManagerAuthHelper(this, clientId, null);
                    Log.d(TAG, "CredentialManagerAuthHelper creado con éxito");
                } catch (Exception e) {
                    Log.e(TAG, "Error al crear CredentialManagerAuthHelper", e);
                    Toast.makeText(this, "Error al inicializar el gestor de credenciales", Toast.LENGTH_SHORT).show();
                    fallbackToTraditionalSignIn();
                    return;
                }
                
                // Enable sign-in button if initialization is successful
                if (signInButton != null) {
                    Log.d(TAG, "Configurando botón para autenticación con Credential Manager");
                    signInButton.setOnClickListener(v -> signInWithCredentialManager());
                    signInButton.setEnabled(true);
                } else {
                    Log.e(TAG, "El botón de inicio de sesión es nulo");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error inicializando Credential Manager Auth Helper: " + e.getMessage(), e);
                Toast.makeText(this, "Servicio de autenticación no disponible, usando método alternativo", Toast.LENGTH_SHORT).show();
                fallbackToTraditionalSignIn();
            }
        } else {
            // Device doesn't support Credential Manager, use traditional sign-in
            Log.d(TAG, "Dispositivo no compatible con Credential Manager (API < 26), usando autenticación tradicional");
            fallbackToTraditionalSignIn();
        }
        
        // Set up sign-out button
        if (signOutButton != null) {
            Log.d(TAG, "Configurando botón de cierre de sesión");
            signOutButton.setOnClickListener(v -> signOut());
        }

        // Botón para entrar a la app como invitado
        if (goToAppButton != null) {
            Log.d(TAG, "Configurando botón para entrar como invitado");
            goToAppButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // Mostrar diálogo para preguntar qué tipo de datos desea cargar
                    String[] options = {"Minibus", "Colectivo", "Taxi", "Todos", "Ninguno"};
                    
                    new android.app.AlertDialog.Builder(MainActivity.this)
                        .setTitle("Cargar Datos de Transporte")
                        .setItems(options, (dialog, which) -> {
                            switch (which) {
                                case 0: // Minibus
                                    Toast.makeText(MainActivity.this, "Cargando datos de minibus...", Toast.LENGTH_SHORT).show();
                                    loadMinibusData();
                                    new Handler().postDelayed(() -> launchMainApp(), 1500);
                                    break;
                                case 1: // Colectivo
                                    Toast.makeText(MainActivity.this, "Cargando datos de colectivo...", Toast.LENGTH_SHORT).show();
                                    loadBusData();
                                    new Handler().postDelayed(() -> launchMainApp(), 1500);
                                    break;
                                case 2: // Taxi
                                    Toast.makeText(MainActivity.this, "Cargando datos de taxi...", Toast.LENGTH_SHORT).show();
                                    loadTaxiData();
                                    new Handler().postDelayed(() -> launchMainApp(), 1500);
                                    break;
                                case 3: // Todos
                                    Toast.makeText(MainActivity.this, "Cargando todos los datos de transporte...", Toast.LENGTH_SHORT).show();
                                    loadTransportData();
                                    new Handler().postDelayed(() -> launchMainApp(), 2000);
                                    break;
                                case 4: // Ninguno
                                default:
                                    launchMainApp();
                                    break;
                            }
                        })
                        .setNegativeButton("Cancelar", (dialog, which) -> {
                            launchMainApp();
                        })
                        .show();
                }
            });
        }
        
        // Set up the View Data button
        if (viewDataButton != null) {
            Log.d(TAG, "Configurando botón para ver datos");
            viewDataButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    readPostData();
                }
            });
        }
        
        // Configurar botones para cargar datos de transporte
        if (loadMinibusButton != null) {
            loadMinibusButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // Mostrar diálogo para confirmar
                    new android.app.AlertDialog.Builder(MainActivity.this)
                        .setTitle("Cargar Datos de Minibus")
                        .setMessage("¿Desea cargar o actualizar los datos de minibus?")
                        .setPositiveButton("Sí, cargar", (dialog, which) -> {
                            Toast.makeText(MainActivity.this, "Cargando datos de minibus...", Toast.LENGTH_SHORT).show();
                            loadMinibusData();
                        })
                        .setNegativeButton("Cancelar", null)
                        .show();
                }
            });
        }
        
        loadBusButton = findViewById(R.id.loadBusButton);
        if (loadBusButton != null) {
            loadBusButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // Mostrar diálogo para confirmar
                    new android.app.AlertDialog.Builder(MainActivity.this)
                        .setTitle("Cargar Datos de Colectivo")
                        .setMessage("¿Desea cargar o actualizar los datos de colectivo?")
                        .setPositiveButton("Sí, cargar", (dialog, which) -> {
                            Toast.makeText(MainActivity.this, "Cargando datos de colectivo...", Toast.LENGTH_SHORT).show();
                            loadBusData();
                        })
                        .setNegativeButton("Cancelar", null)
                        .show();
                }
            });
        }
        
        loadTaxiButton = findViewById(R.id.loadTaxiButton);
        if (loadTaxiButton != null) {
            loadTaxiButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // Mostrar diálogo para confirmar
                    new android.app.AlertDialog.Builder(MainActivity.this)
                        .setTitle("Cargar Datos de Taxi")
                        .setMessage("¿Desea cargar o actualizar los datos de taxi?")
                        .setPositiveButton("Sí, cargar", (dialog, which) -> {
                            Toast.makeText(MainActivity.this, "Cargando datos de taxi...", Toast.LENGTH_SHORT).show();
                            loadTaxiData();
                        })
                        .setNegativeButton("Cancelar", null)
                        .show();
                }
            });
        }
        
        // Lanzar aplicación principal automáticamente después de un breve retraso
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                launchMainApp();
            }
        }, 1500); // 1.5 segundos de espera
    }
    
    /**
     * Sign in with the new Credential Manager
     */
    private void signInWithCredentialManager() {
        Log.d(TAG, "Iniciando proceso de signInWithCredentialManager");
        
        if (mCredentialManagerAuthHelper == null) {
            Log.e(TAG, "Credential Manager Auth Helper is not initialized");
            Toast.makeText(this, "Servicio de autenticación no disponible", Toast.LENGTH_SHORT).show();
            // Intenta el fallback a tradicional
            fallbackToTraditionalSignIn();
            return;
        }
        
        try {
            // Disable the sign-in button to prevent multiple clicks
            if (signInButton != null) {
                signInButton.setEnabled(false);
                Log.d(TAG, "Botón de inicio de sesión deshabilitado");
            } else {
                Log.w(TAG, "signInButton es nulo al intentar deshabilitarlo");
            }
            
            // Show a loading message
            updateStatusText("Iniciando sesión...");
            Log.d(TAG, "Estado actualizado a 'Iniciando sesión...'");
            
            // Verificar que estamos ejecutando esto en una actividad válida
            if (isFinishing()) {
                Log.e(TAG, "La actividad está finalizando, no se puede continuar con la autenticación");
                if (signInButton != null) {
                    signInButton.setEnabled(true);
                }
                return;
            }
            
            Log.d(TAG, "Llamando al método signIn del CredentialManagerAuthHelper");
            // Call the sign-in method from the helper
            mCredentialManagerAuthHelper.signIn(this, new CredentialManagerAuthHelper.AuthCallback() {
                @Override
                public void onSuccess(FirebaseUser user) {
                    Log.d(TAG, "Callback onSuccess recibido: " + 
                          (user != null ? (user.getDisplayName() != null ? user.getDisplayName() : user.getEmail()) : "null"));
                    
                    try {
                        if (isFinishing()) {
                            Log.e(TAG, "La actividad está finalizando, no se puede actualizar la UI");
                            return;
                        }
                        
                        runOnUiThread(() -> {
                            try {
                                Log.d(TAG, "Ejecutando actualización de UI en el hilo principal después de inicio de sesión exitoso");
                                // Re-enable the sign-in button
                                if (signInButton != null) {
                                    signInButton.setEnabled(true);
                                    Log.d(TAG, "Botón de inicio de sesión habilitado");
                                } else {
                                    Log.w(TAG, "signInButton es nulo al intentar habilitarlo");
                                }
                                
                                updateUI(user);
                                Log.d(TAG, "UI actualizada correctamente");
                                
                                // Check and create user data in Firestore
                                if (user != null) {
                                    Log.d(TAG, "Verificando/creando datos de usuario en Firestore");
                                    checkAndCreateUserData(user);
                                    Toast.makeText(MainActivity.this, "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                } else {
                                    Log.w(TAG, "Usuario es nulo después de inicio de sesión exitoso");
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error en la actualización de UI después de inicio de sesión exitoso", e);
                                Toast.makeText(MainActivity.this, "Error interno: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "Error general en callback onSuccess", e);
                    }
                }
                
                @Override
                public void onError(Exception e) {
                    Log.e(TAG, "Callback onError recibido: " + (e != null ? e.getMessage() : "null"), e);
                    
                    try {
                        if (isFinishing()) {
                            Log.e(TAG, "La actividad está finalizando, no se puede actualizar la UI después del error");
                            return;
                        }
                        
                        runOnUiThread(() -> {
                            try {
                                Log.d(TAG, "Ejecutando actualización de UI en el hilo principal después de error");
                                // Re-enable the sign-in button
                                if (signInButton != null) {
                                    signInButton.setEnabled(true);
                                    Log.d(TAG, "Botón de inicio de sesión habilitado después de error");
                                } else {
                                    Log.w(TAG, "signInButton es nulo al intentar habilitarlo después de error");
                                }
                                
                                // Si el error es específicamente relacionado con Credential Manager
                                // intentar el método tradicional como fallback
                                if (e instanceof androidx.credentials.exceptions.GetCredentialException || 
                                    e instanceof androidx.credentials.exceptions.ClearCredentialException || 
                                    e instanceof androidx.credentials.exceptions.CreateCredentialException) {
                                    Log.d(TAG, "Intentando autenticación tradicional como fallback");
                                    Toast.makeText(MainActivity.this, "Cambiando a método alternativo...", 
                                            Toast.LENGTH_SHORT).show();
                                    fallbackToTraditionalSignIn();
                                } else {
                                    updateUI(null);
                                    Toast.makeText(MainActivity.this, "Error de autenticación: " + e.getMessage(), 
                                            Toast.LENGTH_SHORT).show();
                                    updateStatusText(getString(R.string.not_signed_in));
                                }
                            } catch (Exception ex) {
                                Log.e(TAG, "Error en la actualización de UI después de error de autenticación", ex);
                                Toast.makeText(MainActivity.this, "Error interno: " + ex.getMessage(), Toast.LENGTH_SHORT).show();
                            }
                        });
                    } catch (Exception ex) {
                        Log.e(TAG, "Error general en callback onError", ex);
                    }
                }
            });
            Log.d(TAG, "Llamada a signIn realizada, esperando callback");
        } catch (Exception e) {
            Log.e(TAG, "Error crítico durante el proceso de inicio de sesión", e);
            Toast.makeText(this, "Error de autenticación: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            
            // Re-enable the sign-in button
            try {
                if (signInButton != null) {
                    signInButton.setEnabled(true);
                    Log.d(TAG, "Botón de inicio de sesión habilitado después de error crítico");
                }
                
                updateStatusText(getString(R.string.not_signed_in));
                
                // Como fallback, intentar autenticación tradicional
                fallbackToTraditionalSignIn();
            } catch (Exception ex) {
                Log.e(TAG, "Error al recuperarse de un error crítico", ex);
            }
        }
    }
    
    /**
     * Update status text in the UI
     */
    private void updateStatusText(String text) {
        if (statusTextView != null) {
            statusTextView.setText(text);
        }
    }
    
    /**
     * Inicializa la autenticación con Firebase
     */
    private void initializeFirebaseAuth() {
        try {
            mAuth = FirebaseAuth.getInstance();
            Log.d(TAG, "Firebase Auth initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Firebase Auth: " + e.getMessage());
            Toast.makeText(this, "Authentication service unavailable", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Inicializa Firestore
     */
    private void initializeFirestore() {
        try {
            db = FirebaseFirestore.getInstance();
            Log.d(TAG, "Firestore initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Firestore: " + e.getMessage());
            Toast.makeText(this, "Database service unavailable", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Inicializa Remote Config
     */
    private void initializeRemoteConfig() {
        try {
            mFirebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
            FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                    .setMinimumFetchIntervalInSeconds(3600)
                    .build();
            mFirebaseRemoteConfig.setConfigSettingsAsync(configSettings);
            mFirebaseRemoteConfig.setDefaultsAsync(R.xml.remote_config_defaults);
        fetchRemoteConfig();
            Log.d(TAG, "Remote Config initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Remote Config: " + e.getMessage());
            Toast.makeText(this, "Configuration service unavailable", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Fetch and activate the latest Remote Config values
     */
    private void fetchRemoteConfig() {
        try {
            mFirebaseRemoteConfig.fetchAndActivate()
                    .addOnCompleteListener(this, new OnCompleteListener<Boolean>() {
            @Override
                        public void onComplete(@NonNull Task<Boolean> task) {
                    if (task.isSuccessful()) {
                        boolean updated = task.getResult();
                                Log.d(TAG, "Remote Config params updated: " + updated);
                    } else {
                                Log.d(TAG, "Remote Config fetch failed");
                            }
                            // Proceed with app initialization
                            updateUI(mAuth.getCurrentUser());
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error fetching Remote Config: " + e.getMessage());
            // Still update UI even if remote config fails
            updateUI(mAuth != null ? mAuth.getCurrentUser() : null);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        // Check if user is signed in and update UI accordingly
        try {
            FirebaseUser currentUser = mAuth != null ? mAuth.getCurrentUser() : null;
            
            // If credential manager is initialized, use that instead
            if (mCredentialManagerAuthHelper != null) {
                currentUser = mCredentialManagerAuthHelper.getCurrentUser();
            }
            
            updateUI(currentUser);
            
            // Actualizar lastLogin del usuario si está autenticado
            if (currentUser != null && db != null) {
                Map<String, Object> updateData = new HashMap<>();
                updateData.put("lastLogin", new com.google.firebase.Timestamp(new Date()));
                db.collection("users").document(currentUser.getUid())
                    .set(updateData, SetOptions.merge())
                    .addOnSuccessListener(aVoid -> Log.d(TAG, "lastLogin actualizado correctamente"))
                    .addOnFailureListener(e -> Log.e(TAG, "Error al actualizar lastLogin: " + e.getMessage()));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking current user: " + e.getMessage());
            updateUI(null);
        }
    }

    private void checkAndCreateUserData(FirebaseUser user) {
        if (user == null || db == null) {
            Log.e(TAG, "User or Firestore is null");
            return;
        }

        try {
            DocumentReference userRef = db.collection("users").document(user.getUid());
            userRef.get().addOnCompleteListener(new OnCompleteListener<DocumentSnapshot>() {
                @Override
                public void onComplete(@NonNull Task<DocumentSnapshot> task) {
                    if (task.isSuccessful()) {
                        DocumentSnapshot document = task.getResult();
                        if (document != null && !document.exists()) {
                            // Create new user data
                            createNewUserData(user);
                        } else {
                            Log.d(TAG, "User data already exists");
                            readPostData();
                        }
                    } else {
                        Log.w(TAG, "Failed to check user data", task.getException());
                        // Proceed anyway
                        readPostData();
                    }
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error checking user data: " + e.getMessage());
            // Proceed anyway
            readPostData();
        }
    }

    private void createNewUserData(FirebaseUser user) {
        if (user == null || db == null) {
            Log.e(TAG, "User or Firestore is null");
            return;
        }

        try {
            Map<String, Object> userData = new HashMap<>();
            userData.put("name", user.getDisplayName());
            userData.put("email", user.getEmail());
            userData.put("photoUrl", user.getPhotoUrl() != null ? user.getPhotoUrl().toString() : "");
            userData.put("createdAt", System.currentTimeMillis());

            db.collection("users").document(user.getUid())
                    .set(userData)
                    .addOnSuccessListener(new OnSuccessListener<Void>() {
                        @Override
                        public void onSuccess(Void aVoid) {
                            Log.d(TAG, "User data created successfully");
                            // Continue with app initialization
                            readPostData();
                        }
                    })
                    .addOnFailureListener(new OnFailureListener() {
                        @Override
                        public void onFailure(@NonNull Exception e) {
                            Log.w(TAG, "Error creating user data", e);
                            // Continue anyway
                            readPostData();
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error creating user data: " + e.getMessage());
            // Continue anyway
            readPostData();
        }
    }

    private void readPostData() {
        if (db == null) {
            Log.e(TAG, "Firestore is not initialized");
            launchMainApp();
            return;
        }

        try {
            db.collection("posts")
                    .get()
                    .addOnCompleteListener(new OnCompleteListener<QuerySnapshot>() {
                        @Override
                        public void onComplete(@NonNull Task<QuerySnapshot> task) {
                            if (task.isSuccessful()) {
                                QuerySnapshot querySnapshot = task.getResult();
                                if (querySnapshot != null && querySnapshot.isEmpty()) {
                                    // Create sample data if no posts exist
                                    createSampleData();
                                } else {
                                    ArrayList<Map<String, Object>> postList = new ArrayList<>();
                                    for (QueryDocumentSnapshot document : task.getResult()) {
                                        try {
                                            Map<String, Object> post = document.getData();
                                            postList.add(post);
                                        } catch (Exception e) {
                                            Log.e(TAG, "Error parsing post data: " + e.getMessage());
                                        }
                                    }
                                    Log.d(TAG, "Read " + postList.size() + " posts from Firestore");
                                }
                                // Launch main app regardless of post data
                                launchMainApp();
                            } else {
                                Log.w(TAG, "Error getting posts", task.getException());
                                // Launch main app anyway
                                launchMainApp();
                            }
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error reading post data: " + e.getMessage());
            // Launch main app anyway
            launchMainApp();
        }
    }

    private void createSampleData() {
        if (db == null) {
            Log.e(TAG, "Firestore is not initialized");
            launchMainApp();
            return;
        }

        try {
            // Create sample data for the app
            Map<String, Object> samplePost = new HashMap<>();
            samplePost.put("title", "Sample Post");
            samplePost.put("description", "This is a sample post created when the app is first installed.");
            samplePost.put("imageUrl", "https://example.com/sample.jpg");
            samplePost.put("timestamp", System.currentTimeMillis());
            samplePost.put("userId", "sample_user_id");
            samplePost.put("latitude", 37.7749); // San Francisco latitude
            samplePost.put("longitude", -122.4194); // San Francisco longitude

            db.collection("posts")
                    .add(samplePost)
                    .addOnSuccessListener(new OnSuccessListener<DocumentReference>() {
                        @Override
                        public void onSuccess(DocumentReference documentReference) {
                            Log.d(TAG, "Sample post created with ID: " + documentReference.getId());
                            launchMainApp();
                        }
                    })
                    .addOnFailureListener(new OnFailureListener() {
                        @Override
                        public void onFailure(@NonNull Exception e) {
                            Log.w(TAG, "Error creating sample post", e);
                            launchMainApp();
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error creating sample data: " + e.getMessage());
            launchMainApp();
        }
    }

    private void updateUI(FirebaseUser user) {
        try {
            if (user != null) {
                // User is signed in
                if (statusTextView != null) {
                    String displayName = user.getDisplayName();
                    String email = user.getEmail();
                    if (displayName != null && !displayName.isEmpty()) {
                        statusTextView.setText("Bienvenido, " + displayName);
                    } else if (email != null && !email.isEmpty()) {
                        statusTextView.setText("Bienvenido, " + email);
                    } else {
                        statusTextView.setText("Bienvenido");
                    }
                }
                
                if (signInButton != null) {
                    signInButton.setVisibility(View.GONE);
                }
                
                if (goToAppButton != null) {
                    goToAppButton.setVisibility(View.VISIBLE);
                }
                
                if (signOutButton != null) {
                    signOutButton.setVisibility(View.VISIBLE);
                }
                
                if (viewDataButton != null) {
                    viewDataButton.setVisibility(View.VISIBLE);
                }
                
                // Mostrar botones de carga de datos
                if (loadMinibusButton != null) {
                    loadMinibusButton.setVisibility(View.VISIBLE);
                }
                
                if (loadBusButton != null) {
                    loadBusButton.setVisibility(View.VISIBLE);
                }
                
                if (loadTaxiButton != null) {
                    loadTaxiButton.setVisibility(View.VISIBLE);
                }
            } else {
                // User is signed out
                if (statusTextView != null) {
                    statusTextView.setText(R.string.not_signed_in);
                }
                
                if (signInButton != null) {
                    signInButton.setVisibility(View.VISIBLE);
                }
                
                if (goToAppButton != null) {
                    goToAppButton.setVisibility(View.VISIBLE);
                }
                
                if (signOutButton != null) {
                    signOutButton.setVisibility(View.GONE);
                }
                
                if (viewDataButton != null) {
                    viewDataButton.setVisibility(View.GONE);
                }
                
                // Ocultar botones de carga de datos
                if (loadMinibusButton != null) {
                    loadMinibusButton.setVisibility(View.GONE);
                }
                
                if (loadBusButton != null) {
                    loadBusButton.setVisibility(View.GONE);
                }
                
                if (loadTaxiButton != null) {
                    loadTaxiButton.setVisibility(View.GONE);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating UI: " + e.getMessage());
            // Ensure the app is always accessible
            if (goToAppButton != null) {
                goToAppButton.setVisibility(View.VISIBLE);
            }
        }
    }
    
    private void launchMainApp() {
        // Mostrar mensaje y ocultar el botón
        if (statusTextView != null) {
            statusTextView.setText("¡Bienvenido a Itatí Explore!");
        }
        
        // Limpiar la estructura antigua de datos si existe
        FirestoreManager firestoreManager = FirestoreManager.getInstance();
        firestoreManager.eliminarEstructuraAntigua(new FirestoreManager.OperationCompleteListener() {
            @Override
            public void onSuccess() {
                Log.d(TAG, "Limpieza de estructura antigua completada");
            }

            @Override
            public void onError(Exception e) {
                Log.e(TAG, "Error al limpiar estructura antigua", e);
            }
        });
        
        // Iniciar la actividad principal
        try {
            Intent intent = new Intent(MainActivity.this, MainAppActivity.class);
            startActivity(intent);
            finish();
        } catch (Exception e) {
            Log.e(TAG, "Error al iniciar la actividad principal", e);
            Toast.makeText(this, "Error al iniciar la aplicación: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Carga datos de transporte en Firestore
     */
    private void loadTransportData() {
        if (db == null) {
            db = FirebaseFirestore.getInstance();
        }
        
        // Cargar datos de minibus
        loadMinibusData();
        
        // Cargar datos de colectivo
        loadBusData();
        
        // Cargar datos de taxi
        loadTaxiData();
    }
    
    /**
     * Carga datos de minibus en Firestore
     */
    private void loadMinibusData() {
        // Verificar si ya existen datos
        db.collection("transporte").document("minibus").collection("items")
            .get()
            .addOnSuccessListener(querySnapshot -> {
                if (querySnapshot.isEmpty()) {
                    // No hay datos, crear ejemplos
                    createSampleMinibusData();
                } else {
                    Log.d(TAG, "Ya existen " + querySnapshot.size() + " documentos de minibus");
                    Toast.makeText(this, "Datos de minibus ya existen", Toast.LENGTH_SHORT).show();
                }
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al verificar datos de minibus", e);
                // Intentar crear de todos modos
                createSampleMinibusData();
            });
    }
    
    /**
     * Crea datos de ejemplo para minibus
     */
    private void createSampleMinibusData() {
        // Crear documento principal
        db.collection("transporte").document("minibus")
            .set(new HashMap<String, Object>() {{
                put("nombre", "Minibus");
                put("descripcion", "Servicios de minibus en Itatí");
            }})
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Documento minibus creado correctamente");
                
                // Crear items de minibus
                createMinibusItem("Combi 95", 
                    "Minibus con capacidad para 15 pasajeros", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Fminibus3.jpg",
                    "+54 3794 123456",
                    "+54 9 3794 123456",
                    new String[]{"08:00", "10:00"},
                    198);
                
                createMinibusItem("Minibus Express Itatí", 
                    "Servicio rápido de minibus con pocas paradas", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Fminibus1.jpg",
                    "+54 3794 654321",
                    "+54 9 3794 654321",
                    new String[]{"06:30", "09:30", "12:30", "15:30", "19:30"},
                    220);
                
                createMinibusItem("Minibus Turístico Itatí", 
                    "Recorridos turísticos por Itatí y alrededores", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Fminibus2.jpg",
                    "+54 3794 789012",
                    "+54 9 3794 789012",
                    new String[]{"09:00", "11:00", "15:00", "17:00"},
                    250);
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al crear documento minibus", e);
            });
    }
    
    /**
     * Crea un item de minibus
     */
    private void createMinibusItem(String nombre, String descripcion, String imagenUrl, 
                                  String telefono, String whatsapp, String[] horarios, int precio) {
        // Crear un ID único para el documento
        String docId = "random_" + System.currentTimeMillis();
        
        // Crear el mapa de datos
        Map<String, Object> minibusData = new HashMap<>();
        minibusData.put("activo", true);
        minibusData.put("descripcion", descripcion);
        minibusData.put("horarios", java.util.Arrays.asList(horarios));
        minibusData.put("id", docId);
        minibusData.put("imagenUrl", imagenUrl);
        minibusData.put("nombre", nombre);
        minibusData.put("precio", precio);
        minibusData.put("timestamp", System.currentTimeMillis());
        minibusData.put("tipo", "minibus");
        minibusData.put("telefono", telefono);
        minibusData.put("whatsapp", whatsapp);
        
        // Guardar en Firestore
        db.collection("transporte").document("minibus").collection("items")
            .document(docId)
            .set(minibusData)
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Minibus guardado correctamente: " + nombre);
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al guardar minibus: " + nombre, e);
            });
    }
    
    /**
     * Carga datos de colectivo en Firestore
     */
    private void loadBusData() {
        // Verificar si ya existen datos
        db.collection("transporte").document("colectivo").collection("items")
            .get()
            .addOnSuccessListener(querySnapshot -> {
                if (querySnapshot.isEmpty()) {
                    // No hay datos, crear ejemplos
                    createSampleBusData();
                } else {
                    Log.d(TAG, "Ya existen " + querySnapshot.size() + " documentos de colectivo");
                }
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al verificar datos de colectivo", e);
                // Intentar crear de todos modos
                createSampleBusData();
            });
    }
    
    /**
     * Crea datos de ejemplo para colectivo
     */
    private void createSampleBusData() {
        // Crear documento principal
        db.collection("transporte").document("colectivo")
            .set(new HashMap<String, Object>() {{
                put("nombre", "Colectivo");
                put("descripcion", "Servicios de colectivo en Itatí");
            }})
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Documento colectivo creado correctamente");
                
                // Crear items de colectivo
                createBusItem("Línea 101", 
                    "Servicio de colectivo urbano", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Fbus1.jpg",
                    "+54 3794 111222",
                    "+54 9 3794 111222",
                    new String[]{"06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00"},
                    50);
                
                createBusItem("Línea 202", 
                    "Servicio de colectivo interurbano", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Fbus2.jpg",
                    "+54 3794 222333",
                    "+54 9 3794 222333",
                    new String[]{"05:30", "07:30", "09:30", "11:30", "13:30", "15:30", "17:30"},
                    75);
                
                createBusItem("Línea 303", 
                    "Servicio de colectivo de larga distancia", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Fbus3.jpg",
                    "+54 3794 333444",
                    "+54 9 3794 333444",
                    new String[]{"07:00", "12:00", "17:00", "22:00"},
                    120);
                
                Toast.makeText(MainActivity.this, "Datos de colectivo creados correctamente", Toast.LENGTH_SHORT).show();
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al crear documento colectivo", e);
                Toast.makeText(this, "Error al crear datos de colectivo", Toast.LENGTH_SHORT).show();
            });
    }
    
    /**
     * Crea un item de colectivo
     */
    private void createBusItem(String nombre, String descripcion, String imagenUrl, 
                              String telefono, String whatsapp, String[] horarios, int precio) {
        // Crear un ID único para el documento
        String docId = "random_" + System.currentTimeMillis();
        
        // Crear el mapa de datos
        Map<String, Object> busData = new HashMap<>();
        busData.put("activo", true);
        busData.put("descripcion", descripcion);
        busData.put("horarios", java.util.Arrays.asList(horarios));
        busData.put("id", docId);
        busData.put("imagenUrl", imagenUrl);
        busData.put("nombre", nombre);
        busData.put("precio", precio);
        busData.put("timestamp", System.currentTimeMillis());
        busData.put("tipo", "colectivo");
        busData.put("telefono", telefono);
        busData.put("whatsapp", whatsapp);
        
        // Guardar en Firestore
        db.collection("transporte").document("colectivo").collection("items")
            .document(docId)
            .set(busData)
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Colectivo guardado correctamente: " + nombre);
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al guardar colectivo: " + nombre, e);
            });
    }
    
    /**
     * Carga datos de taxi en Firestore
     */
    private void loadTaxiData() {
        // Verificar si ya existen datos
        db.collection("transporte").document("taxi").collection("items")
            .get()
            .addOnSuccessListener(querySnapshot -> {
                if (querySnapshot.isEmpty()) {
                    // No hay datos, crear ejemplos
                    createSampleTaxiData();
                } else {
                    Log.d(TAG, "Ya existen " + querySnapshot.size() + " documentos de taxi");
                }
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al verificar datos de taxi", e);
                // Intentar crear de todos modos
                createSampleTaxiData();
            });
    }
    
    /**
     * Crea datos de ejemplo para taxi
     */
    private void createSampleTaxiData() {
        // Crear documento principal
        db.collection("transporte").document("taxi")
            .set(new HashMap<String, Object>() {{
                put("nombre", "Taxi");
                put("descripcion", "Servicios de taxi en Itatí");
            }})
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Documento taxi creado correctamente");
                
                // Crear items de taxi
                createTaxiItem("Taxi Itatí", 
                    "Servicio de taxi las 24 horas", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Ftaxi1.jpg",
                    "+54 3794 555666",
                    "+54 9 3794 555666",
                    new String[]{"24 horas"},
                    150);
                
                createTaxiItem("Remis Express", 
                    "Servicio de remis con reserva previa", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Ftaxi2.jpg",
                    "+54 3794 666777",
                    "+54 9 3794 666777",
                    new String[]{"06:00 a 00:00"},
                    180);
                
                createTaxiItem("Taxi Turístico", 
                    "Servicio especializado para turistas con guía", 
                    "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/transporte%2Ftaxi3.jpg",
                    "+54 3794 777888",
                    "+54 9 3794 777888",
                    new String[]{"08:00 a 20:00"},
                    200);
                
                Toast.makeText(MainActivity.this, "Datos de taxi creados correctamente", Toast.LENGTH_SHORT).show();
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al crear documento taxi", e);
                Toast.makeText(this, "Error al crear datos de taxi", Toast.LENGTH_SHORT).show();
            });
    }
    
    /**
     * Crea un item de taxi
     */
    private void createTaxiItem(String nombre, String descripcion, String imagenUrl, 
                               String telefono, String whatsapp, String[] horarios, int precio) {
        // Crear un ID único para el documento
        String docId = "random_" + System.currentTimeMillis();
        
        // Crear el mapa de datos
        Map<String, Object> taxiData = new HashMap<>();
        taxiData.put("activo", true);
        taxiData.put("descripcion", descripcion);
        taxiData.put("horarios", java.util.Arrays.asList(horarios));
        taxiData.put("id", docId);
        taxiData.put("imagenUrl", imagenUrl);
        taxiData.put("nombre", nombre);
        taxiData.put("precio", precio);
        taxiData.put("timestamp", System.currentTimeMillis());
        taxiData.put("tipo", "taxi");
        taxiData.put("telefono", telefono);
        taxiData.put("whatsapp", whatsapp);
        
        // Guardar en Firestore
        db.collection("transporte").document("taxi").collection("items")
            .document(docId)
            .set(taxiData)
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Taxi guardado correctamente: " + nombre);
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al guardar taxi: " + nombre, e);
            });
    }

    private void updateTransportData(String transportData) {
        if (db == null) {
            Log.e(TAG, "Firestore is not initialized");
            Toast.makeText(this, "Database service unavailable", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // Crear un objeto con los datos actualizados
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("title", "Transporte Actualizado");
            updateData.put("description", transportData);
            updateData.put("timestamp", System.currentTimeMillis());
            
        Toast.makeText(MainActivity.this, "Actualizando datos...", Toast.LENGTH_SHORT).show();
        
            // Actualizar o crear un documento de transporte
            db.collection("transport")
                    .document("current")
                    .set(updateData)
                    .addOnSuccessListener(new OnSuccessListener<Void>() {
                        @Override
                        public void onSuccess(Void aVoid) {
                            Log.d(TAG, "Datos de transporte actualizados correctamente");
                    Toast.makeText(MainActivity.this, 
                                "Transporte actualizado correctamente", 
                                Toast.LENGTH_SHORT).show();
                        }
                    })
                    .addOnFailureListener(new OnFailureListener() {
                        @Override
                        public void onFailure(@NonNull Exception e) {
                            Log.e(TAG, "Error al actualizar datos de transporte", e);
                    Toast.makeText(MainActivity.this, 
                                "Error al actualizar: " + e.getMessage(), 
                                Toast.LENGTH_SHORT).show();
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error al preparar datos para actualización", e);
            Toast.makeText(this, "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Signs out the current user
     */
    private void signOut() {
        try {
            // Sign out from Credential Manager if available
            if (mCredentialManagerAuthHelper != null) {
                mCredentialManagerAuthHelper.signOut(new CredentialManagerAuthHelper.AuthCallback() {
                    @Override
                    public void onSuccess(FirebaseUser user) {
                        // user will be null after sign out
                        runOnUiThread(() -> {
                            Toast.makeText(MainActivity.this, "Sesión cerrada correctamente", Toast.LENGTH_SHORT).show();
                            updateUI(null);
                        });
                    }
                    
                    @Override
                    public void onError(Exception e) {
                        Log.e(TAG, "Error durante el cierre de sesión", e);
                        runOnUiThread(() -> {
                            Toast.makeText(MainActivity.this, "Error al cerrar sesión", Toast.LENGTH_SHORT).show();
                            // Update UI anyway as Firebase sign out might have worked
                            updateUI(null);
                        });
                    }
                });
            } 
            // Fallback to traditional Firebase sign-out
            else if (mAuth != null) {
                mAuth.signOut();
                Toast.makeText(this, "Sesión cerrada correctamente", Toast.LENGTH_SHORT).show();
                updateUI(null);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error durante el cierre de sesión", e);
            Toast.makeText(this, "Error al cerrar sesión", Toast.LENGTH_SHORT).show();
            updateUI(null);
        }
    }

    /**
     * Fallback to traditional Google Sign In for devices that don't support Credential Manager
     */
    private void fallbackToTraditionalSignIn() {
        try {
            Log.d(TAG, "Inicializando autenticación tradicional con Google");
            
            // Evitar inicializar múltiples veces
            if (mGoogleSignInClient != null) {
                Log.d(TAG, "GoogleSignInClient ya está inicializado");
                if (signInButton != null && !signInButton.hasOnClickListeners()) {
                    configureSignInButton();
                }
                return;
            }
            
            // Obtener el client ID de manera segura
            String clientId;
            try {
                clientId = getString(R.string.firebase_web_client_id);
                if (clientId == null || clientId.isEmpty()) {
                    clientId = getString(R.string.default_web_client_id);
                }
                
                if (clientId == null || clientId.isEmpty()) {
                    Log.e(TAG, "No se pudo obtener un client ID válido para Google Sign In");
                    Toast.makeText(this, "Error de configuración: Client ID no disponible", Toast.LENGTH_SHORT).show();
                    if (signInButton != null) {
                        signInButton.setEnabled(false);
                    }
                    return;
                }
                
                Log.d(TAG, "ID de cliente obtenido: " + clientId.substring(0, Math.min(10, clientId.length())) + "...");
            } catch (Exception e) {
                Log.e(TAG, "Error al obtener ID de cliente para Google Sign In", e);
                Toast.makeText(this, "Error de configuración: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                if (signInButton != null) {
                    signInButton.setEnabled(false);
                }
                return;
            }
            
            // Initialize Google Sign-In
            try {
                GoogleSignInOptions gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                        .requestIdToken(clientId)
                        .requestEmail()
                        .requestProfile()  // Request user profile information
                        .build();
                
                mGoogleSignInClient = GoogleSignIn.getClient(this, gso);
                Log.d(TAG, "GoogleSignInClient inicializado correctamente");
            } catch (Exception e) {
                Log.e(TAG, "Error al crear GoogleSignInOptions o GoogleSignInClient", e);
                Toast.makeText(this, "Error al inicializar autenticación de Google: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                if (signInButton != null) {
                    signInButton.setEnabled(false);
                }
                return;
            }
            
            // Check if we already have a signed in Google account
            try {
                GoogleSignInAccount account = GoogleSignIn.getLastSignedInAccount(this);
                if (account != null && account.getIdToken() != null) {
                    Log.d(TAG, "Usuario ya autenticado con Google, obteniendo token para Firebase");
                    // User is already signed in to Google, authenticate with Firebase
                    firebaseAuthWithGoogle(account.getIdToken());
                } else {
                    Log.d(TAG, "No hay sesión activa de Google");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar cuenta de Google existente", e);
            }
            
            // Set up sign-in button click listener
            configureSignInButton();
            
        } catch (Exception e) {
            Log.e(TAG, "Error general al inicializar la autenticación tradicional con Google: " + e.getMessage(), e);
            Toast.makeText(this, "Servicio de autenticación no disponible", Toast.LENGTH_SHORT).show();
            // Disable sign-in button if initialization fails
            if (signInButton != null) {
                signInButton.setEnabled(false);
            }
        }
    }
    
    /**
     * Configure the sign-in button for traditional Google Sign-In
     */
    private void configureSignInButton() {
        if (signInButton != null && mGoogleSignInClient != null) {
            Log.d(TAG, "Configurando botón para autenticación tradicional con Google");
            
            // Remove previous click listeners
            signInButton.setOnClickListener(null);
            
            // Set new click listener
            signInButton.setOnClickListener(v -> {
                try {
                    Log.d(TAG, "Iniciando flujo de autenticación con Google");
                    // Show loading state
                    signInButton.setEnabled(false);
                    updateStatusText("Iniciando sesión con Google...");
                    
                    Intent signInIntent = mGoogleSignInClient.getSignInIntent();
                    startActivityForResult(signInIntent, RC_SIGN_IN);
                } catch (Exception e) {
                    Log.e(TAG, "Error al iniciar proceso de autenticación: " + e.getMessage(), e);
                    Toast.makeText(this, "No se pudo iniciar el proceso de autenticación", Toast.LENGTH_SHORT).show();
                    signInButton.setEnabled(true);
                    updateStatusText(getString(R.string.not_signed_in));
                }
            });
            signInButton.setEnabled(true);
        } else {
            Log.e(TAG, "No se puede configurar botón de Google Sign-In (null)");
        }
    }
    
    /**
     * ⚠️ NO MODIFICAR - NO TOUCH ⚠️
     * Este método maneja la autenticación con Firebase y el guardado automático de datos del usuario.
     * Cualquier modificación puede afectar el funcionamiento del inicio de sesión y el guardado de datos.
     * Si necesita hacer cambios, contacte al desarrollador principal.
     */
    private void firebaseAuthWithGoogle(String idToken) {
        if (mAuth == null) {
            Log.e(TAG, "Firebase Auth no está inicializado");
            Toast.makeText(this, "Servicio de autenticación no disponible", Toast.LENGTH_SHORT).show();
            updateUI(null);
            return;
        }

        Log.d(TAG, "Autenticando con Firebase usando token de Google");
        
        try {
            AuthCredential credential = GoogleAuthProvider.getCredential(idToken, null);
            
            mAuth.signInWithCredential(credential)
                .addOnCompleteListener(this, task -> {
                    if (task.isSuccessful()) {
                        Log.d(TAG, "Autenticación con Firebase exitosa");
                        FirebaseUser user = mAuth.getCurrentUser();
                        
                        if (user != null) {
                            // Guardar datos del usuario en Firestore automáticamente
                            Map<String, Object> userData = new HashMap<>();
                            userData.put("uid", user.getUid());
                            userData.put("email", user.getEmail());
                            userData.put("displayName", user.getDisplayName());
                            userData.put("photoUrl", user.getPhotoUrl() != null ? user.getPhotoUrl().toString() : null);
                            userData.put("lastLogin", new Date());
                            userData.put("isActive", true);

                            // Verificar si es un usuario nuevo
                            db.collection("users").document(user.getUid())
                                .get()
                                .addOnSuccessListener(documentSnapshot -> {
                                    if (!documentSnapshot.exists()) {
                                        userData.put("createdAt", new Date());
                                    }
                                    
                                    // Guardar o actualizar datos del usuario
                                    db.collection("users").document(user.getUid())
                                        .set(userData, SetOptions.merge())
                                        .addOnSuccessListener(aVoid -> {
                                            Log.d(TAG, "Datos de usuario guardados/actualizados en users");
                                            updateUI(user);
                                            Toast.makeText(MainActivity.this, "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                            launchMainApp(); // Lanzar la app principal después de guardar los datos
                                        })
                                        .addOnFailureListener(e -> {
                                            Log.e(TAG, "Error al guardar datos de usuario", e);
                                            updateUI(user);
                                            Toast.makeText(MainActivity.this, "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                            launchMainApp(); // Lanzar la app principal incluso si hay error
                                        });
                                })
                                .addOnFailureListener(e -> {
                                    Log.e(TAG, "Error al verificar existencia de usuario", e);
                                    updateUI(user);
                                    Toast.makeText(MainActivity.this, "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                    launchMainApp(); // Lanzar la app principal incluso si hay error
                                });
                        } else {
                            updateUI(null);
                        }
                    } else {
                        Log.w(TAG, "Autenticación con Firebase falló", task.getException());
                        Toast.makeText(MainActivity.this, "Error de autenticación", Toast.LENGTH_SHORT).show();
                        updateUI(null);
                    }
                    
                    if (signInButton != null) {
                        signInButton.setEnabled(true);
                    }
                });
        } catch (Exception e) {
            Log.e(TAG, "Error durante la autenticación con Firebase: " + e.getMessage(), e);
            Toast.makeText(this, "Error en el proceso de autenticación", Toast.LENGTH_SHORT).show();
            updateUI(null);
            
            if (signInButton != null) {
                signInButton.setEnabled(true);
            }
        }
    }
    
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // Result returned from launching the Intent from GoogleSignInClient.getSignInIntent(...)
        if (requestCode == RC_SIGN_IN) {
            Log.d(TAG, "Resultado de autenticación de Google recibido: " + resultCode);
            
            // Re-enable the sign-in button in any case
            if (signInButton != null) {
                signInButton.setEnabled(true);
            }
            
            // Check for successful result code
            if (resultCode != RESULT_OK) {
                Log.w(TAG, "Google Sign In cancelado por el usuario, resultCode: " + resultCode);
                updateUI(null);
                updateStatusText(getString(R.string.not_signed_in));
                return;
            }
            
            try {
                Task<GoogleSignInAccount> task = GoogleSignIn.getSignedInAccountFromIntent(data);
                GoogleSignInAccount account = task.getResult(ApiException.class);
                if (account != null) {
                    Log.d(TAG, "Google Sign In exitoso, autenticando con Firebase");
                    // Google Sign In was successful, authenticate with Firebase
                    firebaseAuthWithGoogle(account.getIdToken());
                } else {
                    Log.w(TAG, "Google Sign In retornó cuenta nula");
                    updateUI(null);
                    Toast.makeText(this, "No se pudo obtener la cuenta de Google", Toast.LENGTH_SHORT).show();
                    updateStatusText(getString(R.string.not_signed_in));
                }
            } catch (ApiException e) {
                // The ApiException status code indicates the detailed failure reason
                Log.w(TAG, "Google Sign In falló: código=" + e.getStatusCode(), e);
                updateUI(null);
                
                String errorMessage;
                switch (e.getStatusCode()) {
                    case 12500: // SIGN_IN_CANCELLED
                        errorMessage = "Autenticación cancelada";
                        break;
                    case 12501: // SIGN_IN_CURRENTLY_IN_PROGRESS
                        errorMessage = "Autenticación en progreso";
                        break;
                    case 12502: // SIGN_IN_FAILED
                        errorMessage = "Autenticación fallida";
                        break;
                    default:
                        errorMessage = "Error en la autenticación con Google (código: " + e.getStatusCode() + ")";
                }
                
                Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show();
                updateStatusText(getString(R.string.not_signed_in));
            } catch (Exception e) {
                Log.e(TAG, "Error inesperado durante la autenticación con Google", e);
                updateUI(null);
                Toast.makeText(this, "Error en el proceso de autenticación", Toast.LENGTH_SHORT).show();
                updateStatusText(getString(R.string.not_signed_in));
            }
        }
    }

    /**
     * Sign in with Google account via traditional method
     */
    private void signInWithGoogle() {
        try {
            if (mGoogleSignInClient == null) {
                Log.e(TAG, "GoogleSignInClient es null. Intentando inicializar nuevamente.");
                fallbackToTraditionalSignIn();
                
                if (mGoogleSignInClient == null) {
                    Log.e(TAG, "No se pudo inicializar GoogleSignInClient");
                    Toast.makeText(this, "Error al iniciar sesión. Servicio no disponible.", Toast.LENGTH_SHORT).show();
                    return;
                }
            }
            
            Log.d(TAG, "Iniciando flujo de autenticación con Google");
            Intent signInIntent = mGoogleSignInClient.getSignInIntent();
            if (signInIntent == null) {
                Log.e(TAG, "No se pudo crear Intent para inicio de sesión con Google");
                Toast.makeText(this, "Error al iniciar sesión. Intente más tarde.", Toast.LENGTH_SHORT).show();
                return;
            }
            
            startActivityForResult(signInIntent, RC_SIGN_IN);
            Log.d(TAG, "Activity para Sign In iniciada correctamente");
        } catch (Exception e) {
            Log.e(TAG, "Error al intentar iniciar sesión con Google: " + e.getMessage(), e);
            Toast.makeText(this, "Error al iniciar sesión: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            
            // Si hay un error grave, intentar reinicializar el cliente
            try {
                mGoogleSignInClient = null;
                fallbackToTraditionalSignIn();
            } catch (Exception ex) {
                Log.e(TAG, "Error al reinicializar el cliente de Google", ex);
            }
        }
    }
} 