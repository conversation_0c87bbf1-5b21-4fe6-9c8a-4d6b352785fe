package com.Rages.itatiexplore.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Rages.itatiexplore.R;
import com.Rages.itatiexplore.adapters.LocationAdapter;
import com.Rages.itatiexplore.models.Location;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragmento para mostrar la lista de lugares turísticos
 */
public class LocationsFragment extends Fragment implements LocationAdapter.OnLocationClickListener {

    private RecyclerView recyclerView;
    private LocationAdapter adapter;
    private List<Location> locationList;
    private ProgressBar progressBar;
    private TextView emptyView;
    private FirebaseFirestore db;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_locations, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Inicializar vistas
        recyclerView = view.findViewById(R.id.locations_recycler_view);
        progressBar = view.findViewById(R.id.progress_bar);
        emptyView = view.findViewById(R.id.empty_view);

        // Configurar RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        locationList = new ArrayList<>();
        adapter = new LocationAdapter(getContext(), locationList, this);
        recyclerView.setAdapter(adapter);

        // Inicializar Firestore
        db = FirebaseFirestore.getInstance();

        // Cargar datos
        loadLocations();
    }

    /**
     * Carga los lugares desde Firestore
     */
    private void loadLocations() {
        progressBar.setVisibility(View.VISIBLE);
        
        db.collection("locations")
            .get()
            .addOnCompleteListener(task -> {
                progressBar.setVisibility(View.GONE);
                
                if (task.isSuccessful()) {
                    locationList.clear();
                    
                    for (QueryDocumentSnapshot document : task.getResult()) {
                        Location location = document.toObject(Location.class);
                        location.setId(document.getId());
                        locationList.add(location);
                    }
                    
                    adapter.notifyDataSetChanged();
                    
                    // Mostrar mensaje si no hay lugares
                    if (locationList.isEmpty()) {
                        emptyView.setVisibility(View.VISIBLE);
                        recyclerView.setVisibility(View.GONE);
                    } else {
                        emptyView.setVisibility(View.GONE);
                        recyclerView.setVisibility(View.VISIBLE);
                    }
                } else {
                    Snackbar.make(requireView(), "Error al cargar lugares", Snackbar.LENGTH_LONG).show();
                }
            });
    }

    @Override
    public void onLocationClick(Location location) {
        // Implementar navegación a la vista detallada del lugar
        // Por ejemplo:
        // Intent intent = new Intent(getActivity(), LocationDetailActivity.class);
        // intent.putExtra("LOCATION_ID", location.getId());
        // startActivity(intent);
        
        // Por ahora, solo mostraremos un mensaje
        Snackbar.make(requireView(), "Seleccionado: " + location.getName(), Snackbar.LENGTH_SHORT).show();
    }

    @Override
    public void onViewDetailsClick(Location location) {
        // Mismo comportamiento que el clic en el elemento completo
        onLocationClick(location);
    }
}