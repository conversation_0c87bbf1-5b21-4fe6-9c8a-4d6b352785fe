package com.Rages.itatiexplore.fragments;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.Rages.itatiexplore.R;

public class InfoFragment extends Fragment {

    private static final String TAG = "InfoFragment";

    public InfoFragment() {
        // Required empty public constructor
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        try {
            // Inflate the layout for this fragment
            return inflater.inflate(R.layout.fragment_info, container, false);
        } catch (Exception e) {
            Log.e(TAG, "Error al crear vista del fragmento", e);
            Toast.makeText(getContext(), "Error al cargar información", Toast.LENGTH_SHORT).show();
            return inflater.inflate(R.layout.fragment_error, container, false);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        try {
            // Inicializar vistas
            ImageView imageView = view.findViewById(R.id.itati_image);
            TextView titleTextView = view.findViewById(R.id.itati_title);
            TextView descriptionTextView = view.findViewById(R.id.itati_description);
            TextView historyTitleTextView = view.findViewById(R.id.history_title);
            TextView historyTextView = view.findViewById(R.id.history_text);
            TextView attractionsTitleTextView = view.findViewById(R.id.attractions_title);
            TextView attractionsTextView = view.findViewById(R.id.attractions_text);
            TextView contactTitleTextView = view.findViewById(R.id.contact_title);
            TextView contactTextView = view.findViewById(R.id.contact_text);
            
            // Establecer textos desde recursos de strings
            titleTextView.setText(R.string.itati_title);
            descriptionTextView.setText(R.string.itati_description);
            historyTitleTextView.setText(R.string.history_title);
            historyTextView.setText(R.string.history_text);
            attractionsTitleTextView.setText(R.string.attractions_title);
            attractionsTextView.setText(R.string.attractions_text);
            contactTitleTextView.setText(R.string.contact_title);
            contactTextView.setText(R.string.contact_text);
            
            // Configurar la imagen (se podría cargar de recursos o con una librería como Glide)
            imageView.setImageResource(R.drawable.itati_placeholder);
            imageView.setContentDescription(getString(R.string.itati_image_description));
            
            Log.d(TAG, "Información cargada correctamente");
            
        } catch (Exception e) {
            Log.e(TAG, "Error al inicializar vistas de información", e);
            Toast.makeText(getContext(), "Error al mostrar la información", Toast.LENGTH_SHORT).show();
        }
    }
} 