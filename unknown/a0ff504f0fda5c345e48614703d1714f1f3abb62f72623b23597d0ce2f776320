package com.Rages.itatiexplore.firebase;

import android.util.Log;

import com.Rages.itatiexplore.models.User;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.DocumentReference;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.SetOptions;

import androidx.annotation.NonNull;

public class UserRepository {
    private static final String TAG = "UserRepository";
    private static final String COLLECTION_USERS = "users";
    
    private final FirebaseFirestore db;
    
    public UserRepository() {
        this.db = FirebaseFirestore.getInstance();
        Log.d(TAG, "UserRepository inicializado");
    }
    
    /**
     * Guarda o actualiza los datos del usuario en Firestore
     * @param firebaseUser Usuario autenticado de Firebase
     * @param callback Callback para manejar el resultado
     */
    public void saveUser(FirebaseUser firebaseUser, OnCompleteListener<Void> callback) {
        if (firebaseUser == null) {
            Log.e(TAG, "FirebaseUser es nulo");
            return;
        }
        
        Log.d(TAG, "Iniciando guardado de usuario en Firestore");
        Log.d(TAG, "UID: " + firebaseUser.getUid());
        Log.d(TAG, "Email: " + firebaseUser.getEmail());
        Log.d(TAG, "DisplayName: " + firebaseUser.getDisplayName());
        
        // Crear objeto User con los datos del FirebaseUser
        User user = new User(
            firebaseUser.getUid(),
            firebaseUser.getEmail(),
            firebaseUser.getDisplayName(),
            firebaseUser.getPhotoUrl() != null ? firebaseUser.getPhotoUrl().toString() : null
        );
        
        // Referencia al documento del usuario
        DocumentReference userRef = db.collection(COLLECTION_USERS).document(firebaseUser.getUid());
        Log.d(TAG, "Referencia al documento creada: " + userRef.getPath());
        
        // Guardar o actualizar el documento
        userRef.set(user, SetOptions.merge())
            .addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    Log.d(TAG, "Usuario guardado exitosamente en Firestore");
                } else {
                    Log.e(TAG, "Error al guardar usuario en Firestore", task.getException());
                }
                if (callback != null) {
                    callback.onComplete(task);
                }
            });
    }
    
    /**
     * Obtiene los datos del usuario desde Firestore
     * @param uid ID del usuario
     * @param callback Callback para manejar el resultado
     */
    public void getUser(String uid, OnCompleteListener<DocumentSnapshot> callback) {
        if (uid == null || uid.isEmpty()) {
            Log.e(TAG, "UID es nulo o vacío");
            return;
        }
        
        db.collection(COLLECTION_USERS)
            .document(uid)
            .get()
            .addOnCompleteListener(callback);
    }
    
    /**
     * Actualiza el último login del usuario
     * @param uid ID del usuario
     * @param callback Callback para manejar el resultado
     */
    public void updateLastLogin(String uid, OnCompleteListener<Void> callback) {
        if (uid == null || uid.isEmpty()) {
            Log.e(TAG, "UID es nulo o vacío");
            return;
        }
        
        User user = new User();
        user.setLastLogin(com.google.firebase.Timestamp.now());
        
        db.collection(COLLECTION_USERS)
            .document(uid)
            .set(user, SetOptions.merge())
            .addOnCompleteListener(callback);
    }
} 