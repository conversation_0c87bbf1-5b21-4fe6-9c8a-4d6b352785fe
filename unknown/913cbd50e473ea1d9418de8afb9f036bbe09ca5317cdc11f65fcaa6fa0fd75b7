package com.Rages.itatiexplore.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Utilidades para formateo de fechas
 */
public class DateUtils {
    
    private static final SimpleDateFormat dateFormatInput = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    
    /**
     * Convierte una fecha del formato "dd/MM/yyyy" a "dd de MMMM"
     * Ejemplo: "16/07/2023" -> "16 de julio"
     */
    public static String formatDateToReadable(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return "";
        }
        
        try {
            // Primero parsear la fecha desde el formato original
            Date date = dateFormatInput.parse(dateStr);
            
            if (date == null) {
                return dateStr;
            }
            
            // Extraer día
            SimpleDateFormat dayFormat = new SimpleDateFormat("dd", Locale.getDefault());
            String day = dayFormat.format(date);
            
            // Extraer mes en formato texto
            SimpleDateFormat monthFormat = new SimpleDateFormat("MMMM", new Locale("es", "ES"));
            String month = monthFormat.format(date);
            
            // Formatear la fecha final
            return day + " de " + month;
            
        } catch (ParseException e) {
            // Si hay algún error en el parseo, devolver la fecha original
            return dateStr;
        }
    }
    
    /**
     * Convierte una fecha del formato "dd/MM/yyyy" a "dd de MMMM de yyyy"
     * Ejemplo: "16/07/2023" -> "16 de julio de 2023"
     */
    public static String formatDateToFullReadable(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return "";
        }
        
        try {
            // Primero parsear la fecha desde el formato original
            Date date = dateFormatInput.parse(dateStr);
            
            if (date == null) {
                return dateStr;
            }
            
            // Extraer día
            SimpleDateFormat dayFormat = new SimpleDateFormat("dd", Locale.getDefault());
            String day = dayFormat.format(date);
            
            // Extraer mes en formato texto
            SimpleDateFormat monthFormat = new SimpleDateFormat("MMMM", new Locale("es", "ES"));
            String month = monthFormat.format(date);
            
            // Extraer año
            SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy", Locale.getDefault());
            String year = yearFormat.format(date);
            
            // Formatear la fecha final
            return day + " de " + month + " de " + year;
            
        } catch (ParseException e) {
            // Si hay algún error en el parseo, devolver la fecha original
            return dateStr;
        }
    }
} 