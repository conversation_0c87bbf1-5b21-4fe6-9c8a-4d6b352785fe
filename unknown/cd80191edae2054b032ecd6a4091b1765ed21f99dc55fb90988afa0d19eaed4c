package com.Rages.itatiexplore.models;

/**
 * Clase modelo para representar opciones de transporte
 */
public class Transport {
    private String name;
    private String description;
    private String details;
    private int iconResourceId;
    private String imageUrl;
    private String phone;
    private String whatsapp;
    private String website;
    private String address;
    private String schedule;
    private double price;
    private String additionalInfo; // Campo adicional de la base de datos

    public Transport(String name, String description, String details, int iconResourceId) {
        this.name = name;
        this.description = description;
        this.details = details;
        this.iconResourceId = iconResourceId;
        this.imageUrl = null;
        this.phone = null;
        this.whatsapp = null;
        this.website = null;
        this.address = null;
        this.schedule = null;
        this.price = 0;
        this.additionalInfo = null;
    }

    public Transport(String name, String description, String details, int iconResourceId, String imageUrl) {
        this.name = name;
        this.description = description;
        this.details = details;
        this.iconResourceId = iconResourceId;
        this.imageUrl = imageUrl;
        this.phone = null;
        this.whatsapp = null;
        this.website = null;
        this.address = null;
        this.schedule = null;
        this.price = 0;
        this.additionalInfo = null;
    }
    
    public Transport(String name, String description, String details, int iconResourceId, 
                    String imageUrl, String phone, String website, String address, String schedule) {
        this.name = name;
        this.description = description;
        this.details = details;
        this.iconResourceId = iconResourceId;
        this.imageUrl = imageUrl;
        this.phone = phone;
        this.whatsapp = null;
        this.website = website;
        this.address = address;
        this.schedule = schedule;
        this.price = 0;
        this.additionalInfo = null;
    }
    
    public Transport(String name, String description, String details, int iconResourceId, 
                    String imageUrl, String phone, String whatsapp, String website, 
                    String address, String schedule, double price) {
        this.name = name;
        this.description = description;
        this.details = details;
        this.iconResourceId = iconResourceId;
        this.imageUrl = imageUrl;
        this.phone = phone;
        this.whatsapp = whatsapp;
        this.website = website;
        this.address = address;
        this.schedule = schedule;
        this.price = price;
        this.additionalInfo = null;
    }
    
    public Transport(String name, String description, String details, int iconResourceId, 
                    String imageUrl, String phone, String whatsapp, String website, 
                    String address, String schedule, double price, String additionalInfo) {
        this.name = name;
        this.description = description;
        this.details = details;
        this.iconResourceId = iconResourceId;
        this.imageUrl = imageUrl;
        this.phone = phone;
        this.whatsapp = whatsapp;
        this.website = website;
        this.address = address;
        this.schedule = schedule;
        this.price = price;
        this.additionalInfo = additionalInfo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public int getIconResourceId() {
        return iconResourceId;
    }

    public void setIconResourceId(int iconResourceId) {
        this.iconResourceId = iconResourceId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(String whatsapp) {
        this.whatsapp = whatsapp;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSchedule() {
        return schedule;
    }

    public void setSchedule(String schedule) {
        this.schedule = schedule;
    }
    
    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }
    
    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
    
    /**
     * Devuelve un string con toda la información de contacto disponible
     * Usa iconos HTML para mejor visualización
     */
    public String getContactInfo() {
        StringBuilder info = new StringBuilder();
        
        if (phone != null && !phone.isEmpty()) {
            info.append(phone).append("\n\n");
        }
        
        if (whatsapp != null && !whatsapp.isEmpty()) {
            info.append(whatsapp).append("\n\n");
        }
        
        if (website != null && !website.isEmpty()) {
            info.append(website).append("\n\n");
        }
        
        if (address != null && !address.isEmpty()) {
            info.append(address).append("\n\n");
        }
        
        if (schedule != null && !schedule.isEmpty()) {
            info.append(schedule).append("\n\n");
        }
        
        // Ocultar precio en la información de contacto
        
        return info.toString().trim();
    }
} 