package com.Rages.itatiexplore.utils;

import android.util.Log;

/**
 * Utilidades para el manejo de imágenes en la aplicación
 */
public class ImageUtils {

    private static final String TAG = "ImageUtils";

    /**
     * Obtiene la URL directa de una imagen, útil para manejar URLs de Google Drive
     * @param url URL original de la imagen
     * @return URL directa para acceder a la imagen
     */
    public static String getDirectImageUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        
        try {
            // Log para depuración
            Log.d(TAG, "Procesando URL: " + url);
            
            // Verificar si es una URL de Google Drive
            if (url.contains("drive.google.com") || url.contains("docs.google.com")) {
                Log.d(TAG, "URL de Google Drive detectada");
                
                // Si es una URL de visualización o descarga de Google Drive con formato file/d/
                if (url.contains("file/d/") || url.contains("open?id=") || url.contains("folders/")) {
                    // Extraer el ID del archivo
                    String fileId = extractGoogleDriveFileId(url);
                    if (fileId != null && !fileId.isEmpty()) {
                        Log.d(TAG, "Google Drive FileID extraído: " + fileId);
                        // Crear URL directa para optimizar velocidad y calidad
                        // Usamos sz parámetro para la resolución y cache-control para mejorar caché
                        String directUrl = "https://drive.google.com/uc?export=view&id=" + fileId + "&sz=w800-h800";
                        Log.d(TAG, "URL directa generada: " + directUrl);
                        return directUrl;
                    }
                }
                
                // Si ya es una URL directa con los parámetros correctos
                if ((url.contains("uc?") || url.contains("thumbnail?")) && url.contains("id=")) {
                    // Añadir parámetros de optimización si no los tiene
                    if (!url.contains("sz=")) {
                        if (url.contains("?")) {
                            url += "&sz=w800-h800";
                        } else {
                            url += "?sz=w800-h800";
                        }
                    }
                    Log.d(TAG, "URL de Google Drive optimizada: " + url);
                    return url;
                }
                
                // Para enlaces de compartir comunes
                String fileId = extractGoogleDriveFileId(url);
                if (fileId != null && !fileId.isEmpty()) {
                    String directUrl = "https://drive.google.com/uc?export=view&id=" + fileId + "&sz=w800-h800";
                    Log.d(TAG, "URL directa generada de enlace de compartir: " + directUrl);
                    return directUrl;
                }
            }
            
            // Comprobar si la URL es de Firebase Storage
            if (url.contains("firebasestorage.googleapis.com")) {
                Log.d(TAG, "URL de Firebase Storage detectada");
                return url; // Ya es directa
            }
            
            // Comprobar si la URL es de un proveedor de autenticación
            if (url.contains("googleusercontent.com/a/") || 
                url.contains("lh3.googleusercontent.com") ||
                url.contains("avatars.githubusercontent.com") ||
                url.contains("graph.facebook.com") ||
                url.contains("abs.twimg.com")) {
                Log.d(TAG, "URL de foto de perfil de proveedor de autenticación detectada");
                return url; // Ya es directa
            }
            
            // Para cualquier otra URL
            Log.d(TAG, "Usando URL original sin cambios");
            return url;
        } catch (Exception e) {
            Log.e(TAG, "Error procesando URL: " + e.getMessage());
            // En caso de error, devolver la URL original
            return url;
        }
    }

    /**
     * Extrae el ID de archivo de una URL de Google Drive
     * @param url URL de Google Drive
     * @return ID del archivo
     */
    private static String extractGoogleDriveFileId(String url) {
        try {
            Log.d(TAG, "Intentando extraer ID de Google Drive de: " + url);
            
            // Para URLs como https://drive.google.com/file/d/FILE_ID/view...
            if (url.contains("file/d/")) {
                int startIndex = url.indexOf("file/d/") + 7;
                int endIndex = url.indexOf("/", startIndex);
                if (endIndex > startIndex) {
                    String id = url.substring(startIndex, endIndex);
                    Log.d(TAG, "ID extraído (file/d/): " + id);
                    return id;
                }
            }
            
            // Para URLs como https://drive.google.com/open?id=FILE_ID
            if (url.contains("id=")) {
                int startIndex = url.indexOf("id=") + 3;
                int endIndex = url.indexOf("&", startIndex);
                if (endIndex == -1) {
                    endIndex = url.length();
                }
                if (endIndex > startIndex) {
                    String id = url.substring(startIndex, endIndex);
                    Log.d(TAG, "ID extraído (id=): " + id);
                    return id;
                }
            }
            
            // Para URLs compartidas como https://drive.google.com/uc?export=view&id=FILE_ID
            if (url.contains("export=view&id=")) {
                int startIndex = url.indexOf("id=") + 3;
                int endIndex = url.indexOf("&", startIndex);
                if (endIndex == -1) {
                    endIndex = url.length();
                }
                if (endIndex > startIndex) {
                    String id = url.substring(startIndex, endIndex);
                    Log.d(TAG, "ID extraído (export=view&id=): " + id);
                    return id;
                }
            }
            
            // Para URLs como https://drive.google.com/drive/folders/FILE_ID
            if (url.contains("drive/folders/")) {
                int startIndex = url.indexOf("drive/folders/") + 14;
                int endIndex = url.indexOf("?", startIndex);
                if (endIndex == -1) endIndex = url.indexOf("/", startIndex);
                if (endIndex == -1) endIndex = url.length();
                if (endIndex > startIndex) {
                    String id = url.substring(startIndex, endIndex);
                    Log.d(TAG, "ID extraído (drive/folders/): " + id);
                    return id;
                }
            }
            
            // Para URLs como https://drive.google.com/drive/u/0/folders/FILE_ID
            if (url.contains("drive/u/")) {
                int folderIndex = url.indexOf("folders/");
                if (folderIndex > 0) {
                    int startIndex = folderIndex + 8;
                    int endIndex = url.indexOf("?", startIndex);
                    if (endIndex == -1) endIndex = url.indexOf("/", startIndex);
                    if (endIndex == -1) endIndex = url.length();
                    if (endIndex > startIndex) {
                        String id = url.substring(startIndex, endIndex);
                        Log.d(TAG, "ID extraído (drive/u/X/folders/): " + id);
                        return id;
                    }
                }
            }
            
            // Si no se pudo extraer, registrar y devolver null
            Log.d(TAG, "No se pudo extraer ID de Google Drive: " + url);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error extrayendo ID de Google Drive: " + e.getMessage());
            return null;
        }
    }

    /**
     * Obtiene una URL para miniatura de Google Drive con tamaño optimizado
     * @param url URL original de Google Drive
     * @return URL optimizada para miniaturas
     */
    public static String getThumbnailUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        
        try {
            // Verificar si es una URL de Google Drive
            if (url.contains("drive.google.com") || url.contains("docs.google.com")) {
                String fileId = extractGoogleDriveFileId(url);
                if (fileId != null && !fileId.isEmpty()) {
                    // Usar el endpoint de thumbnail en lugar de uc para miniaturas más rápidas
                    return "https://drive.google.com/thumbnail?id=" + fileId + "&sz=w400";
                }
            }
            
            // Si no es una URL de Google Drive, usar el método estándar
            return getDirectImageUrl(url);
        } catch (Exception e) {
            Log.e(TAG, "Error generando URL de miniatura: " + e.getMessage());
            return url;
        }
    }
}
