package com.Rages.itatiexplore.fragments;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.Rages.itatiexplore.R;
import com.Rages.itatiexplore.adapters.TransportAdapter;
import com.Rages.itatiexplore.models.Transport;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.ListenerRegistration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Fragmento específico para mostrar datos de colectivos.
 * Este fragmento sigue el mismo patrón que TransportTypeFragment para mantener consistencia.
 */
public class BusFragment extends Fragment {
    private static final String TAG = "BusFragment";
    
    private RecyclerView recyclerView;
    private TextView emptyView;
    private TransportAdapter adapter;
    private List<Transport> transportList = new ArrayList<>();
    private FirebaseFirestore db;
    private ListenerRegistration busListener;

    public BusFragment() {
        // Constructor vacío requerido
    }

    public static BusFragment newInstance() {
        return new BusFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Inicializar Firestore
        db = FirebaseFirestore.getInstance();
        Log.d(TAG, "BusFragment creado");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView de BusFragment");
        return inflater.inflate(R.layout.fragment_transport_type, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        try {
            Log.d(TAG, "onViewCreated de BusFragment - Inicializando vistas");
            
            // Inicializar RecyclerView
            recyclerView = view.findViewById(R.id.rv_transport_items);
            recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
            
            // Inicializar adaptador con lista vacía
            transportList = new ArrayList<>();
            adapter = new TransportAdapter(transportList);
            recyclerView.setAdapter(adapter);
            
            // Configurar vista vacía
            emptyView = view.findViewById(R.id.empty_view);
            View emptyContainer = view.findViewById(R.id.empty_container);
            emptyView.setText("Cargando opciones de colectivo...");
            
            if (emptyContainer != null) {
                emptyContainer.setVisibility(View.VISIBLE);
            } else {
                emptyView.setVisibility(View.VISIBLE);
            }
            
            recyclerView.setVisibility(View.GONE);
            
            // Cargar datos específicos de colectivos
            loadBusData();
            
        } catch (Exception e) {
            Log.e(TAG, "Error al inicializar lista de colectivos", e);
            showError("Error al cargar opciones de colectivo");
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // Recargar datos cada vez que el fragmento se muestra
        if (transportList.isEmpty()) {
            loadBusData();
        }
    }
    
    @Override
    public void onDestroyView() {
        // Eliminar el listener cuando el fragmento se destruye
        if (busListener != null) {
            Log.d(TAG, "Eliminando listener de colectivo");
            busListener.remove();
            busListener = null;
        }
        super.onDestroyView();
    }
    
    private void loadBusData() {
        Log.d(TAG, "INICIANDO CARGA DE DATOS DE COLECTIVO");
        
        // Reinicializar la instancia de Firestore para esta sesión
        db = FirebaseFirestore.getInstance();
        
        // Limpiar la lista actual
        if (transportList != null) {
            transportList.clear();
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        }
        
        // Mostrar mensaje de carga
        if (emptyView != null) {
            emptyView.setText("Cargando datos de colectivo...");
            View emptyContainer = getView() != null ? getView().findViewById(R.id.empty_container) : null;
            if (emptyContainer != null) {
                emptyContainer.setVisibility(View.VISIBLE);
            } else {
                emptyView.setVisibility(View.VISIBLE);
            }
        }
        
        if (recyclerView != null) {
            recyclerView.setVisibility(View.GONE);
        }
        
        // Cancelar cualquier listener anterior si existe
        if (busListener != null) {
            busListener.remove();
            busListener = null;
        }
        
        // Intentar la ruta específica para colectivo
        tryBusPath();
    }
    
    /**
     * Intenta cargar datos de colectivo desde la ruta específica
     */
    private void tryBusPath() {
        Log.d(TAG, "Intentando cargar desde la ruta específica: /transporte/colectivo/items");
        
        // Primero verificamos si existe el documento colectivo
        db.collection("transporte").document("colectivo").get()
            .addOnSuccessListener(documentSnapshot -> {
                if (documentSnapshot.exists()) {
                    Log.d(TAG, "Documento colectivo existe: " + documentSnapshot.getData());
                    
                    // Verificar si tiene una subcolección items
                    db.collection("transporte").document("colectivo").collection("items").get()
                        .addOnSuccessListener(itemsSnapshot -> {
                            Log.d(TAG, "Subcolección items encontrada con " + itemsSnapshot.size() + " documentos");
                            
                            if (itemsSnapshot.isEmpty()) {
                                Log.d(TAG, "La subcolección items está vacía");
                                tryAlternativeBusPath();
                                return;
                            }
                            
                            // Configurar listener en tiempo real
                            setupBusListener();
                        })
                        .addOnFailureListener(e -> {
                            Log.e(TAG, "Error al verificar subcolección items", e);
                            tryAlternativeBusPath();
                        });
                } else {
                    Log.d(TAG, "Documento colectivo no existe");
                    tryAlternativeBusPath();
                }
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al verificar documento colectivo", e);
                tryAlternativeBusPath();
            });
    }
    
    /**
     * Configura un listener en tiempo real para la colección de colectivos
     */
    private void setupBusListener() {
        Log.d(TAG, "Configurando listener en tiempo real para colectivos");
        
        if (busListener != null) {
            busListener.remove();
        }
        
        busListener = db.collection("transporte").document("colectivo").collection("items")
            .addSnapshotListener((querySnapshot, e) -> {
                if (e != null) {
                    Log.e(TAG, "Error al escuchar cambios en colectivos", e);
                    return;
                }
                
                if (querySnapshot == null || querySnapshot.isEmpty()) {
                    Log.d(TAG, "No hay datos en la colección de colectivos");
                    showError("No hay opciones de colectivo disponibles");
                    return;
                }
                
                Log.d(TAG, "Datos actualizados en colectivos: " + querySnapshot.size() + " items");
                processBusItems(querySnapshot);
            });
    }
    
    /**
     * Procesa los items de colectivo encontrados
     */
    private void processBusItems(com.google.firebase.firestore.QuerySnapshot querySnapshot) {
        Log.d(TAG, "Procesando " + querySnapshot.size() + " items de colectivo");
        transportList.clear();
        
        for (QueryDocumentSnapshot document : querySnapshot) {
            try {
                // Extraer datos según la estructura
                String nombre = document.getString("nombre");
                String descripcion = document.getString("descripcion");
                String imagenUrl = document.getString("imagenUrl");
                
                // Verificar si el documento está activo
                Boolean isActive = document.getBoolean("activo");
                if (isActive != null && !isActive) {
                    Log.d(TAG, "Documento inactivo, saltando: " + document.getId());
                    continue; // Saltar este documento si no está activo
                }
                
                // Obtener detalles
                String detalles = document.getString("detalles");
                if (detalles == null) {
                    detalles = descripcion;
                }
                
                // Obtener teléfono
                String telefono = document.getString("telefono");
                
                // Obtener WhatsApp
                String whatsapp = document.getString("whatsapp_number");
                if (whatsapp == null) {
                    whatsapp = document.getString("whatsapp");
                }
                
                // Obtener horarios como array, ordenarlos y formatearlos
                List<String> horariosArray = (List<String>) document.get("horarios");
                String horarios = null;
                if (horariosArray != null && !horariosArray.isEmpty()) {
                    // Ordenar los horarios de menor a mayor
                    Collections.sort(horariosArray, new Comparator<String>() {
                        @Override
                        public int compare(String hora1, String hora2) {
                            return hora1.compareTo(hora2);
                        }
                    });
                    
                    // Formatear los horarios en filas de 6 máximo
                    StringBuilder sb = new StringBuilder();
                    int count = 0;
                    for (String hora : horariosArray) {
                        if (count > 0 && count % 6 == 0) {
                            sb.append("\n");
                        } else if (count > 0) {
                            sb.append(" - ");
                        }
                        sb.append(hora);
                        count++;
                    }
                    horarios = sb.toString();
                }
                
                // Obtener precio
                double precio = 0;
                if (document.contains("precio")) {
                    Number precioNum = document.getDouble("precio");
                    if (precioNum != null) {
                        precio = precioNum.doubleValue();
                    }
                }
                
                // Crear objeto de transporte
                Transport transport = new Transport(
                    nombre != null ? nombre : "Colectivo " + document.getId(),
                    descripcion != null ? descripcion : "Servicio de colectivo",
                    detalles != null ? detalles : "Servicio de transporte en colectivo",
                    R.drawable.ic_transport,
                    imagenUrl
                );
                
                // Establecer horarios
                transport.setSchedule(horarios);
                
                // Establecer precio
                transport.setPrice(precio);
                
                // Establecer teléfono
                if (telefono != null && !telefono.isEmpty()) {
                    transport.setPhone(telefono);
                }
                
                // Establecer WhatsApp
                if (whatsapp != null && !whatsapp.isEmpty()) {
                    transport.setWhatsapp(whatsapp);
                }
                
                // Añadir a la lista
                transportList.add(transport);
                Log.d(TAG, "Colectivo añadido a la lista: " + nombre);
                
            } catch (Exception e) {
                Log.e(TAG, "Error procesando item de colectivo: " + document.getId(), e);
            }
        }
        
        // Actualizar la interfaz
        updateUI();
    }
    
    /**
     * Intenta rutas alternativas para colectivo
     */
    private void tryAlternativeBusPath() {
        Log.d(TAG, "Intentando rutas alternativas para colectivo");
        
        // Intentar con la colección directa de colectivo
        db.collection("colectivo").get()
            .addOnSuccessListener(colectivoSnapshot -> {
                if (!colectivoSnapshot.isEmpty()) {
                    Log.d(TAG, "Colección directa colectivo encontrada con " + colectivoSnapshot.size() + " documentos");
                    
                    // Configurar listener en la colección directa
                    if (busListener != null) {
                        busListener.remove();
                    }
                    
                    busListener = db.collection("colectivo")
                        .addSnapshotListener((querySnapshot, e) -> {
                            if (e != null) {
                                Log.e(TAG, "Error al escuchar cambios en colección directa colectivo", e);
                                return;
                            }
                            
                            if (querySnapshot == null || querySnapshot.isEmpty()) {
                                Log.d(TAG, "No hay datos en colección directa colectivo");
                                return;
                            }
                            
                            Log.d(TAG, "Datos actualizados en colección directa colectivo: " + querySnapshot.size());
                            processBusItems(querySnapshot);
                        });
                    
                    // Procesar los datos encontrados
                    processBusItems(colectivoSnapshot);
                } else {
                    Log.d(TAG, "No se encontró colección directa colectivo");
                    
                    // Intentar con la colección transporte y filtrar por tipo
                    db.collection("transporte")
                        .whereEqualTo("tipo", "colectivo")
                        .get()
                        .addOnSuccessListener(filteredSnapshot -> {
                            if (!filteredSnapshot.isEmpty()) {
                                Log.d(TAG, "Encontrados " + filteredSnapshot.size() + " documentos con tipo=colectivo");
                                
                                // Configurar listener con filtro
                                if (busListener != null) {
                                    busListener.remove();
                                }
                                
                                busListener = db.collection("transporte")
                                    .whereEqualTo("tipo", "colectivo")
                                    .addSnapshotListener((querySnapshot, e) -> {
                                        if (e != null) {
                                            Log.e(TAG, "Error al escuchar cambios en documentos filtrados", e);
                                            return;
                                        }
                                        
                                        if (querySnapshot == null || querySnapshot.isEmpty()) {
                                            Log.d(TAG, "No hay datos en documentos filtrados");
                                            return;
                                        }
                                        
                                        Log.d(TAG, "Datos actualizados en documentos filtrados: " + querySnapshot.size());
                                        processBusItems(querySnapshot);
                                    });
                                
                                // Procesar los datos encontrados
                                processBusItems(filteredSnapshot);
                            } else {
                                Log.d(TAG, "No se encontraron documentos con tipo=colectivo");
                                
                                // Último intento - crear datos genéricos
                                createGenericBusData();
                            }
                        })
                        .addOnFailureListener(e -> {
                            Log.e(TAG, "Error al buscar documentos con tipo=colectivo", e);
                            createGenericBusData();
                        });
                }
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al buscar en colección directa colectivo", e);
                createGenericBusData();
            });
    }
    
    /**
     * Crea datos genéricos de colectivo como último recurso
     */
    private void createGenericBusData() {
        Log.d(TAG, "Creando datos genéricos de colectivo como último recurso");
        
        transportList.clear();
        
        // Crear algunos datos genéricos de colectivo
        Transport transport1 = new Transport(
            "Colectivo Itatí - Corrientes",
            "Servicio regular de colectivo",
            "Horarios:\n06:00 - 08:00 - 10:00 - 12:00 - 14:00 - 16:00 - 18:00 - 20:00",
            R.drawable.ic_transport
        );
        transport1.setPhone("+5493777123456");
        transport1.setSchedule("06:00 - 08:00 - 10:00 - 12:00 - 14:00 - 16:00 - 18:00 - 20:00");
        transport1.setPrice(500.0);
        
        Transport transport2 = new Transport(
            "Colectivo Itatí - Resistencia",
            "Servicio regular de colectivo",
            "Horarios:\n07:00 - 09:00 - 11:00 - 13:00 - 15:00 - 17:00 - 19:00",
            R.drawable.ic_transport
        );
        transport2.setPhone("+5493777654321");
        transport2.setSchedule("07:00 - 09:00 - 11:00 - 13:00 - 15:00 - 17:00 - 19:00");
        transport2.setPrice(600.0);
        
        transportList.add(transport1);
        transportList.add(transport2);
        
        updateUI();
    }
    
    /**
     * Actualiza la interfaz de usuario con los datos cargados
     */
    private void updateUI() {
        if (getActivity() == null) return;
        
        getActivity().runOnUiThread(() -> {
            if (transportList.isEmpty()) {
                if (emptyView != null) {
                    emptyView.setText("No hay opciones de colectivo disponibles");
                    emptyView.setVisibility(View.VISIBLE);
                }
                if (recyclerView != null) {
                    recyclerView.setVisibility(View.GONE);
                }
            } else {
                if (adapter != null) {
                    adapter.notifyDataSetChanged();
                }
                if (emptyView != null) {
                    emptyView.setVisibility(View.GONE);
                }
                if (recyclerView != null) {
                    recyclerView.setVisibility(View.VISIBLE);
                }
            }
        });
    }
    
    /**
     * Muestra un mensaje de error
     */
    private void showError(String message) {
        if (getActivity() == null) return;
        
        getActivity().runOnUiThread(() -> {
            if (emptyView != null) {
                emptyView.setText(message);
                emptyView.setVisibility(View.VISIBLE);
            }
            if (recyclerView != null) {
                recyclerView.setVisibility(View.GONE);
            }
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        });
    }
}