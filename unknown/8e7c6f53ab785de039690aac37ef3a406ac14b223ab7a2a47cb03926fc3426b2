package com.Rages.itatiexplore.utils;

import android.graphics.Color;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.osmdroid.util.GeoPoint;
import org.osmdroid.views.MapView;
import org.osmdroid.views.overlay.Polyline;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class MapboxDirectionsClient {
    private static final String TAG = "MapboxDirectionsClient";
    private static final String BASE_URL = "https://api.mapbox.com/directions/v5/mapbox/";
    private final String accessToken;
    private final OkHttpClient client;

    public interface DirectionsCallback {
        void onDirectionsSuccess(Polyline route);
        void onDirectionsFailure(String errorMessage);
    }

    public MapboxDirectionsClient(String accessToken) {
        this.accessToken = accessToken;
        this.client = new OkHttpClient();
    }

    /**
     * Solicita una ruta desde el origen hasta el destino
     * @param profile perfil de navegación (walking, driving, cycling)
     * @param origin punto de origen
     * @param destination punto de destino
     * @param callback callback para recibir el resultado
     */
    public void getDirections(String profile, GeoPoint origin, GeoPoint destination, DirectionsCallback callback) {
        // Construir URL
        String url = BASE_URL + profile + "/" + 
                origin.getLongitude() + "," + origin.getLatitude() + ";" +
                destination.getLongitude() + "," + destination.getLatitude() +
                "?alternatives=true&annotations=distance&continue_straight=true" +
                "&geometries=polyline6&overview=full&steps=false&access_token=" + accessToken;
        
        // Crear solicitud
        Request request = new Request.Builder()
                .url(url)
                .build();

        // Ejecutar solicitud asíncrona
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Error al obtener direcciones", e);
                callback.onDirectionsFailure("Error de conexión: " + e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    callback.onDirectionsFailure("Error en la respuesta: " + response.code());
                    return;
                }

                try {
                    String responseData = response.body().string();
                    JSONObject jsonResponse = new JSONObject(responseData);
                    
                    // Verificar si hay rutas disponibles
                    if (!jsonResponse.has("routes") || jsonResponse.getJSONArray("routes").length() == 0) {
                        callback.onDirectionsFailure("No se encontraron rutas");
                        return;
                    }

                    // Obtener la primera ruta
                    JSONObject route = jsonResponse.getJSONArray("routes").getJSONObject(0);
                    String geometry = route.getString("geometry");
                    
                    // Decodificar la geometría polyline6
                    List<GeoPoint> points = decodePolyline(geometry, 1e6);
                    
                    // Crear la línea para dibujar en el mapa
                    Polyline routeLine = new Polyline();
                    routeLine.setPoints(points);
                    routeLine.setColor(Color.parseColor("#4285F4")); // Azul Google Maps
                    routeLine.setWidth(14f); // Ancho de la línea
                    routeLine.setGeodesic(true);
                    
                    // Devolver la línea a través del callback
                    callback.onDirectionsSuccess(routeLine);
                    
                } catch (JSONException e) {
                    Log.e(TAG, "Error al procesar JSON de direcciones", e);
                    callback.onDirectionsFailure("Error al procesar datos: " + e.getMessage());
                }
            }
        });
    }

    /**
     * Decodifica una cadena polyline en una lista de puntos
     * Basado en: https://github.com/mapbox/polyline/blob/master/src/polyline.js
     */
    private List<GeoPoint> decodePolyline(String encoded, double precision) {
        List<GeoPoint> points = new ArrayList<>();
        int index = 0;
        int lat = 0;
        int lng = 0;
        int shift, result;
        int byte_;
        int factor = (int) Math.pow(10, 5); // Valor predeterminado para polyline precisión 5
        
        if (precision == 1e6) {
            factor = (int) Math.pow(10, 6); // Para polyline6
        }

        int len = encoded.length();
        while (index < len) {
            // Latitud
            shift = 0;
            result = 0;
            do {
                byte_ = encoded.charAt(index++) - 63;
                result |= (byte_ & 0x1f) << shift;
                shift += 5;
            } while (byte_ >= 0x20);
            int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
            lat += dlat;

            // Longitud
            shift = 0;
            result = 0;
            do {
                byte_ = encoded.charAt(index++) - 63;
                result |= (byte_ & 0x1f) << shift;
                shift += 5;
            } while (byte_ >= 0x20);
            int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
            lng += dlng;

            double finalLat = lat / precision;
            double finalLng = lng / precision;

            points.add(new GeoPoint(finalLat, finalLng));
        }
        return points;
    }
} 