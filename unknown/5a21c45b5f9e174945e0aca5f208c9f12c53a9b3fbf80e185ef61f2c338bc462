package com.Rages.itatiexplore.models;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Clase modelo para representar comentarios de los usuarios
 */
public class Comment {
    private String userId;
    private String userName;
    private String text;
    private long timestamp;
    private float rating; // Calificación de 0 a 5 estrellas
    private String location; // Ubicación del comentario
    private String photoUrl; // URL de la foto de perfil del usuario
    private String visitType; // Tipo de visita (turismo religioso, cultural, etc.)
    private String visitDate; // Fecha de la visita
    private boolean recommends; // Si recomienda el lugar
    private String positiveHighlight; // Aspecto positivo destacado
    private String negativeHighlight; // Aspecto negativo destacado
    private double latitud; // Latitud de la ubicación
    private double longitud; // Longitud de la ubicación 
    private Date fecha; // Fecha del comentario
    private String comentario; // Contenido del comentario (alias para text)
    private String ubicacion; // Ubicación del comentario (alias para location)
    private String usuario; // Usuario que hizo el comentario (alias para userName)
    private int likes = 0; // Contador de likes
    private String commentId; // ID único del comentario

    public Comment() {
        // Constructor vacío requerido para Firebase
    }

    public Comment(String userId, String userName, String text, long timestamp, float rating) {
        this.userId = userId;
        this.userName = userName;
        this.text = text;
        this.timestamp = timestamp;
        this.rating = rating;
        this.location = "";
        this.photoUrl = "";
    }
    
    public Comment(String userId, String userName, String text, long timestamp, float rating, String location) {
        this.userId = userId;
        this.userName = userName;
        this.text = text;
        this.timestamp = timestamp;
        this.rating = rating;
        this.location = location;
        this.photoUrl = "";
    }
    
    public Comment(String userId, String userName, String text, long timestamp, float rating, String location, String photoUrl) {
        this.userId = userId;
        this.userName = userName;
        this.text = text;
        this.timestamp = timestamp;
        this.rating = rating;
        this.location = location;
        this.photoUrl = photoUrl;
        this.visitType = "Turismo religioso"; // Valor por defecto
        this.visitDate = "";
        this.recommends = true; // Por defecto, se asume que recomienda
        this.positiveHighlight = "";
        this.negativeHighlight = "";
    }
    
    public Comment(String userId, String userName, String text, long timestamp, float rating, 
                  String location, String photoUrl, String visitType, String visitDate, 
                  boolean recommends, String positiveHighlight, String negativeHighlight) {
        this.userId = userId;
        this.userName = userName;
        this.text = text;
        this.timestamp = timestamp;
        this.rating = rating;
        this.location = location;
        this.photoUrl = photoUrl;
        this.visitType = visitType;
        this.visitDate = visitDate;
        this.recommends = recommends;
        this.positiveHighlight = positiveHighlight;
        this.negativeHighlight = negativeHighlight;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    // Alias para setUserId para compatibilidad
    public void setId(String id) {
        this.userId = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public float getRating() {
        return rating;
    }
    
    public void setRating(float rating) {
        this.rating = rating;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getPhotoUrl() {
        return photoUrl;
    }
    
    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }
    
    public String getVisitType() {
        return visitType;
    }
    
    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }
    
    public String getVisitDate() {
        return visitDate;
    }
    
    public void setVisitDate(String visitDate) {
        this.visitDate = visitDate;
    }
    
    public boolean isRecommends() {
        return recommends;
    }
    
    public void setRecommends(boolean recommends) {
        this.recommends = recommends;
    }
    
    public String getPositiveHighlight() {
        return positiveHighlight;
    }
    
    public void setPositiveHighlight(String positiveHighlight) {
        this.positiveHighlight = positiveHighlight;
    }
    
    // Alias para setPositiveHighlight para compatibilidad
    public void setPositiveAspect(String positiveAspect) {
        this.positiveHighlight = positiveAspect;
    }
    
    public String getNegativeHighlight() {
        return negativeHighlight;
    }
    
    public void setNegativeHighlight(String negativeHighlight) {
        this.negativeHighlight = negativeHighlight;
    }
    
    // Alias para setNegativeHighlight para compatibilidad
    public void setNegativeAspect(String negativeAspect) {
        this.negativeHighlight = negativeAspect;
    }
    
    /**
     * Devuelve la fecha formateada para mostrar
     */
    public String getFormattedDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(timestamp));
    }
    
    // Método para establecer la fecha directamente
    public void setDate(Date date) {
        setFecha(date);
    }
    
    /**
     * Devuelve un texto formateado para la fecha de visita
     */
    public String getFormattedVisitDate() {
        if (visitDate == null || visitDate.isEmpty()) {
            return "";
        }
        return "Visitó en " + visitDate;
    }

    // Nuevos getters y setters para los campos adicionales
    public double getLatitud() {
        return latitud;
    }
    
    public void setLatitud(double latitud) {
        this.latitud = latitud;
    }
    
    public double getLongitud() {
        return longitud;
    }
    
    public void setLongitud(double longitud) {
        this.longitud = longitud;
    }
    
    public Date getFecha() {
        return fecha;
    }
    
    public void setFecha(Date fecha) {
        this.fecha = fecha;
        // Actualizar también el timestamp para compatibilidad
        if (fecha != null) {
            this.timestamp = fecha.getTime();
        }
    }
    
    public String getComentario() {
        return comentario != null ? comentario : text;
    }
    
    public void setComentario(String comentario) {
        this.comentario = comentario;
        // Actualizar también el campo text para compatibilidad
        this.text = comentario;
    }
    
    public String getUbicacion() {
        return ubicacion != null ? ubicacion : location;
    }
    
    public void setUbicacion(String ubicacion) {
        this.ubicacion = ubicacion;
        // Actualizar también el campo location para compatibilidad
        this.location = ubicacion;
    }
    
    public String getUsuario() {
        return usuario != null ? usuario : userName;
    }
    
    public void setUsuario(String usuario) {
        this.usuario = usuario;
        // Actualizar también el campo userName para compatibilidad
        this.userName = usuario;
    }
    
    public int getLikes() {
        return likes;
    }
    
    public void setLikes(int likes) {
        this.likes = likes;
    }
    
    /**
     * Incrementa el contador de likes en 1
     */
    public void incrementLikes() {
        this.likes++;
    }
    
    /**
     * Decrementa el contador de likes en 1 (si es mayor que 0)
     */
    public void decrementLikes() {
        if (this.likes > 0) {
            this.likes--;
        }
    }

    // Getters y setters para commentId
    public String getCommentId() {
        return commentId;
    }
    
    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }
} 