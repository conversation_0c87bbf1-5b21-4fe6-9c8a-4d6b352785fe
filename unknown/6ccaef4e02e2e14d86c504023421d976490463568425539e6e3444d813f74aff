package com.Rages.itatiexplore.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.Rages.itatiexplore.R;
import com.Rages.itatiexplore.models.Location;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.Rages.itatiexplore.utils.ImageUtils;

import java.util.List;

/**
 * Adaptador para mostrar lugares turísticos en un RecyclerView
 */
public class LocationAdapter extends RecyclerView.Adapter<LocationAdapter.LocationViewHolder> {

    private final List<Location> locations;
    private final Context context;
    private final OnLocationClickListener listener;

    /**
     * Interface para manejar clics en los elementos
     */
    public interface OnLocationClickListener {
        void onLocationClick(Location location);
        void onViewDetailsClick(Location location);
    }

    /**
     * Constructor del adaptador
     */
    public LocationAdapter(Context context, List<Location> locations, OnLocationClickListener listener) {
        this.context = context;
        this.locations = locations;
        this.listener = listener;
    }

    @NonNull
    @Override
    public LocationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_location, parent, false);
        return new LocationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull LocationViewHolder holder, int position) {
        Location location = locations.get(position);
        holder.bind(location, listener);
    }

    @Override
    public int getItemCount() {
        return locations.size();
    }

    /**
     * ViewHolder para los elementos de lugar turístico
     */
    static class LocationViewHolder extends RecyclerView.ViewHolder {
        private final ImageView locationImage;
        private final TextView locationName;
        private final TextView locationAddress;
        private final TextView locationHours;
        private final TextView locationDescription;
        private final Chip categoryChip;
        private final MaterialButton btnViewDetails;

        public LocationViewHolder(@NonNull View itemView) {
            super(itemView);
            locationImage = itemView.findViewById(R.id.location_image);
            locationName = itemView.findViewById(R.id.location_name);
            locationAddress = itemView.findViewById(R.id.location_address);
            locationHours = itemView.findViewById(R.id.location_hours);
            locationDescription = itemView.findViewById(R.id.location_description);
            categoryChip = itemView.findViewById(R.id.location_category_chip);
            btnViewDetails = itemView.findViewById(R.id.btn_view_details);
        }

        /**
         * Vincula los datos del lugar con los elementos de la vista
         */
        public void bind(final Location location, final OnLocationClickListener listener) {
            // Configurar nombre y categoría
            locationName.setText(location.getName());
            categoryChip.setText(location.getCategory());
            
            // Configurar dirección y horarios
            locationAddress.setText(location.getAddress());
            locationHours.setText(location.getHours());
            
            // Configurar descripción
            locationDescription.setText(location.getDescription());
            
            // Cargar imagen con Glide y aplicar transformaciones
            if (location.getImageUrl() != null && !location.getImageUrl().isEmpty()) {
                // Convertir URL si es de Google Drive
                String directUrl = ImageUtils.getDirectImageUrl(location.getImageUrl());
                android.util.Log.d("LocationAdapter", "Cargando imagen: " + location.getImageUrl());
                android.util.Log.d("LocationAdapter", "URL procesada: " + directUrl);
                
                // Aplicar transformaciones: centrar y recortar la imagen
                RequestOptions requestOptions = new RequestOptions()
                        .placeholder(R.drawable.ic_location)
                        .error(R.drawable.ic_location)
                        .transform(new CenterCrop());
                
                Glide.with(itemView.getContext())
                        .load(directUrl)
                        .apply(requestOptions)
                        .into(locationImage);
            } else {
                // Usar imagen por defecto si no hay URL
                locationImage.setImageResource(R.drawable.ic_location);
            }
            
            // Configurar listeners de clic
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onLocationClick(location);
                }
            });
            
            btnViewDetails.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onViewDetailsClick(location);
                }
            });
        }
    }
}