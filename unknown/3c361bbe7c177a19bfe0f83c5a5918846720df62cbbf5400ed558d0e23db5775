package com.Rages.itatiexplore.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.Rages.itatiexplore.R;

/**
 * Un fragmento vacío para pruebas
 */
public class EmptyFragment extends Fragment {

    public EmptyFragment() {
        // Constructor vacío requerido
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Crear un TextView simple
        TextView textView = new TextView(getActivity());
        textView.setText("Fragmento de prueba");
        textView.setPadding(20, 20, 20, 20);
        textView.setTextSize(24);
        
        return textView;
    }
}