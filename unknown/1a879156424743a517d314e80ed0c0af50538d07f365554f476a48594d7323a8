package com.Rages.itatiexplore.fragments;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.RatingBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import de.hdodenhof.circleimageview.CircleImageView;

import com.Rages.itatiexplore.R;
import com.Rages.itatiexplore.adapters.CommentAdapter;
import com.Rages.itatiexplore.adapters.PlaceSpinnerAdapter;
import com.Rages.itatiexplore.models.Comment;
import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.common.SignInButton;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.AuthCredential;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.auth.GoogleAuthProvider;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.Query;
import com.google.firebase.firestore.SetOptions;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.Locale;

import com.Rages.itatiexplore.utils.ImageUtils;

public class CommentsFragment extends Fragment {

    private static final String TAG = "CommentsFragment";
    private static final int RC_SIGN_IN = 9001;
    private static final int REQUEST_LOCATION_PERMISSION = 1002;
    private static final float NEARBY_RADIUS_METERS = 100.0f; // Radio de 100 metros para lugares cercanos
    
    // Constantes para filtros
    private static final int FILTER_ALL = 0;
    private static final int FILTER_RECENT = 1;
    private static final int FILTER_HIGHEST_RATING = 2;
    private static final int FILTER_LOWEST_RATING = 3;
    
    // Categorías
    private static final int CATEGORY_BASILICA = 10;
    private static final int CATEGORY_PLAZA = 11;
    private static final int CATEGORY_MIRADOR = 12;
    private static final int CATEGORY_RESTAURANTE = 13;
    private static final int CATEGORY_HOTEL = 14;
    private static final int CATEGORY_MUSEO = 15;
    
    private RecyclerView recyclerView;
    private CommentAdapter adapter;
    private List<Comment> commentList;
    private List<Comment> filteredCommentList;
    private EditText commentEditText;
    private Button gpsButton;
    private Button submitButton;
    private LinearLayout commentLayout;
    private LinearLayout signInLayout;
    private SignInButton signInButton;
    private TextView userInfoText;
    private RatingBar ratingBar;
    private int currentFilter = FILTER_RECENT;
    
    // Nuevos componentes para manejo de ubicación y marcadores
    private LocationManager locationManager;
    private Spinner spinnerLocation;
    private List<PlaceSpinnerAdapter.PlaceItem> nearbyPlaces = new ArrayList<>();
    private PlaceSpinnerAdapter placesAdapter;
    private double currentLat = 0;
    private double currentLng = 0;
    
    private FirebaseAuth firebaseAuth;
    private GoogleSignInClient googleSignInClient;
    private FirebaseFirestore db;

    private String selectedLocationName; // Para almacenar el nombre del lugar seleccionado
    private double selectedLocationLat;
    private double selectedLocationLng;

    public CommentsFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Obtener argumentos si existen
        Bundle args = getArguments();
        if (args != null) {
            selectedLocationName = args.getString("locationName", "");
            selectedLocationLat = args.getDouble("latitude", 0);
            selectedLocationLng = args.getDouble("longitude", 0);
            
            // Log para depuración
            Log.d(TAG, "CommentsFragment creado con ubicación: " + selectedLocationName + 
                 " (" + selectedLocationLat + ", " + selectedLocationLng + ")");
        }
        
        // Configurar Firebase Auth
        firebaseAuth = FirebaseAuth.getInstance();
        
        // Configurar Google Sign-In
        GoogleSignInOptions gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken(getString(R.string.default_web_client_id))
                .requestEmail()
                .build();
        
        googleSignInClient = GoogleSignIn.getClient(requireActivity(), gso);
        
        // Inicializar Firestore
        db = FirebaseFirestore.getInstance();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        try {
            // Inflate the layout for this fragment
            return inflater.inflate(R.layout.fragment_comments, container, false);
        } catch (Exception e) {
            Log.e(TAG, "Error al crear vista del fragmento", e);
            Toast.makeText(getContext(), "Error al cargar comentarios", Toast.LENGTH_SHORT).show();
            return inflater.inflate(R.layout.fragment_error, container, false);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        try {
            // Inicializar listas
            commentList = new ArrayList<>();
            filteredCommentList = new ArrayList<>();
            
            // Inicializar RecyclerView
            recyclerView = view.findViewById(R.id.comments_recycler_view);
            recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
            
            // Crear adaptador con listener para likes y opciones
            adapter = new CommentAdapter(filteredCommentList, new CommentAdapter.OnCommentActionListener() {
                @Override
                public void onLikeClicked(Comment comment, int position) {
                    handleLikeAction(comment, position);
                }
                
                @Override
                public void onOptionsClicked(Comment comment, int position, View view) {
                    showCommentOptions(comment, position, view);
                }
            });
            recyclerView.setAdapter(adapter);
            
            // Inicializar vistas
            commentEditText = view.findViewById(R.id.comment_edit_text);
            ratingBar = view.findViewById(R.id.rating_bar);
            gpsButton = view.findViewById(R.id.gps_button);
            submitButton = view.findViewById(R.id.submit_button);
            commentLayout = view.findViewById(R.id.comment_layout);
            signInLayout = view.findViewById(R.id.sign_in_layout);
            signInButton = view.findViewById(R.id.sign_in_button);
            userInfoText = view.findViewById(R.id.user_info_text);
            
            // Inicializar LocationManager
            locationManager = (LocationManager) requireContext().getSystemService(Context.LOCATION_SERVICE);
            
            // Inicializar Firebase Auth
            firebaseAuth = FirebaseAuth.getInstance();
            
            // Configurar el inicio de sesión de Google
            GoogleSignInOptions gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                    .requestIdToken(getString(R.string.default_web_client_id))
                    .requestEmail()
                    .build();
            googleSignInClient = GoogleSignIn.getClient(requireActivity(), gso);
            
            // Configurar botón de inicio de sesión
            signInButton.setOnClickListener(v -> signIn());
            
            // Configurar botón de enviar comentario
            if (submitButton != null) {
                submitButton.setOnClickListener(v -> {
                    // Guardar el estado de todos los elementos de UI antes de enviar
                    saveUIState();
                    submitComment();
                });
            }
            
            // Inicializar Firestore
            db = FirebaseFirestore.getInstance();
            
            // Inicializar el spinner de ubicaciones
            spinnerLocation = view.findViewById(R.id.spinner_location);
            nearbyPlaces = new ArrayList<>();
            placesAdapter = new PlaceSpinnerAdapter(requireContext(), nearbyPlaces);
            spinnerLocation.setAdapter(placesAdapter);
            
            // Obtener referencia al icono de categoría
            ImageView locationCategoryIcon = view.findViewById(R.id.location_category_icon);
            
            // Configurar listener para el spinner
            spinnerLocation.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    // Obtener el elemento seleccionado
                    PlaceSpinnerAdapter.PlaceItem selectedPlace = (PlaceSpinnerAdapter.PlaceItem) parent.getItemAtPosition(position);
                    
                    // Actualizar el icono según la categoría
                    if (selectedPlace != null && locationCategoryIcon != null) {
                        int iconResId = getCategoryIcon(selectedPlace.category);
                        locationCategoryIcon.setImageResource(iconResId);
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // No hacer nada
                }
            });
            
            // Cargar marcadores disponibles
            loadMarkersFromFirestore();
            
            // Configurar botón GPS para ubicación actual
            gpsButton.setOnClickListener(v -> {
                // Verificar permisos
                if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    requestLocationPermission();
                    return;
                }
                
                // Obtener ubicación
                getCurrentLocation();
                
                // Mostrar mensaje
                Toast.makeText(getContext(), "Buscando lugares cercanos...", Toast.LENGTH_SHORT).show();
            });
            
            // Configurar chips de filtro
            View chipAll = view.findViewById(R.id.chip_all);
            View chipBasilica = view.findViewById(R.id.chip_basilica);
            View chipPlaza = view.findViewById(R.id.chip_plaza);
            View chipMirador = view.findViewById(R.id.chip_mirador);
            View chipRestaurante = view.findViewById(R.id.chip_restaurante);
            View chipHotel = view.findViewById(R.id.chip_hotel);
            View btnMoreFilters = view.findViewById(R.id.btn_more_filters);
            
            // Configurar listener para cada chip
            chipAll.setOnClickListener(v -> {
                currentFilter = FILTER_ALL;
                setActiveChip(view, R.id.chip_all);
                applyFilter();
            });
            
            chipBasilica.setOnClickListener(v -> {
                currentFilter = CATEGORY_BASILICA;
                setActiveChip(view, R.id.chip_basilica);
                filterByCategory("Basílica");
                adapter.notifyDataSetChanged();
            });
            
            chipPlaza.setOnClickListener(v -> {
                currentFilter = CATEGORY_PLAZA;
                setActiveChip(view, R.id.chip_plaza);
                filterByCategory("Plaza");
                adapter.notifyDataSetChanged();
            });
            
            chipMirador.setOnClickListener(v -> {
                currentFilter = CATEGORY_MIRADOR;
                setActiveChip(view, R.id.chip_mirador);
                filterByCategory("Mirador");
                adapter.notifyDataSetChanged();
            });
            
            chipRestaurante.setOnClickListener(v -> {
                currentFilter = CATEGORY_RESTAURANTE;
                setActiveChip(view, R.id.chip_restaurante);
                filterByCategory("Restaurante");
                adapter.notifyDataSetChanged();
            });
            
            chipHotel.setOnClickListener(v -> {
                currentFilter = CATEGORY_HOTEL;
                setActiveChip(view, R.id.chip_hotel);
                filterByCategory("Hotel");
                adapter.notifyDataSetChanged();
            });
            
            btnMoreFilters.setOnClickListener(v -> showFilterPopupMenu(v));
            
            // Cargar comentarios
            loadCommentsFromFirestore();
            
            // Actualizar la UI según el estado de autenticación
            updateUI(firebaseAuth.getCurrentUser());
            
            // Mostrar título específico si viene de un marcador
            if (selectedLocationName != null && !selectedLocationName.isEmpty()) {
                // Intentar actualizar el título de la actividad
                if (getActivity() != null) {
                    getActivity().setTitle("Comentarios: " + selectedLocationName);
                }
                
                // Buscar el TextView del título para actualizarlo
                TextView commentsTitle = view.findViewById(R.id.comments_title);
                if (commentsTitle != null) {
                    commentsTitle.setText("Opiniones: " + selectedLocationName);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onViewCreated", e);
        }
    }
    
    /**
     * Guarda el estado actual de los elementos de UI importantes
     */
    private void saveUIState() {
        try {
            // Aquí podemos guardar el estado de cualquier elemento que necesitemos restaurar
            // después de publicar un comentario
        } catch (Exception e) {
            Log.e(TAG, "Error al guardar estado de UI", e);
        }
    }
    
    /**
     * Solicita los permisos de ubicación
     */
    private void requestLocationPermission() {
        if (ContextCompat.checkSelfPermission(requireContext(), 
                Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    REQUEST_LOCATION_PERMISSION);
        } else {
            getCurrentLocation();
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_LOCATION_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                getCurrentLocation();
            } else {
                Toast.makeText(getContext(), "Se requieren permisos de ubicación para usar esta función", Toast.LENGTH_SHORT).show();
            }
        }
    }
    
    /**
     * Obtiene la ubicación actual
     */
    private void getCurrentLocation() {
        try {
            if (locationManager == null) {
                locationManager = (LocationManager) requireContext().getSystemService(Context.LOCATION_SERVICE);
            }
            
            if (locationManager == null) {
                Toast.makeText(getContext(), "No se pudo acceder al servicio de ubicación", Toast.LENGTH_SHORT).show();
                return;
            }
            
            boolean isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            boolean isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            
            // Comentado: Modal de GPS
            /*if (!isGpsEnabled && !isNetworkEnabled) {
                Toast.makeText(getContext(), "Por favor activa el GPS", Toast.LENGTH_SHORT).show();
                return;
            }
            
            Toast.makeText(getContext(), "Obteniendo ubicación GPS...", Toast.LENGTH_SHORT).show();*/
            
            if (ActivityCompat.checkSelfPermission(requireContext(), 
                    Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                requestLocationPermission();
                return;
            }
            
            // Intentar obtener la última ubicación conocida
            Location location = null;
            
            // Primero intentar con GPS
            if (isGpsEnabled) {
                location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
            }
            
            // Si no hay ubicación del GPS, probar con la red
            if (location == null && isNetworkEnabled) {
                location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
            }
            
            if (location != null) {
                currentLat = location.getLatitude();
                currentLng = location.getLongitude();
                Log.d(TAG, "Ubicación actual: " + currentLat + ", " + currentLng);
                
                // Actualizar marcadores cercanos
                findNearbyPlaces();
            } else {
                // Si no hay ubicación conocida, solicitar una actualización única
                Log.d(TAG, "No hay ubicación conocida, solicitando actualización única");
                requestSingleLocationUpdate();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error al obtener ubicación", e);
            Toast.makeText(getContext(), "Error al obtener ubicación: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Solicita una única actualización de ubicación
     */
    private void requestSingleLocationUpdate() {
        try {
            if (locationManager == null) {
                locationManager = (LocationManager) requireContext().getSystemService(Context.LOCATION_SERVICE);
            }
            
            if (ActivityCompat.checkSelfPermission(requireContext(), 
                    Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                requestLocationPermission();
                return;
            }
            
            boolean isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            boolean isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            
            // Comentado: Modal de GPS
            /*if (!isGpsEnabled && !isNetworkEnabled) {
                Toast.makeText(getContext(), "Por favor activa el GPS o la ubicación por red", Toast.LENGTH_SHORT).show();
                return;
            }
            
            Toast.makeText(getContext(), "Actualizando ubicación...", Toast.LENGTH_SHORT).show();*/
            
            final LocationListener locationListener = new LocationListener() {
                @Override
                public void onLocationChanged(@NonNull Location location) {
                    currentLat = location.getLatitude();
                    currentLng = location.getLongitude();
                    Log.d(TAG, "Ubicación actualizada: " + currentLat + ", " + currentLng);
                    findNearbyPlaces();
                    
                    // Remover el listener después de obtener la ubicación
                    if (locationManager != null) {
                        locationManager.removeUpdates(this);
                    }
                }
                
                @Override
                public void onStatusChanged(String provider, int status, Bundle extras) {}
                
                @Override
                public void onProviderEnabled(@NonNull String provider) {}
                
                @Override
                public void onProviderDisabled(@NonNull String provider) {
                    Toast.makeText(getContext(), "Proveedor de ubicación desactivado", Toast.LENGTH_SHORT).show();
                }
            };
            
            // Intentar primero con GPS por ser más preciso
            if (isGpsEnabled) {
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER, 
                    0, 
                    0, 
                    locationListener,
                    null
                );
                Log.d(TAG, "Solicitando actualización por GPS");
            } 
            // Si no hay GPS, usar red
            else if (isNetworkEnabled) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER, 
                    0, 
                    0, 
                    locationListener,
                    null
                );
                Log.d(TAG, "Solicitando actualización por red");
            }
            
            // Establecer un timeout para cancelar la búsqueda después de 10 segundos
            new Handler().postDelayed(() -> {
                if (currentLat == 0 && currentLng == 0 && locationManager != null) {
                    locationManager.removeUpdates(locationListener);
                    
                    // Si después de 10 segundos no hay ubicación, usar una ubicación por defecto
                    // (en este caso, usar la ubicación de la Basílica de Itatí)
                    if (getContext() != null) {
                        Toast.makeText(getContext(), "No se pudo obtener la ubicación, usando ubicación por defecto", Toast.LENGTH_SHORT).show();
                        currentLat = -27.26852;
                        currentLng = -58.24334;
                        findNearbyPlaces();
                    }
                }
            }, 10000); // 10 segundos de timeout
        } catch (Exception e) {
            Log.e(TAG, "Error al solicitar actualización de ubicación", e);
            Toast.makeText(getContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Carga los marcadores de Firestore
     */
    private void loadMarkersFromFirestore() {
        try {
            // Limpiar lista existente
            nearbyPlaces.clear();
            
            // Cargar al menos la Basílica de Itatí como valor predeterminado
            nearbyPlaces.add(new PlaceSpinnerAdapter.PlaceItem("Basílica de Itatí", "iglesia", -27.26852, -58.24334));
            
            // Notificar al adaptador para que muestre al menos este lugar
            if (placesAdapter != null) {
                placesAdapter.notifyDataSetChanged();
            }
            
            Log.d(TAG, "Cargando marcadores de Firestore...");
            
            // Cargar el resto de marcadores desde Firestore
            if (db == null) {
                db = FirebaseFirestore.getInstance();
            }
            
            db.collection("marcadores")
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    int markersLoaded = 0;
                    
                    for (QueryDocumentSnapshot document : queryDocumentSnapshots) {
                        try {
                            String name = document.getString("name");
                            String category = document.getString("categoriaId");
                            Double latitude = document.getDouble("latitude");
                            Double longitude = document.getDouble("longitude");
                            
                            if (name != null && category != null && latitude != null && longitude != null) {
                                // No los agregamos todos al spinner aún, solo los guardamos para filtrar después
                                // por proximidad cuando se utilice el botón GPS
                                nearbyPlaces.add(new PlaceSpinnerAdapter.PlaceItem(name, category, latitude, longitude));
                                markersLoaded++;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error al procesar marcador: " + document.getId(), e);
                        }
                    }
                    
                    Log.d(TAG, "Marcadores cargados desde Firestore: " + markersLoaded);
                    
                    // Notificar al adaptador si hay cambios
                    if (placesAdapter != null) {
                        placesAdapter.notifyDataSetChanged();
                    }
                    
                    // Si ya tenemos una ubicación, actualizar los lugares cercanos
                    if (currentLat != 0 && currentLng != 0) {
                        findNearbyPlaces();
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al cargar marcadores", e);
                    Toast.makeText(getContext(), "Error al cargar marcadores: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
        } catch (Exception e) {
            Log.e(TAG, "Error en loadMarkersFromFirestore", e);
            Toast.makeText(getContext(), "Error al cargar marcadores: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Encuentra lugares cercanos basados en la ubicación actual
     */
    private void findNearbyPlaces() {
        if (currentLat == 0 && currentLng == 0) {
            Toast.makeText(getContext(), "No se ha podido obtener la ubicación", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Verificar que tengamos lugares para buscar
        if (nearbyPlaces == null || nearbyPlaces.isEmpty()) {
            Toast.makeText(getContext(), "No hay lugares disponibles para buscar", Toast.LENGTH_SHORT).show();
            // Intentar cargar marcadores nuevamente
            loadMarkersFromFirestore();
            return;
        }
        
        Log.d(TAG, "Buscando lugares cercanos a: " + currentLat + ", " + currentLng);
        Log.d(TAG, "Total de lugares disponibles: " + nearbyPlaces.size());
        
        // Crea una lista temporal para almacenar lugares cercanos
        List<PlaceSpinnerAdapter.PlaceItem> closeByPlaces = new ArrayList<>();
        
        // Itera a través de todos los lugares para encontrar los cercanos
        for (PlaceSpinnerAdapter.PlaceItem place : nearbyPlaces) {
            float[] results = new float[1];
            Location.distanceBetween(currentLat, currentLng, place.latitude, place.longitude, results);
            float distanceInMeters = results[0];
            
            if (distanceInMeters <= NEARBY_RADIUS_METERS) {
                // Este lugar está dentro del radio especificado
                closeByPlaces.add(place);
                Log.d(TAG, "Lugar cercano encontrado: " + place.name + " (a " + distanceInMeters + "m)");
            }
        }
        
        // Actualizar el spinner con los lugares cercanos
        if (closeByPlaces.isEmpty()) {
            Toast.makeText(getContext(), "No se encontraron lugares en un radio de " + 
                (int)NEARBY_RADIUS_METERS + " metros. Mostrando todos los lugares disponibles.", Toast.LENGTH_SHORT).show();
            
            // Si no hay lugares cercanos, mostrar todos los disponibles
            placesAdapter = new PlaceSpinnerAdapter(requireContext(), nearbyPlaces);
            spinnerLocation.setAdapter(placesAdapter);
        } else {
            // Limpiar y actualizar el adaptador con los lugares cercanos
            placesAdapter = new PlaceSpinnerAdapter(requireContext(), closeByPlaces);
            spinnerLocation.setAdapter(placesAdapter);
            
            Toast.makeText(getContext(), 
                    "Se encontraron " + closeByPlaces.size() + " lugares cercanos", 
                    Toast.LENGTH_SHORT).show();
        }
    }
    
    // Usamos PlaceSpinnerAdapter.PlaceItem en lugar de esta clase
    // La clase PlaceItem ahora está definida en PlaceSpinnerAdapter
    
    /**
     * Determina el icono a mostrar según la categoría del lugar
     * @param category Categoría del lugar
     * @return ID del recurso de icono
     */
    private int getCategoryIcon(String category) {
        if (category == null) return R.drawable.ic_location;
        
        String lowerCategory = category.toLowerCase().trim();
        
        if (lowerCategory.contains("iglesia") || lowerCategory.contains("basilica") || 
            lowerCategory.contains("capilla") || lowerCategory.contains("catedral")) {
            return R.drawable.ic_church;
        }
        
        if (lowerCategory.contains("hotel") || lowerCategory.contains("hospedaje") || 
            lowerCategory.contains("alojamiento") || lowerCategory.contains("posada")) {
            return R.drawable.ic_hotel;
        }
        
        if (lowerCategory.contains("restaurante") || lowerCategory.contains("comida") || 
            lowerCategory.contains("cafeteria") || lowerCategory.contains("bar")) {
            return R.drawable.ic_restaurant;
        }
        
        if (lowerCategory.contains("atraccion") || lowerCategory.contains("turismo") || 
            lowerCategory.contains("monumento") || lowerCategory.contains("mirador")) {
            return R.drawable.ic_attraction;
        }
        
        if (lowerCategory.contains("baño") || lowerCategory.contains("sanitario") || 
            lowerCategory.contains("wc") || lowerCategory.contains("servicio")) {
            return R.drawable.ic_bathroom;
        }
        
        if (lowerCategory.contains("farmacia") || lowerCategory.contains("medicamento")) {
            return R.drawable.ic_pharmacy;
        }
        
        if (lowerCategory.contains("transporte") || lowerCategory.contains("terminal") || 
            lowerCategory.contains("bus") || lowerCategory.contains("colectivo")) {
            return R.drawable.ic_transport;
        }
        
        if (lowerCategory.contains("cajero") || lowerCategory.contains("banco") || 
            lowerCategory.contains("atm") || lowerCategory.contains("dinero")) {
            return R.drawable.ic_atm;
        }
        
        // Si no coincide con ninguna categoría, usar icono de ubicación genérico
        return R.drawable.ic_location;
    }
    
    private void signIn() {
        Intent signInIntent = googleSignInClient.getSignInIntent();
        startActivityForResult(signInIntent, RC_SIGN_IN);
    }
    
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == RC_SIGN_IN) {
            Task<GoogleSignInAccount> task = GoogleSignIn.getSignedInAccountFromIntent(data);
            try {
                // El inicio de sesión de Google fue exitoso, autenticar con Firebase
                GoogleSignInAccount account = task.getResult(ApiException.class);
                Log.d(TAG, "firebaseAuthWithGoogle:" + account.getId());
                firebaseAuthWithGoogle(account.getIdToken());
            } catch (ApiException e) {
                // El inicio de sesión de Google falló
                Log.w(TAG, "Google sign in failed", e);
                Toast.makeText(getContext(), "Error al iniciar sesión con Google", Toast.LENGTH_SHORT).show();
                updateUI(null);
            }
        }
    }
    
    /**
     * ⚠️ NO MODIFICAR - NO TOUCH ⚠️
     * Este método maneja la autenticación con Firebase y el guardado automático de datos del usuario.
     * Cualquier modificación puede afectar el funcionamiento del inicio de sesión y el guardado de datos.
     * Si necesita hacer cambios, contacte al desarrollador principal.
     */
    private void firebaseAuthWithGoogle(String idToken) {
        AuthCredential credential = GoogleAuthProvider.getCredential(idToken, null);
        firebaseAuth.signInWithCredential(credential)
                .addOnCompleteListener(requireActivity(), task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = firebaseAuth.getCurrentUser();
                        if (user != null) {
                            // Guardar datos del usuario en Firestore automáticamente
                            Map<String, Object> userData = new HashMap<>();
                            userData.put("uid", user.getUid());
                            userData.put("email", user.getEmail());
                            userData.put("displayName", user.getDisplayName());
                            userData.put("photoUrl", user.getPhotoUrl() != null ? user.getPhotoUrl().toString() : null);
                            userData.put("lastLogin", new Date());
                            userData.put("isActive", true);

                            // Verificar si es un usuario nuevo
                            db.collection("users").document(user.getUid())
                                .get()
                                .addOnSuccessListener(documentSnapshot -> {
                                    if (!documentSnapshot.exists()) {
                                        userData.put("createdAt", new Date());
                                    }
                                    
                                    // Guardar o actualizar datos del usuario
                                    db.collection("users").document(user.getUid())
                                        .set(userData, SetOptions.merge())
                                        .addOnSuccessListener(aVoid -> {
                                            Log.d(TAG, "Datos de usuario guardados/actualizados en users");
                                            updateUI(user);
                                            Toast.makeText(requireContext(), "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                        })
                                        .addOnFailureListener(e -> {
                                            Log.e(TAG, "Error al guardar datos de usuario", e);
                                            updateUI(user);
                                            Toast.makeText(requireContext(), "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                        });
                                })
                                .addOnFailureListener(e -> {
                                    Log.e(TAG, "Error al verificar existencia de usuario", e);
                                    updateUI(user);
                                    Toast.makeText(requireContext(), "¡Bienvenido!", Toast.LENGTH_SHORT).show();
                                });
                        }
                    } else {
                        Log.w(TAG, "signInWithCredential:failure", task.getException());
                        Toast.makeText(requireContext(), "Error de autenticación", Toast.LENGTH_SHORT).show();
                        updateUI(null);
                    }
                });
    }
    
    private void updateUI(FirebaseUser user) {
        if (user != null) {
            // Usuario autenticado, mostrar interfaz de comentarios
            signInLayout.setVisibility(View.GONE);
            commentLayout.setVisibility(View.VISIBLE);
            userInfoText.setVisibility(View.VISIBLE);
            userInfoText.setText("Comentando como: " + user.getDisplayName());
            
            // Cargar los likes del usuario actual
            loadUserLikes(user.getUid());
            
            // Cargar foto de perfil del usuario en el avatar del modal
            String photoUrl = user.getPhotoUrl() != null ? user.getPhotoUrl().toString() : null;
            
            // Avatar en el modal
            CircleImageView userAvatarModal = getView().findViewById(R.id.user_avatar_modal);
            
            // Mostrar foto de perfil
            if (photoUrl != null && !photoUrl.isEmpty()) {
                // Convertir URL si es de Google Drive
                String directUrl = ImageUtils.getDirectImageUrl(photoUrl);
                Log.d(TAG, "Cargando foto de perfil: " + photoUrl);
                Log.d(TAG, "URL procesada: " + directUrl);
                
                Glide.with(this)
                    .load(directUrl)
                    .apply(RequestOptions.circleCropTransform())
                    .into(userAvatarModal);
            }
            
            // Configurar botón para abrir modal de comentarios
            Button btnOpenCommentModal = getView().findViewById(R.id.btn_open_comment_modal);
            btnOpenCommentModal.setOnClickListener(v -> {
                View commentModalContainer = getView().findViewById(R.id.comment_modal_container);
                commentModalContainer.setVisibility(View.VISIBLE);
                
                // Asegurarnos que el modal esté por encima de todo
                commentModalContainer.bringToFront();
                
                // Ocultar la barra de estado y navegación para modo inmersivo
                if (getActivity() != null && getActivity().getWindow() != null) {
                    getActivity().getWindow().getDecorView().setSystemUiVisibility(
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    );
                }
            });
            
            // Configurar botón para cerrar modal
            ImageButton btnCloseComment = getView().findViewById(R.id.btn_close_comment);
            btnCloseComment.setOnClickListener(v -> {
                View commentModalContainer = getView().findViewById(R.id.comment_modal_container);
                commentModalContainer.setVisibility(View.GONE);
                
                // Restaurar la interfaz de usuario normal
                if (getActivity() != null && getActivity().getWindow() != null) {
                    getActivity().getWindow().getDecorView().setSystemUiVisibility(
                        View.SYSTEM_UI_FLAG_VISIBLE
                    );
                }
            });
            
            // Configurar fondo del modal para cerrar al hacer clic fuera
            View commentModalContainer = getView().findViewById(R.id.comment_modal_container);
            commentModalContainer.setOnClickListener(v -> {
                commentModalContainer.setVisibility(View.GONE);
                
                // Restaurar la interfaz de usuario normal
                if (getActivity() != null && getActivity().getWindow() != null) {
                    getActivity().getWindow().getDecorView().setSystemUiVisibility(
                        View.SYSTEM_UI_FLAG_VISIBLE
                    );
                }
            });
            
            // Evitar que los clics en la tarjeta cierren el modal
            View commentCard = getView().findViewById(R.id.comment_card);
            commentCard.setOnClickListener(v -> {
                // No hacer nada, solo consumir el clic
            });
            
            // Configurar selector de ubicación
            View locationSelector = getView().findViewById(R.id.comment_location_selector);
            locationSelector.setOnClickListener(v -> {
                // Aquí se abriría un diálogo para seleccionar ubicación
                Toast.makeText(getContext(), "Seleccionar ubicación (funcionalidad pendiente)", Toast.LENGTH_SHORT).show();
            });
        } else {
            // No hay usuario autenticado, mostrar botón de inicio de sesión
            signInLayout.setVisibility(View.VISIBLE);
            commentLayout.setVisibility(View.GONE);
            userInfoText.setVisibility(View.GONE);
        }
    }
    
    /**
     * Carga los comentarios desde Firestore
     */
    private void loadCommentsFromFirestore() {
        try {
            // Mostrar mensaje de carga
            Toast.makeText(getContext(), "Cargando comentarios...", Toast.LENGTH_SHORT).show();
            
            // Obtener instancia de Firestore
            FirebaseFirestore db = FirebaseFirestore.getInstance();
            
            // Consulta base
            com.google.firebase.firestore.Query query = db.collection("comentarios");
            
            // Si hay una ubicación seleccionada, filtrar por ella
            if (selectedLocationName != null && !selectedLocationName.isEmpty()) {
                query = query.whereEqualTo("ubicacion", selectedLocationName);
            }
            
            // Ordenar por fecha (más recientes primero)
            query = query.orderBy("fecha", com.google.firebase.firestore.Query.Direction.DESCENDING);
            
            // Ejecutar la consulta
            query.get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    commentList.clear();
                    
                    for (com.google.firebase.firestore.DocumentSnapshot document : queryDocumentSnapshots) {
                        String commentId = document.getString("id");
                        String userId = document.getString("userId");
                        String userName = document.getString("usuario");
                        String commentText = document.getString("comentario");
                        Date date = document.getDate("fecha");
                        String location = document.getString("ubicacion");
                        String photoUrl = document.getString("photoUrl");
                        String visitType = document.getString("tipoVisita");
                        String visitDate = document.getString("fechaVisita");
                        Boolean recommends = document.getBoolean("recomienda");
                        String positiveAspect = document.getString("aspectoPositivo");
                        String negativeAspect = document.getString("aspectoNegativo");
                        String agradecimiento = document.getString("agradecimiento");
                        
                        // Obtener calificación (rating)
                        Double ratingDouble = document.getDouble("rating");
                        float rating = ratingDouble != null ? ratingDouble.floatValue() : 0f;
                        
                        // Crear objeto Comment
                        Comment comment = new Comment();
                        // Usar el ID específico del comentario si está disponible, de lo contrario usar el userId
                        comment.setId(userId != null ? userId : "");
                        // Guardar también el ID completo del comentario si está disponible
                        if (commentId != null) {
                            comment.setCommentId(commentId);
                        }
                        comment.setUserName(userName != null ? userName : "Usuario anónimo");
                        
                        // Si el comentario es muy corto y hay un mensaje de agradecimiento, mostrar ambos
                        if (agradecimiento != null && !agradecimiento.isEmpty()) {
                            // Formatear el texto para incluir el mensaje de agradecimiento
                            if (commentText != null && !commentText.isEmpty()) {
                                comment.setText(commentText + "\n\n" + agradecimiento);
                            } else {
                                comment.setText(agradecimiento);
                            }
                        } else {
                        comment.setText(commentText != null ? commentText : "");
                        }
                        
                        comment.setDate(date != null ? date : new Date());
                        comment.setRating(rating);
                        comment.setLocation(location != null ? location : "");
                        comment.setPhotoUrl(photoUrl);
                        comment.setVisitType(visitType);
                        comment.setVisitDate(visitDate);
                        comment.setRecommends(recommends != null ? recommends : false);
                        comment.setPositiveAspect(positiveAspect);
                        comment.setNegativeAspect(negativeAspect);
                        
                        // Obtener contador de likes (si existe)
                        Long likesLong = document.getLong("likes");
                        int likes = likesLong != null ? likesLong.intValue() : 0;
                        comment.setLikes(likes);
                        
                        Log.d(TAG, "Comentario cargado: " + comment.getUserId() + "_" + comment.getTimestamp() + 
                              ", likes: " + likes);
                        
                        // Añadir a la lista
                        commentList.add(comment);
                    }
                    
                    // Aplicar el filtro actual a los comentarios cargados
                    applyFilter();
                    
                    // Cargar los likes del usuario actual
                    FirebaseUser currentUser = firebaseAuth.getCurrentUser();
                    if (currentUser != null) {
                        loadUserLikes(currentUser.getUid());
                    }
                    
                    Log.d(TAG, "Comentarios cargados: " + commentList.size());
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al cargar comentarios", e);
                    
                    // Mostrar mensaje de error
                    Toast.makeText(getContext(), "Error al cargar comentarios: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
                
        } catch (Exception e) {
            Log.e(TAG, "Error al cargar comentarios", e);
            Toast.makeText(getContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Aplica el filtro seleccionado a la lista de comentarios
     */
    private void applyFilter() {
        // Limpiar la lista filtrada
        filteredCommentList.clear();
        
        // Si no hay comentarios, no hay nada que filtrar
        if (commentList.isEmpty()) {
            adapter.notifyDataSetChanged();
            return;
        }
        
        // Copiar todos los comentarios a la lista filtrada
        filteredCommentList.addAll(commentList);
        
        // Aplicar el filtro seleccionado
        switch (currentFilter) {
            case FILTER_ALL:
                // Mostrar todos los comentarios, sin filtrar
                break;
                
            case FILTER_RECENT:
                // Ordenar por fecha (más recientes primero)
                Collections.sort(filteredCommentList, (c1, c2) -> 
                    Long.compare(c2.getTimestamp(), c1.getTimestamp()));
                break;
                
            case FILTER_HIGHEST_RATING:
                // Ordenar por calificación (mayor a menor)
                Collections.sort(filteredCommentList, (c1, c2) -> 
                    Float.compare(c2.getRating(), c1.getRating()));
                break;
                
            case FILTER_LOWEST_RATING:
                // Ordenar por calificación (menor a mayor)
                Collections.sort(filteredCommentList, (c1, c2) -> 
                    Float.compare(c1.getRating(), c2.getRating()));
                break;
                
            // Filtros por categoría
            case CATEGORY_BASILICA:
                filterByCategory("Basílica");
                break;
                
            case CATEGORY_PLAZA:
                filterByCategory("Plaza");
                break;
                
            case CATEGORY_MIRADOR:
                filterByCategory("Mirador");
                break;
                
            case CATEGORY_RESTAURANTE:
                filterByCategory("Restaurante");
                break;
                
            case CATEGORY_HOTEL:
                filterByCategory("Hotel");
                break;
                
            case CATEGORY_MUSEO:
                filterByCategory("Museo");
                break;
        }
        
        // Notificar al adaptador que los datos han cambiado
        adapter.notifyDataSetChanged();
        
        // Mostrar mensaje con el número de comentarios filtrados
        Toast.makeText(getContext(), 
            "Mostrando " + filteredCommentList.size() + " comentarios", 
            Toast.LENGTH_SHORT).show();
    }
    
    /**
     * Establece el chip activo y actualiza la apariencia de todos los chips
     */
    private void setActiveChip(View rootView, int activeChipId) {
        // Lista de todos los IDs de chips
        int[] chipIds = {
            R.id.chip_all,
            R.id.chip_basilica,
            R.id.chip_plaza,
            R.id.chip_mirador,
            R.id.chip_restaurante,
            R.id.chip_hotel
        };
        
        // Actualizar apariencia de cada chip
        for (int chipId : chipIds) {
            TextView chip = rootView.findViewById(chipId);
            if (chip != null) {
                if (chipId == activeChipId) {
                    // Chip activo
                    chip.setBackground(getResources().getDrawable(R.drawable.chip_background_selected));
                    chip.setTextColor(getResources().getColor(android.R.color.white));
                } else {
                    // Chip inactivo
                    chip.setBackground(getResources().getDrawable(R.drawable.chip_background));
                    chip.setTextColor(getResources().getColor(R.color.primary));
                }
            }
        }
    }
    
    /**
     * Filtra los comentarios por categoría
     */
    private void filterByCategory(String category) {
        List<Comment> categoryComments = new ArrayList<>();
        for (Comment comment : commentList) {
            if (comment.getLocation() != null && 
                comment.getLocation().contains(category)) {
                categoryComments.add(comment);
            }
        }
        filteredCommentList.clear();
        filteredCommentList.addAll(categoryComments);
        
        // Si no hay comentarios en esta categoría, mostrar un mensaje
        if (categoryComments.isEmpty()) {
            Toast.makeText(getContext(), 
                "No hay comentarios para " + category, 
                Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Muestra un menú emergente con opciones adicionales de filtrado
     */
    private void showFilterPopupMenu(View anchorView) {
        PopupMenu popupMenu = new PopupMenu(getContext(), anchorView);
        popupMenu.getMenu().add(0, FILTER_RECENT, 0, "Ordenar por más recientes");
        popupMenu.getMenu().add(0, FILTER_HIGHEST_RATING, 0, "Ordenar por mejor valorados");
        popupMenu.getMenu().add(0, FILTER_LOWEST_RATING, 0, "Ordenar por peor valorados");
        popupMenu.getMenu().add(0, CATEGORY_MUSEO, 0, "Filtrar por Museo");
        
        popupMenu.setOnMenuItemClickListener(item -> {
            int selectedFilter = item.getItemId();
            
            // Desactivar todos los chips si es una opción de ordenamiento
            if (selectedFilter == FILTER_RECENT || 
                selectedFilter == FILTER_HIGHEST_RATING || 
                selectedFilter == FILTER_LOWEST_RATING) {
                setActiveChip(anchorView.getRootView(), -1); // -1 significa ningún chip activo
            } else {
                // Es una categoría, activar el chip correspondiente si existe
                // Para categorías que no tienen chip, no hacemos nada con los chips
            }
            
            currentFilter = selectedFilter;
            applyFilter();
            return true;
        });
        
        popupMenu.show();
    }

    // Agregar este método para manejar el envío de comentarios
    private void submitComment() {
        try {
            // Verificar si el fragmento está adjunto a una actividad
            if (!isAdded()) {
                Log.e(TAG, "Fragmento no adjunto a una actividad");
                return;
            }

            String commentText = commentEditText.getText().toString().trim();
            
            if (commentText.isEmpty()) {
                Toast.makeText(getContext(), "Por favor, escribe un comentario", Toast.LENGTH_SHORT).show();
                return;
            }
            
            FirebaseUser user = firebaseAuth.getCurrentUser();
            
            if (user == null) {
                Toast.makeText(getContext(), "Debes iniciar sesión para comentar", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // Obtener la calificación del RatingBar
            float rating = ratingBar != null ? ratingBar.getRating() : 0;
            
            if (rating == 0) {
                Toast.makeText(getContext(), "Por favor, califica tu experiencia", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // Obtener el lugar seleccionado
            PlaceSpinnerAdapter.PlaceItem selectedPlace = null;
            if (spinnerLocation != null && spinnerLocation.getSelectedItem() != null) {
                selectedPlace = (PlaceSpinnerAdapter.PlaceItem) spinnerLocation.getSelectedItem();
            }
            
            if (selectedPlace == null) {
                Toast.makeText(getContext(), "Por favor, selecciona un lugar", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // Hacer una copia final de los datos del lugar seleccionado
            final String placeName = selectedPlace.name;
            final double placeLat = selectedPlace.latitude;
            final double placeLng = selectedPlace.longitude;
            final String placeCategory = selectedPlace.category;
            
            // Obtener instancia de Firestore
            FirebaseFirestore db = FirebaseFirestore.getInstance();
            
            // Mostrar mensaje de carga
            Toast.makeText(getContext(), "Enviando comentario...", Toast.LENGTH_SHORT).show();

            // Crear el objeto con los datos del comentario
            Date currentDate = new Date();
            
            // Generar un ID único para el comentario
            String commentId = "comment_" + System.currentTimeMillis() + "_" + user.getUid();
            
            // HashMap con los campos que coinciden con la estructura de Firestore
            Map<String, Object> commentData = new HashMap<>();
            commentData.put("id", commentId);                   // ID único del comentario
            commentData.put("userId", user.getUid());           // ID del usuario que comentó
            commentData.put("comentario", commentText);
            commentData.put("fecha", currentDate);
            commentData.put("usuario", user.getDisplayName());
            commentData.put("rating", rating);
            commentData.put("ubicacion", placeName);
            commentData.put("ubicacionLat", placeLat);
            commentData.put("ubicacionLng", placeLng);
            commentData.put("categoria", placeCategory);
            
            // Agregar un mensaje de agradecimiento para comentarios muy cortos
            if (commentText.length() <= 5) {
                commentData.put("agradecimiento", "¡Gracias por tu comentario!");
            }
            
            // Valores por defecto para los campos adicionales
            commentData.put("tipoVisita", "Turismo");
            commentData.put("fechaVisita", new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault()).format(currentDate));
            commentData.put("recomienda", rating >= 3.0f);
            
            // Generar destacados según la calificación
            if (rating >= 4.0f) {
                commentData.put("aspectoPositivo", "Excelente experiencia");
            } else if (rating >= 3.0f) {
                commentData.put("aspectoPositivo", "Buena experiencia");
            }
            
            if (rating <= 2.0f) {
                commentData.put("aspectoNegativo", "Experiencia mejorable");
            }
            
            // Añadir URL de la foto de perfil si está disponible
            if (user.getPhotoUrl() != null) {
                commentData.put("photoUrl", user.getPhotoUrl().toString());
            }
            
            // Inicializar contador de likes en 0
            commentData.put("likes", 0);

            // Guardar el comentario usando el ID único como ID del documento
            db.collection("comentarios")
                .document(commentId)
                .set(commentData)
                .addOnSuccessListener(aVoid -> {
                    Log.d(TAG, "Comentario guardado con ID: " + commentId);
                    
                    // Limpiar campo de texto
                    if (commentEditText != null) {
                        commentEditText.setText("");
                    }
                    
                    // Reiniciar rating a 3 estrellas (valor por defecto)
                    if (ratingBar != null) {
                        ratingBar.setRating(3.0f);
                    }
                    
                    // Ocultar modal
                    View commentModalContainer = getView().findViewById(R.id.comment_modal_container);
                    if (commentModalContainer != null) {
                        commentModalContainer.setVisibility(View.GONE);
                        
                        // Restaurar el estado de la UI normal al desaparecer el modal
                        if (getActivity() != null && getActivity().getWindow() != null) {
                            getActivity().getWindow().getDecorView().setSystemUiVisibility(
                                View.SYSTEM_UI_FLAG_VISIBLE
                            );
                        }
                    }
                    
                    // Mostrar mensaje de éxito
                    Toast.makeText(getContext(), "¡Gracias por tu comentario!", Toast.LENGTH_SHORT).show();
                    
                    // Recargar comentarios
                    loadCommentsFromFirestore();
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al guardar comentario", e);
                    Toast.makeText(getContext(), "Error al enviar comentario: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
        } catch (Exception e) {
            Log.e(TAG, "Error al enviar comentario", e);
            Toast.makeText(getContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Muestra un diálogo con todos los comentarios de un lugar específico
     */
    private void showCommentsForLocation(String locationName) {
        try {
            // Crear un diálogo personalizado
            AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
            View dialogView = getLayoutInflater().inflate(R.layout.dialog_location_comments, null);
            builder.setView(dialogView);
            
            // Configurar título y elementos
            TextView titleText = dialogView.findViewById(R.id.dialog_title);
            RecyclerView commentsList = dialogView.findViewById(R.id.dialog_comments_recycler);
            Button closeButton = dialogView.findViewById(R.id.btn_close_dialog);
            
            // Configurar el título
            if (titleText != null) {
                titleText.setText("Comentarios de " + locationName);
            }
            
            // Configurar RecyclerView
            if (commentsList != null) {
                commentsList.setLayoutManager(new LinearLayoutManager(getContext()));
                
                // Filtrar comentarios por ubicación
                List<Comment> locationComments = new ArrayList<>();
                for (Comment comment : commentList) {
                    if (comment.getLocation() != null && comment.getLocation().equals(locationName)) {
                        locationComments.add(comment);
                    }
                }
                
                // Configurar adaptador
                CommentAdapter adapter = new CommentAdapter(locationComments);
                commentsList.setAdapter(adapter);
            }
            
            // Crear y mostrar el diálogo
            AlertDialog dialog = builder.create();
            
            // Configurar botón de cierre
            if (closeButton != null) {
                closeButton.setOnClickListener(v -> dialog.dismiss());
            }
            
            dialog.show();
            
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar comentarios del lugar", e);
            Toast.makeText(getContext(), "Error al mostrar comentarios: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Maneja la acción de dar like a un comentario
     */
    private void handleLikeAction(Comment comment, int position) {
        // Verificar si el usuario está autenticado
        FirebaseUser currentUser = firebaseAuth.getCurrentUser();
        if (currentUser == null) {
            Toast.makeText(getContext(), "Debes iniciar sesión para dar like", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Obtener el ID único del comentario
        String commentId;
        if (comment.getCommentId() != null && !comment.getCommentId().isEmpty()) {
            // Usar el ID explícito si está disponible
            commentId = comment.getCommentId();
        } else {
            // Crear ID en el formato antiguo si no está disponible
            commentId = "comment_" + comment.getTimestamp() + "_" + comment.getUserId();
        }
        
        Log.d(TAG, "Manejando acción de like para comentario: " + commentId + 
              ", posición: " + position + ", likes actuales: " + comment.getLikes());
        
        checkUserLike(comment, currentUser.getUid(), position);
    }
    
    /**
     * Verifica si el usuario ya dio like al comentario
     */
    private void checkUserLike(Comment comment, String userId, int position) {
        // Obtener el ID único del comentario
        String commentId;
        if (comment.getCommentId() != null && !comment.getCommentId().isEmpty()) {
            // Usar el ID explícito si está disponible
            commentId = comment.getCommentId();
        } else {
            // Crear ID en el formato antiguo si no está disponible
            commentId = "comment_" + comment.getTimestamp() + "_" + comment.getUserId();
        }
        
        // Referencia a la colección de likes
        db.collection("likes")
            .whereEqualTo("commentId", commentId)
            .whereEqualTo("userId", userId)
            .get()
            .addOnSuccessListener(queryDocumentSnapshots -> {
                if (queryDocumentSnapshots.isEmpty()) {
                    // El usuario no ha dado like, agregar like
                    addLike(comment, userId, position, commentId);
                } else {
                    // El usuario ya dio like, quitar like
                    removeLike(comment, userId, position, queryDocumentSnapshots.getDocuments().get(0).getId(), commentId);
                }
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al verificar like", e);
                Toast.makeText(getContext(), "Error al verificar like", Toast.LENGTH_SHORT).show();
            });
    }
    
    /**
     * Agrega un like al comentario
     */
    private void addLike(Comment comment, String userId, int position, String commentId) {
        Log.d(TAG, "Agregando like a comentario: " + commentId + ", likes actuales: " + comment.getLikes());
        
        // Incrementar el contador de likes
        comment.incrementLikes();
        
        // Actualizar el estado visual en el adaptador
        if (adapter instanceof CommentAdapter) {
            ((CommentAdapter) adapter).setLikeStatus(commentId, true);
        }
        
        // Actualizar la UI inmediatamente
        adapter.notifyItemChanged(position);
        
        // Crear registro de like en Firestore
        Map<String, Object> likeData = new HashMap<>();
        likeData.put("commentId", commentId);
        likeData.put("userId", userId);
        likeData.put("timestamp", new Date().getTime());
        
        // Guardar el like en Firestore
        db.collection("likes")
            .add(likeData)
            .addOnSuccessListener(documentReference -> {
                Log.d(TAG, "Like agregado con ID: " + documentReference.getId() + 
                      ", nuevo contador: " + comment.getLikes());
                
                // Actualizar contador en el comentario en Firestore
                updateLikesInFirestore(comment);
                
                // Actualizar todas las instancias del comentario en las listas locales
                updateLocalCommentLikes(comment);
                
                Toast.makeText(getContext(), "¡Like agregado!", Toast.LENGTH_SHORT).show();
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al agregar like", e);
                // Revertir el incremento del contador
                comment.decrementLikes();
                // Revertir el estado visual
                if (adapter instanceof CommentAdapter) {
                    ((CommentAdapter) adapter).setLikeStatus(commentId, false);
                }
                adapter.notifyItemChanged(position);
                Toast.makeText(getContext(), "Error al dar like", Toast.LENGTH_SHORT).show();
            });
    }
    
    /**
     * Quita un like del comentario
     */
    private void removeLike(Comment comment, String userId, int position, String likeId, String commentId) {
        Log.d(TAG, "Quitando like a comentario: " + commentId + ", likes actuales: " + comment.getLikes());
        
        // Decrementar el contador de likes
        comment.decrementLikes();
        
        // Actualizar el estado visual en el adaptador
        if (adapter instanceof CommentAdapter) {
            ((CommentAdapter) adapter).setLikeStatus(commentId, false);
        }
        
        // Actualizar la UI inmediatamente
        adapter.notifyItemChanged(position);
        
        // Eliminar registro de like en Firestore
        db.collection("likes").document(likeId)
            .delete()
            .addOnSuccessListener(aVoid -> {
                Log.d(TAG, "Like eliminado correctamente, nuevo contador: " + comment.getLikes());
                
                // Actualizar contador en el comentario en Firestore
                updateLikesInFirestore(comment);
                
                // Actualizar todas las instancias del comentario en las listas locales
                updateLocalCommentLikes(comment);
                
                Toast.makeText(getContext(), "Like eliminado", Toast.LENGTH_SHORT).show();
            })
            .addOnFailureListener(e -> {
                Log.e(TAG, "Error al eliminar like", e);
                // Revertir el decremento del contador
                comment.incrementLikes();
                // Revertir el estado visual
                if (adapter instanceof CommentAdapter) {
                    ((CommentAdapter) adapter).setLikeStatus(commentId, true);
                }
                adapter.notifyItemChanged(position);
                Toast.makeText(getContext(), "Error al quitar like", Toast.LENGTH_SHORT).show();
            });
    }
    
    /**
     * Actualiza todas las instancias de un comentario en las listas locales
     */
    private void updateLocalCommentLikes(Comment comment) {
        String commentId = comment.getUserId() + "_" + comment.getTimestamp();
        Log.d(TAG, "Actualizando listas locales para comentario: " + commentId + 
              ", nuevo contador: " + comment.getLikes());
        
        // Actualizar el contador en la lista principal
        boolean foundInMainList = false;
        for (Comment c : commentList) {
            if (c.getUserId().equals(comment.getUserId()) && 
                c.getTimestamp() == comment.getTimestamp()) {
                c.setLikes(comment.getLikes());
                foundInMainList = true;
                Log.d(TAG, "Comentario actualizado en lista principal");
            }
        }
        
        // Actualizar la lista filtrada también
        boolean foundInFilteredList = false;
        for (Comment c : filteredCommentList) {
            if (c.getUserId().equals(comment.getUserId()) && 
                c.getTimestamp() == comment.getTimestamp()) {
                c.setLikes(comment.getLikes());
                foundInFilteredList = true;
                Log.d(TAG, "Comentario actualizado en lista filtrada");
            }
        }
        
        // Buscar la posición del comentario en la lista filtrada
        int position = -1;
        for (int i = 0; i < filteredCommentList.size(); i++) {
            Comment c = filteredCommentList.get(i);
            if (c.getUserId().equals(comment.getUserId()) && 
                c.getTimestamp() == comment.getTimestamp()) {
                position = i;
                break;
            }
        }
        
        // Notificar cambios al adaptador solo para el elemento específico
        if (position >= 0) {
            adapter.notifyItemChanged(position);
            Log.d(TAG, "Notificado cambio en posición: " + position);
        } else {
            // Si no se encontró la posición, actualizar toda la lista
            adapter.notifyDataSetChanged();
            Log.d(TAG, "Notificados cambios en toda la lista");
        }
        
        Log.d(TAG, "Listas locales actualizadas con nuevo contador de likes: " + comment.getLikes() + 
              ", encontrado en lista principal: " + foundInMainList + 
              ", encontrado en lista filtrada: " + foundInFilteredList);
    }
    
    /**
     * Actualiza el contador de likes en Firestore
     */
    private void updateLikesInFirestore(Comment comment) {
        try {
            // Crear ID único para el comentario
            String commentId = "comment_" + comment.getTimestamp() + "_" + comment.getUserId();
            Log.d(TAG, "Actualizando likes para comentario con ID: " + commentId + ", nuevo valor: " + comment.getLikes());
            
            // Buscar el comentario por su ID único
            db.collection("comentarios")
                .whereEqualTo("id", commentId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    boolean found = false;
                    
                    if (!queryDocumentSnapshots.isEmpty()) {
                        found = true;
                        String documentId = queryDocumentSnapshots.getDocuments().get(0).getId();
                        
                        // Actualizar el contador de likes
                        db.collection("comentarios").document(documentId)
                            .update("likes", comment.getLikes())
                            .addOnSuccessListener(aVoid -> {
                                Log.d(TAG, "Like actualizado correctamente en Firestore para documento: " + documentId);
                                
                                // Actualizar todas las instancias del comentario en las listas locales
                                updateLocalCommentLikes(comment);
                            })
                            .addOnFailureListener(e -> {
                                Log.e(TAG, "Error al actualizar like", e);
                                Toast.makeText(getContext(), "Error al registrar like", Toast.LENGTH_SHORT).show();
                            });
                    }
                    
                    if (!found) {
                        Log.e(TAG, "No se encontró el comentario para actualizar like");
                        Toast.makeText(getContext(), "No se pudo actualizar el like", Toast.LENGTH_SHORT).show();
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al buscar comentario para actualizar like", e);
                    Toast.makeText(getContext(), "Error al buscar comentario", Toast.LENGTH_SHORT).show();
                });
        } catch (Exception e) {
            Log.e(TAG, "Error al actualizar likes en Firestore", e);
            Toast.makeText(getContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Muestra opciones para un comentario (editar, eliminar, etc.)
     */
    private void showCommentOptions(Comment comment, int position, View view) {
        // Verificar si el usuario está autenticado
        FirebaseUser currentUser = firebaseAuth.getCurrentUser();
        if (currentUser == null) {
            Toast.makeText(getContext(), "Debes iniciar sesión para gestionar comentarios", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Verificar si el comentario pertenece al usuario actual
        boolean isOwner = currentUser.getUid().equals(comment.getUserId());
        
        // Crear menú popup
        PopupMenu popup = new PopupMenu(requireContext(), view);
        popup.getMenuInflater().inflate(R.menu.comment_options_menu, popup.getMenu());
        
        // Mostrar opciones según si es propietario o no
        popup.getMenu().findItem(R.id.action_edit).setVisible(isOwner);
        popup.getMenu().findItem(R.id.action_delete).setVisible(isOwner);
        
        // Configurar listener
        popup.setOnMenuItemClickListener(item -> {
            int itemId = item.getItemId();
            
            if (itemId == R.id.action_edit) {
                // Implementar edición de comentario
                Toast.makeText(getContext(), "Editar comentario", Toast.LENGTH_SHORT).show();
                return true;
            } else if (itemId == R.id.action_delete) {
                // Implementar eliminación de comentario
                deleteComment(comment, position);
                return true;
            } else if (itemId == R.id.action_report) {
                // Implementar reporte de comentario
                reportComment(comment);
                return true;
            }
            
            return false;
        });
        
        // Mostrar el menú
        popup.show();
    }
    
    /**
     * Carga los likes del usuario actual
     */
    private void loadUserLikes(String userId) {
        try {
            Log.d(TAG, "Cargando likes del usuario: " + userId);
            
            // Consultar todos los likes del usuario actual
            db.collection("likes")
                .whereEqualTo("userId", userId)
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    // Verificar si el adaptador es del tipo correcto
                    if (adapter instanceof CommentAdapter) {
                        CommentAdapter commentAdapter = (CommentAdapter) adapter;
                        
                        // Limpiar estados anteriores
                        commentAdapter.clearLikeStatuses();
                        
                        // Procesar cada like
                        for (com.google.firebase.firestore.DocumentSnapshot document : queryDocumentSnapshots) {
                            String commentId = document.getString("commentId");
                            if (commentId != null) {
                                // Marcar el comentario como "liked" por el usuario
                                commentAdapter.setLikeStatus(commentId, true);
                                Log.d(TAG, "Comentario marcado como liked: " + commentId);
                            }
                        }
                        
                        // Notificar cambios
                        adapter.notifyDataSetChanged();
                        Log.d(TAG, "Se cargaron " + queryDocumentSnapshots.size() + " likes del usuario");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al cargar likes del usuario", e);
                });
        } catch (Exception e) {
            Log.e(TAG, "Error al cargar likes del usuario", e);
        }
    }
    
    /**
     * Elimina un comentario
     */
    private void deleteComment(Comment comment, int position) {
        // Mostrar diálogo de confirmación
        new AlertDialog.Builder(requireContext())
            .setTitle("Eliminar comentario")
            .setMessage("¿Estás seguro de que deseas eliminar este comentario?")
            .setPositiveButton("Eliminar", (dialog, which) -> {
                // Crear el ID esperado para el comentario basado en timestamp y userId
                String expectedCommentId = "comment_" + comment.getTimestamp() + "_" + comment.getUserId();
                Log.d(TAG, "Intentando eliminar comentario con ID: " + expectedCommentId);
                
                // Eliminar de Firestore usando el ID esperado
                db.collection("comentarios")
                    .whereEqualTo("id", expectedCommentId)
                    .get()
                    .addOnSuccessListener(queryDocumentSnapshots -> {
                        if (!queryDocumentSnapshots.isEmpty()) {
                            // Obtener el documento del comentario
                            String documentId = queryDocumentSnapshots.getDocuments().get(0).getId();
                            Log.d(TAG, "Comentario encontrado con documento ID: " + documentId);
                            
                            // Eliminar el documento
                            db.collection("comentarios").document(documentId)
                                .delete()
                                .addOnSuccessListener(aVoid -> {
                                    // Eliminar de la lista local
                                    commentList.remove(comment);
                                    filteredCommentList.remove(comment);
                                    adapter.notifyItemRemoved(position);
                                    
                                    Toast.makeText(getContext(), "Comentario eliminado", Toast.LENGTH_SHORT).show();
                                })
                                .addOnFailureListener(e -> {
                                    Log.e(TAG, "Error al eliminar comentario", e);
                                    Toast.makeText(getContext(), "Error al eliminar comentario", Toast.LENGTH_SHORT).show();
                                });
                        } else {
                            Log.e(TAG, "No se encontró el comentario con id: " + expectedCommentId);
                            Toast.makeText(getContext(), "No se pudo encontrar el comentario", Toast.LENGTH_SHORT).show();
                        }
                    })
                    .addOnFailureListener(e -> {
                        Log.e(TAG, "Error al buscar comentario para eliminar", e);
                        Toast.makeText(getContext(), "Error al eliminar comentario", Toast.LENGTH_SHORT).show();
                    });
            })
            .setNegativeButton("Cancelar", null)
            .show();
    }

    /**
     * Reporta un comentario inapropiado
     */
    private void reportComment(Comment comment) {
        try {
            // Verificar si el usuario está autenticado
            FirebaseUser currentUser = firebaseAuth.getCurrentUser();
            if (currentUser == null) {
                Toast.makeText(getContext(), "Debes iniciar sesión para reportar comentarios", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // Mostrar diálogo de confirmación con opciones
            AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
            builder.setTitle("Reportar comentario");
            
            // Opciones de reporte
            final String[] options = {
                "Contenido ofensivo", 
                "Spam", 
                "Información falsa", 
                "Acoso", 
                "Otro"
            };
            
            builder.setItems(options, (dialog, which) -> {
                String reason = options[which];
                
                // Crear objeto de reporte
                Map<String, Object> reportData = new HashMap<>();
                reportData.put("commentId", comment.getCommentId());
                reportData.put("commentText", comment.getText());
                reportData.put("commentUserId", comment.getUserId());
                reportData.put("reporterUserId", currentUser.getUid());
                reportData.put("reporterEmail", currentUser.getEmail());
                reportData.put("reason", reason);
                reportData.put("timestamp", com.google.firebase.Timestamp.now());
                reportData.put("status", "pending"); // pending, reviewed, rejected
                
                // Guardar reporte en Firestore
                db.collection("reportes")
                    .add(reportData)
                    .addOnSuccessListener(documentReference -> {
                        Toast.makeText(getContext(), "Comentario reportado correctamente", Toast.LENGTH_SHORT).show();
                        Log.d(TAG, "Reporte guardado con ID: " + documentReference.getId());
                    })
                    .addOnFailureListener(e -> {
                        Toast.makeText(getContext(), "Error al reportar comentario", Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "Error al guardar reporte", e);
                    });
            });
            
            builder.setNegativeButton("Cancelar", null);
            builder.show();
            
        } catch (Exception e) {
            Log.e(TAG, "Error al reportar comentario", e);
            Toast.makeText(getContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
}