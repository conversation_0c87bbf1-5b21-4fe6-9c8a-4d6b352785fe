package com.Rages.itatiexplore.firebase;

import android.app.Activity;
import android.content.Context;
import android.os.CancellationSignal;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.credentials.ClearCredentialStateRequest;
import androidx.credentials.CredentialManager;
import androidx.credentials.CredentialManagerCallback;
import androidx.credentials.GetCredentialRequest;
import androidx.credentials.GetCredentialResponse;
import androidx.credentials.exceptions.ClearCredentialException;
import androidx.credentials.exceptions.GetCredentialException;

import com.Rages.itatiexplore.models.User;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.libraries.identity.googleid.GetGoogleIdOption;
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential;
import com.google.firebase.auth.AuthCredential;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.auth.GoogleAuthProvider;
import com.google.firebase.firestore.FirebaseFirestore;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Helper class to handle authentication using Credential Manager and Firebase
 */
public class CredentialManagerAuthHelper {
    private static final String TAG = "CredentialManagerAuthHelper";
    private static final Executor executor = Executors.newFixedThreadPool(2);
    
    private final CredentialManager credentialManager;
    private final FirebaseAuth firebaseAuth;
    private final Context context;
    private final String clientId;
    private final String nonce;
    
    /**
     * Authentication callback interface
     */
    public interface AuthCallback {
        void onSuccess(FirebaseUser user);
        void onError(Exception e);
    }
    
    /**
     * Constructor for the CredentialManagerAuthHelper
     * 
     * @param context The context
     * @param clientId The Google OAuth client ID (from google-services.json)
     * @param nonce Optional nonce for security (can be null)
     */
    public CredentialManagerAuthHelper(Context context, String clientId, String nonce) {
        this.context = context;
        this.clientId = clientId;
        this.nonce = nonce;
        
        // Inicializar Credential Manager con manejo de error
        CredentialManager tempCredentialManager = null;
        try {
            tempCredentialManager = CredentialManager.create(context);
            Log.d(TAG, "Credential Manager inicializado correctamente");
        } catch (Exception e) {
            Log.e(TAG, "Error al inicializar Credential Manager", e);
        }
        this.credentialManager = tempCredentialManager;
        
        // Inicializar Firebase Auth con manejo de error
        FirebaseAuth tempFirebaseAuth = null;
        try {
            tempFirebaseAuth = FirebaseAuth.getInstance();
            Log.d(TAG, "Firebase Auth inicializado correctamente");
        } catch (Exception e) {
            Log.e(TAG, "Error al inicializar Firebase Auth", e);
        }
        this.firebaseAuth = tempFirebaseAuth;
    }
    
    /**
     * Verifica si este helper está correctamente inicializado
     * @return true si está listo para usar, false en caso contrario
     */
    public boolean isInitialized() {
        return credentialManager != null && firebaseAuth != null;
    }
    
    /**
     * Signs in the user using Credential Manager and Google ID
     * 
     * @param activity The activity
     * @param callback The callback to handle success/failure
     */
    public void signIn(Activity activity, AuthCallback callback) {
        Log.d(TAG, "Iniciando proceso de autenticación con Credential Manager");
        
        try {
            // Verificar que ningún parámetro sea nulo
            if (activity == null) {
                Log.e(TAG, "El parámetro activity es nulo");
                callback.onError(new IllegalArgumentException("Activity no puede ser nulo"));
                return;
            }
            
            if (callback == null) {
                Log.e(TAG, "El parámetro callback es nulo");
                throw new IllegalArgumentException("Callback no puede ser nulo");
            }
            
            if (credentialManager == null) {
                Log.e(TAG, "credentialManager es nulo, posiblemente no se inicializó correctamente");
                callback.onError(new IllegalStateException("Credential Manager no está inicializado"));
                return;
            }
            
            if (firebaseAuth == null) {
                Log.e(TAG, "firebaseAuth es nulo, posiblemente no se inicializó correctamente");
                callback.onError(new IllegalStateException("Firebase Auth no está inicializado"));
                return;
            }
            
            if (clientId == null || clientId.isEmpty()) {
                Log.e(TAG, "clientId es nulo o vacío");
                callback.onError(new IllegalStateException("Client ID no está configurado"));
                return;
            }
            
            Log.d(TAG, "Creando GetGoogleIdOption para autenticación");
            // Create the Google ID token request
            GetGoogleIdOption googleIdOption;
            try {
                googleIdOption = new GetGoogleIdOption.Builder()
                        .setFilterByAuthorizedAccounts(false)
                        .setServerClientId(clientId)
                        .setNonce(nonce)
                        .build();
                Log.d(TAG, "GetGoogleIdOption creado exitosamente");
            } catch (Exception e) {
                Log.e(TAG, "Error al crear GetGoogleIdOption", e);
                callback.onError(e);
                return;
            }
            
            Log.d(TAG, "Creando GetCredentialRequest para autenticación");
            // Create the credential request
            GetCredentialRequest request;
            try {
                request = new GetCredentialRequest.Builder()
                        .addCredentialOption(googleIdOption)
                        .build();
                Log.d(TAG, "GetCredentialRequest creado exitosamente");
            } catch (Exception e) {
                Log.e(TAG, "Error al crear GetCredentialRequest", e);
                callback.onError(e);
                return;
            }
            
            Log.d(TAG, "Llamando a getCredentialAsync");
            // Request the credential
            try {
                credentialManager.getCredentialAsync(
                        activity,
                        request,
                        new CancellationSignal(),
                        executor,
                        new CredentialManagerCallback<GetCredentialResponse, GetCredentialException>() {
                            @Override
                            public void onResult(GetCredentialResponse response) {
                                Log.d(TAG, "getCredentialAsync: onResult recibido");
                                try {
                                    // Handle the credential
                                    if (response == null) {
                                        Log.e(TAG, "La respuesta es nula");
                                        callback.onError(new IllegalStateException("Respuesta nula del Credential Manager"));
                                        return;
                                    }
                                    
                                    if (response.getCredential() == null) {
                                        Log.e(TAG, "La credencial en la respuesta es nula");
                                        callback.onError(new IllegalStateException("Credencial nula del Credential Manager"));
                                        return;
                                    }
                                    
                                    if (response.getCredential() instanceof GoogleIdTokenCredential) {
                                        Log.d(TAG, "Credencial es de tipo GoogleIdTokenCredential");
                                        GoogleIdTokenCredential credential = 
                                                (GoogleIdTokenCredential) response.getCredential();
                                        
                                        // Get the token and authenticate with Firebase
                                        String idToken = credential.getIdToken();
                                        if (idToken == null || idToken.isEmpty()) {
                                            Log.e(TAG, "ID Token es nulo o vacío");
                                            callback.onError(new IllegalStateException("ID Token nulo o vacío"));
                                            return;
                                        }
                                        
                                        Log.d(TAG, "ID Token obtenido, autenticando con Firebase");
                                        firebaseAuthWithGoogle(idToken, callback);
                                    } else {
                                        Log.e(TAG, "Tipo de credencial inesperado: " + 
                                              (response.getCredential() != null ? 
                                               response.getCredential().getClass().getName() : "null"));
                                        callback.onError(new Exception("Tipo de credencial inesperado recibido"));
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "Error procesando la credencial", e);
                                    callback.onError(e);
                                }
                            }

                            @Override
                            public void onError(GetCredentialException e) {
                                Log.e(TAG, "Error en getCredentialAsync", e);
                                callback.onError(e);
                            }
                        });
                Log.d(TAG, "getCredentialAsync llamado exitosamente");
            } catch (Exception e) {
                Log.e(TAG, "Error crítico al llamar a getCredentialAsync", e);
                callback.onError(e);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error general iniciando proceso de autenticación", e);
            callback.onError(e);
        }
    }
    
    /**
     * Authenticates with Firebase using the Google ID token
     * 
     * @param idToken The Google ID token
     * @param callback The callback to handle success/failure
     */
    private void firebaseAuthWithGoogle(String idToken, AuthCallback callback) {
        if (firebaseAuth == null) {
            Log.e(TAG, "Firebase Auth no está inicializado");
            callback.onError(new IllegalStateException("Firebase Auth no está inicializado"));
            return;
        }
        
        Log.d(TAG, "firebaseAuthWithGoogle:" + idToken.substring(0, Math.min(10, idToken.length())) + "...");
        
        AuthCredential credential = GoogleAuthProvider.getCredential(idToken, null);
        firebaseAuth.signInWithCredential(credential)
            .addOnCompleteListener((Activity) context, new OnCompleteListener<AuthResult>() {
                @Override
                public void onComplete(@NonNull Task<AuthResult> task) {
                    if (task.isSuccessful()) {
                        Log.d(TAG, "signInWithCredential:success");
                        FirebaseUser user = firebaseAuth.getCurrentUser();
                        
                        if (user == null) {
                            Log.e(TAG, "Usuario de Firebase es nulo después de autenticación exitosa");
                            callback.onError(new IllegalStateException("Usuario de Firebase es nulo"));
                            return;
                        }
                        
                        Log.d(TAG, "Usuario autenticado: " + user.getUid());
                        
                        // Crear y guardar el usuario en Firestore inmediatamente
                        FirebaseFirestore.getInstance()
                            .collection("users")
                            .document(user.getUid())
                            .set(new User(
                                user.getUid(),
                                user.getEmail(),
                                user.getDisplayName(),
                                user.getPhotoUrl() != null ? user.getPhotoUrl().toString() : null
                            ))
                            .addOnSuccessListener(aVoid -> {
                                Log.d(TAG, "Usuario guardado en Firestore exitosamente");
                                callback.onSuccess(user);
                            })
                            .addOnFailureListener(e -> {
                                Log.e(TAG, "Error al guardar usuario en Firestore", e);
                                // Aún así notificamos éxito en la autenticación
                                callback.onSuccess(user);
                            });
                    } else {
                        Log.w(TAG, "signInWithCredential:failure", task.getException());
                        callback.onError(task.getException());
                    }
                }
            });
    }
    
    /**
     * Signs out the user and clears credential state
     * 
     * @param callback Optional callback to notify when sign out is complete
     */
    public void signOut(AuthCallback callback) {
        // Verificar inicialización
        if (firebaseAuth == null) {
            Log.e(TAG, "Firebase Auth no está inicializado, no se puede cerrar sesión");
            if (callback != null) {
                callback.onError(new IllegalStateException("Firebase Auth no está inicializado"));
            }
            return;
        }
        
        if (credentialManager == null) {
            Log.e(TAG, "Credential Manager no está inicializado, sólo se cerrará sesión de Firebase");
            // Firebase sign out
            try {
                firebaseAuth.signOut();
                Log.d(TAG, "Firebase Auth sign out exitoso");
                if (callback != null) {
                    callback.onSuccess(null);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error al cerrar sesión de Firebase", e);
                if (callback != null) {
                    callback.onError(e);
                }
            }
            return;
        }
        
        // Firebase sign out
        firebaseAuth.signOut();
        
        // When a user signs out, clear the current user credential state from all credential providers
        try {
            ClearCredentialStateRequest clearRequest = new ClearCredentialStateRequest();
            credentialManager.clearCredentialStateAsync(
                    clearRequest,
                    new CancellationSignal(),
                    executor,
                    new CredentialManagerCallback<Void, ClearCredentialException>() {
                        @Override
                        public void onResult(Void result) {
                            Log.d(TAG, "Credential state cleared successfully");
                            if (callback != null) {
                                callback.onSuccess(null);
                            }
                        }
                        
                        @Override
                        public void onError(ClearCredentialException e) {
                            Log.e(TAG, "Couldn't clear credential state: " + e.getLocalizedMessage());
                            if (callback != null) {
                                callback.onError(e);
                            }
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error general al intentar limpiar estado de credenciales", e);
            if (callback != null) {
                callback.onError(e);
            }
        }
    }
    
    /**
     * Signs out the user (simplified version without callback)
     */
    public void signOut() {
        signOut(null);
    }
    
    /**
     * Gets the current user
     * 
     * @return The current Firebase user, or null if not signed in
     */
    public FirebaseUser getCurrentUser() {
        if (firebaseAuth == null) {
            Log.e(TAG, "Firebase Auth no está inicializado, no se puede obtener el usuario");
            return null;
        }
        return firebaseAuth.getCurrentUser();
    }
} 