package com.Rages.itatiexplore.adapters;

import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.Rages.itatiexplore.EventDetailActivity;
import com.Rages.itatiexplore.R;
import com.Rages.itatiexplore.models.Event;
import com.Rages.itatiexplore.utils.DateUtils;
import com.Rages.itatiexplore.utils.ImageUtils;
import com.bumptech.glide.Glide;

import java.util.List;

public class EventAdapter extends RecyclerView.Adapter<EventAdapter.EventViewHolder> {
    
    private final List<Event> eventList;
    private boolean isInDetailView = false;  // Flag para saber si estamos en la vista de detalle
    private EventClickListener eventClickListener;
    
    public interface EventClickListener {
        void onEventClick(Event event);
    }
    
    public EventAdapter(List<Event> eventList) {
        this(eventList, false);
    }
    
    public EventAdapter(List<Event> eventList, boolean isInDetailView) {
        this.eventList = eventList;
        this.isInDetailView = isInDetailView;
    }
    
    public EventAdapter(List<Event> eventList, EventClickListener listener) {
        this(eventList, false);
        this.eventClickListener = listener;
    }
    
    public void setEventClickListener(EventClickListener listener) {
        this.eventClickListener = listener;
    }
    
    @NonNull
    @Override
    public EventViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_event, parent, false);
        return new EventViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull EventViewHolder holder, int position) {
        Event event = eventList.get(position);
        
        // Configurar título del evento
        holder.titleTextView.setText(event.getTitle());
        
        // Configurar fecha y hora
        holder.dateTextView.setText(DateUtils.formatDateToReadable(event.getDate()));
        
        // Configurar hora si está disponible
        if (event.getTime() != null && !event.getTime().isEmpty()) {
            holder.timeTextView.setVisibility(View.VISIBLE);
            holder.timeTextView.setText("• " + event.getTime());
        } else {
            holder.timeTextView.setVisibility(View.GONE);
        }
        
        // Configurar ubicación si está disponible
        if (event.getLocation() != null && !event.getLocation().isEmpty()) {
            holder.locationTextView.setVisibility(View.VISIBLE);
            holder.locationTextView.setText(event.getLocation());
        } else {
            holder.locationTextView.setVisibility(View.GONE);
        }
        
        // Configurar descripción
        holder.descriptionTextView.setText(event.getDescription());
        
        // Configurar categoría
        if (event.getCategory() != null && !event.getCategory().isEmpty()) {
            holder.categoryChip.setVisibility(View.VISIBLE);
            holder.categoryChip.setText(event.getCategory());
        } else {
            // Si no hay categoría, mostrar si es evento principal o no
            holder.categoryChip.setVisibility(View.VISIBLE);
            holder.categoryChip.setText(event.isMainEvent() ? "Destacado" : "Evento");
        }
        
        // Cargar imagen con Glide si hay url de imagen
        if (event.getImageUrl() != null && !event.getImageUrl().isEmpty()) {
            // Convertir URL si es de Google Drive
            String directUrl = ImageUtils.getDirectImageUrl(event.getImageUrl());
            Log.d("EventAdapter", "Cargando imagen: " + event.getImageUrl());
            Log.d("EventAdapter", "URL procesada: " + directUrl);
            
            Glide.with(holder.itemView.getContext())
                .load(directUrl)
                .placeholder(R.drawable.event_placeholder)
                .error(R.drawable.event_placeholder)
                .centerCrop()
                .into(holder.imageView);
        } else {
            // Usar un recurso de imagen predeterminado
            holder.imageView.setImageResource(R.drawable.event_placeholder);
        }
        
        // Configurar botón de detalles
        holder.viewDetailsButton.setOnClickListener(v -> {
            if (eventClickListener != null) {
                eventClickListener.onEventClick(event);
            } else {
                openEventDetail(v, event);
            }
        });
        
        // Botón de compartir eliminado
        
        // Configurar listener de clics para toda la tarjeta
        holder.itemView.setOnClickListener(v -> {
            if (eventClickListener != null) {
                eventClickListener.onEventClick(event);
            } else {
                openEventDetail(v, event);
            }
        });
    }
    
    // Método shareEvent eliminado
    
    private void openEventDetail(View view, Event event) {
        Intent intent = new Intent(view.getContext(), EventDetailActivity.class);
        
        // Pasar el ID del evento si está disponible
        if (event.getId() != null) {
            intent.putExtra(EventDetailActivity.EXTRA_EVENT_ID, event.getId());
        }
        
        // Pasar el resto de datos del evento
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_TITLE, event.getTitle());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_DATE, event.getDate());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_TIME, event.getTime());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_LOCATION, event.getLocation());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_DESCRIPTION, event.getDescription());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_IMAGE_URL, event.getImageUrl());
        intent.putExtra(EventDetailActivity.EXTRA_IS_MAIN_EVENT, event.isMainEvent());
        
        if (event.isMainEvent()) {
            intent.putExtra(EventDetailActivity.EXTRA_MAIN_EVENT_ID, event.getId());
        } else if (event.getMainEventId() != null) {
            intent.putExtra(EventDetailActivity.EXTRA_MAIN_EVENT_ID, event.getMainEventId());
        }
        
        view.getContext().startActivity(intent);
    }
    
    @Override
    public int getItemCount() {
        return eventList.size();
    }
    
    public static class EventViewHolder extends RecyclerView.ViewHolder {
        final ImageView imageView;
        final TextView titleTextView;
        final TextView dateTextView;
        final TextView timeTextView;
        final TextView locationTextView;
        final TextView descriptionTextView;
        final com.google.android.material.chip.Chip categoryChip;
        final com.google.android.material.button.MaterialButton viewDetailsButton;
        // Botón de compartir eliminado
        
        public EventViewHolder(@NonNull View itemView) {
            super(itemView);
            
            imageView = itemView.findViewById(R.id.event_image);
            titleTextView = itemView.findViewById(R.id.event_title);
            dateTextView = itemView.findViewById(R.id.event_date);
            timeTextView = itemView.findViewById(R.id.event_time);
            locationTextView = itemView.findViewById(R.id.event_location);
            descriptionTextView = itemView.findViewById(R.id.event_description);
            categoryChip = itemView.findViewById(R.id.event_category_chip);
            viewDetailsButton = itemView.findViewById(R.id.btn_view_details);
            // Referencia al botón de compartir eliminada
        }
    }
} 