package com.Rages.itatiexplore.models;

/**
 * Modelo para representar un lugar turístico en la aplicación.
 */
public class Location {
    private String id;
    private String name;
    private String category;
    private String address;
    private String hours;
    private String description;
    private String imageUrl;
    private double latitude;
    private double longitude;

    /**
     * Constructor vacío requerido para Firebase
     */
    public Location() {
    }

    /**
     * Constructor completo para crear un lugar turístico
     */
    public Location(String id, String name, String category, String address, String hours, 
                   String description, String imageUrl, double latitude, double longitude) {
        this.id = id;
        this.name = name;
        this.category = category;
        this.address = address;
        this.hours = hours;
        this.description = description;
        this.imageUrl = imageUrl;
        this.latitude = latitude;
        this.longitude = longitude;
    }

    // Getters y setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getHours() {
        return hours;
    }

    public void setHours(String hours) {
        this.hours = hours;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
}