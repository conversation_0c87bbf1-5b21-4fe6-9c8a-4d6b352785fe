<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="10dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:strokeWidth="0dp"
    app:cardBackgroundColor="@color/white">

    <!-- Diseño principal con borde superior de color -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Borde superior de color -->
        <View
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="@color/primary" />

        <!-- Contenido principal -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Cabecera con avatar y datos del usuario -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="10dp">

                <!-- Avatar del usuario -->
                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/user_avatar"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_person"
                    android:scaleType="centerCrop"
                    app:civ_border_width="2dp"
                    app:civ_border_color="@color/primary" 
                    app:civ_circle_background_color="@color/gray_50"/>

                <!-- Información del usuario -->
                <LinearLayout
                    android:id="@+id/user_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="10dp"
                    android:orientation="vertical"
                    android:layout_gravity="center_vertical">

                    <!-- Nombre de usuario -->
                    <TextView
                        android:id="@+id/user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/on_surface"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/montserrat"
                        android:maxLines="1"
                        android:ellipsize="end"
                        tools:text="Nombre de Usuario" />
                        
                    <!-- Ubicación (debajo del nombre) -->
                    <TextView
                        android:id="@+id/comment_location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="0dp"
                        android:drawableStart="@drawable/ic_location"
                        android:drawablePadding="2dp"
                        android:drawableTint="@color/primary"
                        android:textColor="@color/primary"
                        android:textSize="12sp"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:ellipsize="end"
                        android:fontFamily="@font/montserrat"
                        tools:text="Restaurante El Paran, 08/05/2025 14:22" />
                </LinearLayout>

                <!-- Columna derecha (rating arriba, fecha abajo) -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="end"
                    android:layout_marginStart="4dp"
                    android:layout_gravity="center_vertical">
                    
                    <!-- Calificación con estrellas (arriba a la derecha) -->
                    <RatingBar
                        android:id="@+id/comment_rating"
                        style="?android:attr/ratingBarStyleSmall"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:isIndicator="true"
                        android:numStars="5"
                        android:stepSize="0.5"
                        android:progressTint="@color/gold"
                        android:secondaryProgressTint="@color/gold"
                        tools:rating="4.5" />
                        
                    <!-- Fecha y hora (debajo del rating) -->
                    <TextView
                        android:id="@+id/comment_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:textColor="@color/on_surface_medium"
                        android:textSize="10sp"
                        android:fontFamily="@font/montserrat"
                        tools:text="10/09/2023 15:30" />
                </LinearLayout>
            </LinearLayout>

            <!-- Línea divisoria -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray_50"
                android:layout_marginHorizontal="14dp" />

            <!-- Contenido del comentario -->
            <TextView
                android:id="@+id/comment_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:textColor="@color/on_surface"
                android:textSize="14sp"
                android:lineSpacingMultiplier="1.2"
                android:fontFamily="@font/montserrat"
                tools:text="Este es el texto del comentario que puede ocupar varias líneas y brinda información sobre la experiencia del usuario. Puede ser bastante extenso y detallado." />

            <!-- Barra de acciones -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:gravity="center_vertical"
                android:background="@color/gray_50">

                <!-- Botón de me gusta con contador integrado -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/rounded_button_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:id="@+id/like_button"
                    android:foreground="?attr/selectableItemBackground">
                    
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_thumb_up"
                        android:contentDescription="Me gusta"
                        app:tint="@color/primary" />
    
                    <!-- Contador de likes -->
                    <TextView
                        android:id="@+id/likes_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:textColor="@color/primary"
                        android:textSize="15sp"
                        android:fontFamily="@font/montserrat"
                        android:textStyle="bold"
                        tools:text="12" />
                </LinearLayout>

                <!-- Espaciador -->
                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <!-- Botón de opciones -->
                <ImageButton
                    android:id="@+id/btn_options"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@android:drawable/ic_menu_more"
                    android:contentDescription="Más opciones"
                    app:tint="@color/on_surface_medium" />
            </LinearLayout>
            
            <!-- Eliminamos el icono de like de la parte inferior -->
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>