<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/>
    <style name="Theme.ItatiExplore" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/dark_primary</item>
        <item name="colorPrimaryVariant">@color/dark_primary</item>
        <item name="colorPrimaryContainer">@color/dark_primary</item>
        <item name="colorOnPrimary">@color/dark_primary_foreground</item>
        
        
        <item name="colorSecondary">@color/dark_secondary</item>
        <item name="colorSecondaryVariant">@color/dark_secondary</item>
        <item name="colorSecondaryContainer">@color/dark_secondary</item>
        <item name="colorOnSecondary">@color/dark_secondary_foreground</item>
        
        
        <item name="colorAccent">@color/dark_accent</item>
        
        
        <item name="colorError">@color/dark_destructive</item>
        <item name="colorOnError">@color/dark_destructive_foreground</item>
        
        
        <item name="android:colorBackground">@color/dark_background</item>
        <item name="colorOnBackground">@color/dark_foreground</item>
        
        
        <item name="colorSurface">@color/dark_card</item>
        <item name="colorOnSurface">@color/dark_card_foreground</item>
        <item name="colorSurfaceVariant">@color/dark_muted</item>
        <item name="colorOnSurfaceVariant">@color/dark_muted_foreground</item>
        
        
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/dark_card</item>
        <item name="android:windowLightStatusBar" ns1:targetApi="23">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        
        
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.ItatiExplore.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.ItatiExplore.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.ItatiExplore.LargeComponent</item>
        
        
        <item name="materialButtonStyle">@style/Widget.ItatiExplore.Button.Dark</item>
        <item name="materialCardViewStyle">@style/Widget.ItatiExplore.CardView.Dark</item>
        <item name="bottomNavigationStyle">@style/Widget.ItatiExplore.BottomNavigation.Dark</item>
        <item name="toolbarStyle">@style/Widget.ItatiExplore.Toolbar.Dark</item>
        <item name="tabStyle">@style/Widget.ItatiExplore.TabLayout.Dark</item>
        <item name="chipStyle">@style/Widget.ItatiExplore.Chip.Dark</item>
        <item name="textInputStyle">@style/Widget.ItatiExplore.TextInputLayout.Dark</item>
    </style>
    <style name="Theme.Material3.DayNight" parent="Theme.Material3.Dark"/>
    <style name="Theme.Material3.DayNight.BottomSheetDialog" parent="Theme.Material3.Dark.BottomSheetDialog"/>
    <style name="Theme.Material3.DayNight.Dialog" parent="Theme.Material3.Dark.Dialog"/>
    <style name="Theme.Material3.DayNight.Dialog.Alert" parent="Theme.Material3.Dark.Dialog.Alert"/>
    <style name="Theme.Material3.DayNight.Dialog.MinWidth" parent="Theme.Material3.Dark.Dialog.MinWidth"/>
    <style name="Theme.Material3.DayNight.DialogWhenLarge" parent="Theme.Material3.Dark.DialogWhenLarge"/>
    <style name="Theme.Material3.DayNight.NoActionBar" parent="Theme.Material3.Dark.NoActionBar"/>
    <style name="Theme.Material3.DayNight.SideSheetDialog" parent="Theme.Material3.Dark.SideSheetDialog"/>
    <style name="Theme.Material3.DynamicColors.DayNight" parent="Theme.Material3.DynamicColors.Dark"/>
    <style name="Theme.MaterialComponents.DayNight" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="Theme.MaterialComponents.BottomSheetDialog"/>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="Theme.MaterialComponents.Dialog.Alert.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="Theme.MaterialComponents.Dialog.FixedSize.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="Theme.MaterialComponents.Dialog.MinWidth.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="Theme.MaterialComponents.DialogWhenLarge"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="Theme.MaterialComponents.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="Theme.MaterialComponents.NoActionBar.Bridge"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.Material3.DynamicColors.DayNight" parent="ThemeOverlay.Material3.DynamicColors.Dark"/>
    <style name="Widget.ItatiExplore.BottomNavigation.Dark" parent="Widget.ItatiExplore.BottomNavigation">
        <item name="backgroundTint">@color/dark_card</item>
        <item name="itemIconTint">@color/dark_primary</item>
        <item name="itemTextColor">@color/dark_primary</item>
    </style>
    <style name="Widget.ItatiExplore.Button.Dark" parent="Widget.ItatiExplore.Button">
        <item name="backgroundTint">@color/dark_primary</item>
        <item name="android:textColor">@color/dark_primary_foreground</item>
    </style>
    <style name="Widget.ItatiExplore.CardView.Dark" parent="Widget.ItatiExplore.CardView">
        <item name="cardBackgroundColor">@color/dark_card</item>
    </style>
    <style name="Widget.ItatiExplore.Chip.Dark" parent="Widget.ItatiExplore.Chip">
        <item name="chipBackgroundColor">@color/dark_muted</item>
        <item name="chipStrokeColor">@color/dark_primary</item>
        <item name="chipIconTint">@color/dark_primary</item>
        <item name="android:textColor">@color/dark_foreground</item>
    </style>
    <style name="Widget.ItatiExplore.TabLayout.Dark" parent="Widget.ItatiExplore.TabLayout">
        <item name="android:background">@color/dark_card</item>
        <item name="tabTextColor">@color/dark_muted_foreground</item>
        <item name="tabSelectedTextColor">@color/dark_primary</item>
        <item name="tabIndicatorColor">@color/dark_primary</item>
    </style>
    <style name="Widget.ItatiExplore.TextInputLayout.Dark" parent="Widget.ItatiExplore.TextInputLayout">
        <item name="boxStrokeColor">@color/dark_primary</item>
        <item name="hintTextColor">@color/dark_primary</item>
        <item name="android:textColorHint">@color/dark_muted_foreground</item>
    </style>
    <style name="Widget.ItatiExplore.Toolbar.Dark" parent="Widget.ItatiExplore.Toolbar">
        <item name="android:background">@color/dark_card</item>
        <item name="titleTextColor">@color/dark_foreground</item>
        <item name="subtitleTextColor">@color/dark_muted_foreground</item>
        <item name="colorControlNormal">@color/dark_foreground</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="Widget.MaterialComponents.ActionBar.Surface"/>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="Widget.MaterialComponents.AppBarLayout.Surface"/>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="Widget.MaterialComponents.BottomAppBar"/>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="Widget.MaterialComponents.BottomNavigationView"/>
    <style name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" parent="Widget.MaterialComponents.NavigationRailView"/>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="Widget.MaterialComponents.TabLayout"/>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="Widget.MaterialComponents.Toolbar.Surface"/>
</resources>