#Tue May 27 17:10:59 ART 2025
com.Rages.itatiexplore.app-main-62\:/color/bottom_nav_item_color.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\color_bottom_nav_item_color.xml.flat
com.Rages.itatiexplore.app-main-62\:/color/chip_background_color.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\color_chip_background_color.xml.flat
com.Rages.itatiexplore.app-main-62\:/color/chip_text_color.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\color_chip_text_color.xml.flat
com.Rages.itatiexplore.app-main-62\:/color/copy_button_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\color_copy_button_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/color/copy_status_text_color.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\color_copy_status_text_color.xml.flat
com.Rages.itatiexplore.app-main-62\:/color/nav_item_background_color.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\color_nav_item_background_color.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/actions_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_actions_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/baseline_add_24.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_baseline_add_24.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/baseline_location_on_24.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_baseline_location_on_24.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/baseline_my_location_24.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_baseline_my_location_24.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/baseline_remove_24.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_baseline_remove_24.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/category_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_category_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/category_button_selector.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_category_button_selector.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/category_pill_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_category_pill_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/chip_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_chip_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/chip_background_selected.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_chip_background_selected.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/circle_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/circle_shape.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_shape.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/comment_actions_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_comment_actions_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/comment_text_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_comment_text_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/edit_text_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_edit_text_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/event_placeholder.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_event_placeholder.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/gradient_overlay.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_overlay.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/gradient_scrim.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_scrim.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_add.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_add.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_add_comment.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_add_comment.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_add_transport.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_add_transport.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_atm.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_atm.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_attraction.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_attraction.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_bathroom.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bathroom.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_church.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_church.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_comment.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_comment.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_comments.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_comments.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_description.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_description.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_destination.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_destination.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_donate.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_donate.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_donations.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_donations.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_event.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_event.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_events.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_events.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_filter.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_filter.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_hotel.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_hotel.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_info.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_info.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_launcher_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_location.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_location.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_map.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_map.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_mercado_pago.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_mercado_pago.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_money.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_money.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_my_location.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_my_location.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_person.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_pharmacy.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_pharmacy.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_phone.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_phone.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_reply.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_reply.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_restaurant.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_restaurant.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_schedule.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_schedule.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_search.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_search.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_send.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_send.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_sort.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_sort.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_stat_ic_notification.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_stat_ic_notification.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_thumb_up.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_thumb_up.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_transport.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_transport.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_website.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_website.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_whatsapp.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_whatsapp.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_zoom_in.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_zoom_in.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/ic_zoom_out.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_zoom_out.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/image_gradient.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_image_gradient.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_attraction.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_attraction.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_bathroom.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_bathroom.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_church.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_church.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_city.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_city.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_hotel.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_hotel.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_restaurant.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_restaurant.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/img_transport.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_img_transport.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/itati_placeholder.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_itati_placeholder.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/like_button_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_like_button_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/like_count_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_like_count_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/location_badge_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_location_badge_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/login_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_login_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/map_placeholder.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_map_placeholder.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/modern_button_primary.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_modern_button_primary.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/modern_card_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_modern_card_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/modern_chip_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_modern_chip_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/modern_gradient_header.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_modern_gradient_header.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/modern_gradient_header_dark.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_modern_gradient_header_dark.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/nav_header_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_nav_header_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/negative_highlight_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_negative_highlight_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/options_button_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_options_button_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/placeholder_image.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_placeholder_image.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/positive_highlight_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_positive_highlight_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/reply_button_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_reply_button_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/rounded_button_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_button_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/rounded_light_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_light_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/rounded_tag_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_tag_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/spinner_background.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_spinner_background.xml.flat
com.Rages.itatiexplore.app-main-62\:/drawable/white_circle.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\drawable_white_circle.xml.flat
com.Rages.itatiexplore.app-main-62\:/font/montserrat.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\font_montserrat.xml.flat
com.Rages.itatiexplore.app-main-62\:/font/montserrat_medium.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\font_montserrat_medium.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout-sw600dp/activity_main_app_new.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout-sw600dp_activity_main_app_new.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout-sw600dp/activity_main_modern.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout-sw600dp_activity_main_modern.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_add_place.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_add_place.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_basic_main.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_basic_main.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_event_detail.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_event_detail.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_main.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_main_app.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main_app.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_main_app_new.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main_app_new.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_main_modern.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main_modern.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/activity_transport_detail.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_transport_detail.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/dialog_location_comments.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_location_comments.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/dialog_location_selector.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_location_selector.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/filter_spinner_dropdown_item.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_filter_spinner_dropdown_item.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/filter_spinner_item.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_filter_spinner_item.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_comments.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_comments.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_donations.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_donations.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_error.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_error.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_events.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_events.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_info.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_info.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_locations.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_locations.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_map.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_map.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_transport.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_transport.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/fragment_transport_type.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_fragment_transport_type.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_comment.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_comment.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_comment_image.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_comment_image.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_event.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_event.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_featured_event.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_featured_event.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_location.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_location.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_photo_preview.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_photo_preview.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_place_spinner.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_place_spinner.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_reply.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_reply.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/item_transport.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_item_transport.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/nav_header.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_nav_header.xml.flat
com.Rages.itatiexplore.app-main-62\:/layout/navigation_rail_header.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\layout_navigation_rail_header.xml.flat
com.Rages.itatiexplore.app-main-62\:/menu/bottom_navigation_menu.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\menu_bottom_navigation_menu.xml.flat
com.Rages.itatiexplore.app-main-62\:/menu/comment_options_menu.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\menu_comment_options_menu.xml.flat
com.Rages.itatiexplore.app-main-62\:/menu/drawer_menu.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\menu_drawer_menu.xml.flat
com.Rages.itatiexplore.app-main-62\:/menu/top_app_bar.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\menu_top_app_bar.xml.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_foreground.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_foreground.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_foreground.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat
com.Rages.itatiexplore.app-main-62\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.Rages.itatiexplore.app-main-62\:/navigation/nav_graph.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\navigation_nav_graph.xml.flat
com.Rages.itatiexplore.app-main-62\:/xml/backup_rules.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.Rages.itatiexplore.app-main-62\:/xml/data_extraction_rules.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.Rages.itatiexplore.app-main-62\:/xml/network_security_config.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\xml_network_security_config.xml.flat
com.Rages.itatiexplore.app-main-62\:/xml/remote_config_defaults.xml=C\:\\Users\\Rages\\AndroidStudioProjects\\ItatiExplore\\app\\build\\intermediates\\merged_res\\debug\\xml_remote_config_defaults.xml.flat
