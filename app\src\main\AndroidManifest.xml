<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permisos necesarios -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Permisos adicionales para osmdroid -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- Permisos para Credential Manager -->
    <uses-permission android:name="android.permission.CREDENTIAL_MANAGER" />
    <!-- Permiso para el portapapeles -->
    <uses-permission android:name="android.permission.CLIPBOARD_READ" />
    <uses-permission android:name="android.permission.CLIPBOARD_WRITE" />
    <!-- Permisos para trabajo en segundo plano -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.ItatiExplore"
        tools:targetApi="31"
        tools:replace="android:allowBackup"
        android:usesCleartextTraffic="false"
        android:networkSecurityConfig="@xml/network_security_config">
        
        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_ic_notification" />
        
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming notification message. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />
        
        <!-- Actividad principal que muestra la pantalla de inicio -->
        <activity
            android:name=".MainActivity"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- Actividad de la aplicación principal -->
        <activity
            android:name=".MainAppActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:label="@string/app_name" />
            
        <!-- Actividad de detalles de eventos -->
        <activity
            android:name=".EventDetailActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:parentActivityName=".MainAppActivity"
            android:label="Detalles del Evento" />
        
        <!-- Actividad de agregar lugar -->
        <activity
            android:name=".activities.AddPlaceActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:parentActivityName=".MainAppActivity"
            android:theme="@style/Theme.ItatiExplore" />
            
        <!-- Actividad de detalles de transporte -->
        <activity
            android:name=".activities.TransportDetailActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:parentActivityName=".MainAppActivity"
            android:label="Detalles del Transporte"
            android:theme="@style/Theme.ItatiExplore" />
        
        <!-- Firebase Cloud Messaging Service -->
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        
    </application>

</manifest>