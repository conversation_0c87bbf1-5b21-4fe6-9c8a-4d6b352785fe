<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.ItatiExplore" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary</item>
        <item name="colorPrimaryContainer">@color/primary</item>
        <item name="colorOnPrimary">@color/primary_foreground</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary</item>
        <item name="colorSecondaryContainer">@color/secondary</item>
        <item name="colorOnSecondary">@color/secondary_foreground</item>
        
        <!-- Accent color for components -->
        <item name="colorAccent">@color/accent</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/destructive</item>
        <item name="colorOnError">@color/destructive_foreground</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/foreground</item>
        
        <!-- Surface colors -->
        <item name="colorSurface">@color/card</item>
        <item name="colorOnSurface">@color/card_foreground</item>
        <item name="colorSurfaceVariant">@color/muted</item>
        <item name="colorOnSurfaceVariant">@color/muted_foreground</item>
        
        <!-- Status bar and navigation bar -->
        <item name="android:statusBarColor">@color/card</item>
        <item name="android:navigationBarColor">@color/card</item>
        <item name="android:windowLightStatusBar" tools:targetApi="23">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">true</item>
        
        <!-- Shape and typography customization -->
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.ItatiExplore.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.ItatiExplore.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.ItatiExplore.LargeComponent</item>
        
        <!-- Text appearance -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.ItatiExplore.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.ItatiExplore.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.ItatiExplore.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.ItatiExplore.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.ItatiExplore.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.ItatiExplore.Headline6</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.ItatiExplore.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.ItatiExplore.Subtitle2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.ItatiExplore.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.ItatiExplore.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.ItatiExplore.Caption</item>
        <item name="textAppearanceButton">@style/TextAppearance.ItatiExplore.Button</item>
        <item name="textAppearanceOverline">@style/TextAppearance.ItatiExplore.Overline</item>
        
        <!-- Component styles -->
        <item name="materialButtonStyle">@style/Widget.ItatiExplore.Button</item>
        <item name="materialCardViewStyle">@style/Widget.ItatiExplore.CardView</item>
        <item name="bottomNavigationStyle">@style/Widget.ItatiExplore.BottomNavigation</item>
        <item name="toolbarStyle">@style/Widget.ItatiExplore.Toolbar</item>
        <item name="tabStyle">@style/Widget.ItatiExplore.TabLayout</item>
        <item name="chipStyle">@style/Widget.ItatiExplore.Chip</item>
        <item name="textInputStyle">@style/Widget.ItatiExplore.TextInputLayout</item>
    </style>
    
    <!-- Los estilos se han movido al archivo styles.xml para evitar duplicados -->
</resources>