<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.TransportFragment">

    <!-- AppBarLayout para el encabezado -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <!-- Cabecera con título y logo -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/header_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="0dp"
            app:cardElevation="4dp"
            app:layout_scrollFlags="scroll|enterAlways">

            <LinearLayout
                android:id="@+id/title_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/primary"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingBottom="16dp">

                <!-- Título con logo -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:src="@android:drawable/ic_menu_directions"
                        app:tint="@color/white" />

                    <TextView
                        android:id="@+id/transport_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Transporte"
                        android:textColor="@android:color/white"
                        android:textSize="18sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- Subtítulo -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Opciones para llegar a Itatí"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:paddingHorizontal="16dp"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Contenido principal -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Tabs de categorías de transporte -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/transport_tab_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:tabGravity="center"
                    app:tabIconTint="@color/primary"
                    app:tabIndicatorColor="@color/primary"
                    app:tabIndicatorHeight="3dp"
                    app:tabInlineLabel="true"
                    app:tabMode="auto"
                    app:tabRippleColor="@color/ripple"
                    app:tabSelectedTextColor="@color/primary"
                    app:tabTextColor="@color/on_surface_medium"
                    android:visibility="visible" />
            </com.google.android.material.card.MaterialCardView>

            <!-- ViewPager para mostrar el contenido de cada categoría -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/transport_view_pager"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginTop="16dp"
                android:visibility="visible" />

            <!-- Contenedor de carga -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/loading_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="32dp">

                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:id="@+id/progress_indicator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:indeterminate="true"
                            app:indicatorColor="@color/primary"
                            app:indicatorSize="56dp"
                            app:trackThickness="4dp" />

                        <TextView
                            android:id="@+id/empty_view"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:fontFamily="@font/montserrat"
                            android:gravity="center"
                            android:text="Cargando opciones de transporte..."
                            android:textColor="@color/on_surface"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/montserrat"
                            android:gravity="center"
                            android:text="Estamos obteniendo la información más actualizada"
                            android:textColor="@color/on_surface_medium"
                            android:textSize="14sp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Quitamos el botón flotante para actualizar transporte -->

</androidx.coordinatorlayout.widget.CoordinatorLayout>