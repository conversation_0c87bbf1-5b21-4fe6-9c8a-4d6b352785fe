<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/primary_variant"
                android:endColor="@color/primary"
                android:type="linear" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/primary"
                android:endColor="@color/primary_light"
                android:type="linear" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
