<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="8dp">

    <ImageView
        android:id="@+id/reply_user_avatar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="@drawable/circle_background"
        android:padding="6dp"
        android:src="@drawable/ic_person"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/reply_user_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="?attr/colorOnSurface"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/reply_user_avatar"
        app:layout_constraintTop_toTopOf="@+id/reply_user_avatar"
        tools:text="Nombre de Usuario" />

    <TextView
        android:id="@+id/reply_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/gray_500"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/reply_user_name"
        app:layout_constraintStart_toEndOf="@+id/reply_user_name"
        app:layout_constraintTop_toTopOf="@+id/reply_user_name"
        tools:text="10/09/2023" />

    <TextView
        android:id="@+id/reply_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="4dp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/reply_user_avatar"
        app:layout_constraintTop_toBottomOf="@+id/reply_user_name"
        tools:text="Esta es una respuesta al comentario principal que puede tener varias líneas de texto." />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="@+id/reply_text"
        app:layout_constraintTop_toBottomOf="@+id/reply_text">

        <TextView
            android:id="@+id/reply_like_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_thumb_up"
            android:drawablePadding="4dp"
            android:drawableTint="@color/gray_500"
            android:padding="4dp"
            android:text="Me gusta"
            android:textColor="@color/gray_700"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/reply_likes_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textColor="@color/gray_700"
            android:textSize="12sp"
            tools:text="3" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>