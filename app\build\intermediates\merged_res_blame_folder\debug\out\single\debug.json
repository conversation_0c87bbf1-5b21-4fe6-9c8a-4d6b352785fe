[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_login_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\login_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_money.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_money.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_hotel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_hotel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\color_chip_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\color\\chip_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_thumb_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_thumb_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\color_nav_item_background_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\color\\nav_item_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_baseline_add_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\baseline_add_24.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/layout_activity_main_app.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/layout/activity_main_app.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_send.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_send.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_options_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\options_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_dialog_location_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\dialog_location_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_comment_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_comment_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_zoom_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_zoom_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_church.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_church.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_place_spinner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_place_spinner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_add_comment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_add_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_events.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_events.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout-sw600dp_activity_main_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout-sw600dp\\activity_main_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\font_montserrat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\font\\montserrat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_circle_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\circle_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\xml\\network_security_config.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/layout_fragment_map.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/layout/fragment_map.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_navigation_rail_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\navigation_rail_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_like_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\like_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_event_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\event_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_baseline_my_location_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\baseline_my_location_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_add_place.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_add_place.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout-sw600dp_activity_main_app_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout-sw600dp\\activity_main_app_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_negative_highlight_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\negative_highlight_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_map.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_map.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_actions_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\actions_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_bathroom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_bathroom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_filter_spinner_dropdown_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\filter_spinner_dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_reply.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_reply.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_main_app_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_main_app_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_rounded_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\rounded_button_background.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/drawable_modern_button_primary.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/drawable/modern_button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_hotel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_hotel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_dialog_location_comments.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\dialog_location_comments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_attraction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_attraction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_baseline_remove_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\baseline_remove_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_transport_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_transport_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_destination.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_destination.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\color_copy_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\color\\copy_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\color_copy_status_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\color\\copy_status_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_mercado_pago.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_mercado_pago.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_nav_header_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\nav_header_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_transport_type.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_transport_type.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_donate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_donate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_comment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_comment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_category_pill_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\category_pill_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\menu_top_app_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\menu\\top_app_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\menu_drawer_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\menu\\drawer_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_category_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\category_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_location_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\location_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_reply_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\reply_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_positive_highlight_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\positive_highlight_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_comment_actions_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\comment_actions_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_website.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_website.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_zoom_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_zoom_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_gradient_scrim.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\gradient_scrim.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_city.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_city.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\color_chip_background_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\color\\chip_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_sort.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_sort.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_chip_background_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\chip_background_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_image_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\image_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\xml_remote_config_defaults.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\xml\\remote_config_defaults.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_donations.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_donations.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_description.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_description.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_rounded_tag_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\rounded_tag_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_comment_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\comment_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_featured_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_featured_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_add_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_add_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_transport.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_transport.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/drawable_modern_gradient_header_dark.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/drawable/modern_gradient_header_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/drawable_modern_card_background.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/drawable/modern_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_stat_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_stat_ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_restaurant.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_restaurant.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_main_app.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_main_app.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_filter_spinner_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\filter_spinner_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_locations.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_locations.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_donations.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_donations.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_atm.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_atm.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_church.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_church.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_event_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_event_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_placeholder_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\placeholder_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_chip_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\chip_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\menu_comment_options_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\menu\\comment_options_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_photo_preview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_photo_preview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_map.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_map.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_white_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\white_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_my_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_my_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_itati_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\itati_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_baseline_location_on_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\baseline_location_on_24.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/layout_fragment_events.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/layout/fragment_events.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/drawable_modern_chip_background.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/drawable/modern_chip_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_map_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\map_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_restaurant.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_restaurant.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/drawable_modern_gradient_header.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/drawable/modern_gradient_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_like_count_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\like_count_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_category_button_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\category_button_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_whatsapp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_whatsapp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_pharmacy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_pharmacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\font_montserrat_medium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\font\\montserrat_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_rounded_light_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\rounded_light_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\color_bottom_nav_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\color\\bottom_nav_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_main_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_main_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_comments.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_comments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_img_bathroom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\img_bathroom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_schedule.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_schedule.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_comments.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_comments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_fragment_events.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\fragment_events.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_activity_basic_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\activity_basic_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\layout_item_reply.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\layout\\item_reply.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-merged_res-60:\\drawable_ic_attraction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.Rages.itatiexplore.app-main-62:\\drawable\\ic_attraction.xml"}, {"merged": "com.Rages.itatiexplore.app-merged_res-60:/layout_nav_header.xml.flat", "source": "com.Rages.itatiexplore.app-main-62:/layout/nav_header.xml"}]