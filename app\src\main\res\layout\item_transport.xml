<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/ripple">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Imagen principal del transporte -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="180dp">

            <!-- Imagen de fondo -->
            <ImageView
                android:id="@+id/transport_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_transport"
                android:contentDescription="Imagen del transporte" />

            <!-- Gradiente para mejorar legibilidad del texto -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_scrim" />

            <!-- Nombre del transporte sobre la imagen -->
            <TextView
                android:id="@+id/transport_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:padding="16dp"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:shadowColor="#80000000"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2"
                tools:text="Empresa de Transporte" />

        </FrameLayout>

        <!-- Contenido de texto -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Descripción del transporte -->
            <TextView
                android:id="@+id/transport_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/on_surface"
                android:textSize="14sp"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Servicio regular de ómnibus con salidas diarias desde la terminal de Corrientes hacia Itatí." />

            <!-- Información de contacto -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@android:drawable/ic_menu_call"
                    app:tint="@color/primary" />

                <TextView
                    android:id="@+id/transport_phone_preview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:fontFamily="@font/montserrat"
                    android:textColor="@color/on_surface_medium"
                    android:textSize="12sp"
                    tools:text="+54 3777 123456" />

            </LinearLayout>

            <!-- Botón Ver Ficha Completa -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details_preview"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:text="Ver Información"
                android:textColor="@color/primary"
                app:icon="@android:drawable/ic_menu_info_details"
                app:iconTint="@color/primary" />

        </LinearLayout>
        
        <!-- Sección de detalles expandible (oculta permanentemente) -->
        <LinearLayout
            android:id="@+id/expanded_details_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone"
            android:background="#F5F5F5">
            
            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                android:layout_marginBottom="16dp"/>
                
            <!-- Sección de horarios -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Horarios"
                android:textStyle="bold"
                android:textColor="#0066CC"
                android:textSize="16sp"
                android:layout_marginBottom="8dp"/>
                
            <TextView
                android:id="@+id/transport_schedule_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textColor="@color/on_surface"
                android:textSize="14sp"
                tools:text="Lunes a Viernes: 06:00 - 22:00\nSábados: 07:00 - 21:00" />
                
            <!-- Sección de contacto -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Contacto"
                android:textStyle="bold"
                android:textColor="#0066CC"
                android:textSize="16sp"
                android:layout_marginBottom="8dp"/>
                
            <!-- Teléfono detallado -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical">
                
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_call"
                    app:tint="@color/primary" />
                    
                <TextView
                    android:id="@+id/transport_phone_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/on_surface"
                    android:textSize="14sp"
                    tools:text="+54 3777 123456" />
                    
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_call_expanded"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:layout_marginStart="8dp"
                    android:text="Llamar"
                    android:textColor="@color/primary"
                    android:minWidth="0dp"
                    android:padding="4dp" />
            </LinearLayout>
            
            <!-- WhatsApp detallado -->
            <LinearLayout
                android:id="@+id/whatsapp_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical">
                
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_whatsapp" />
                    
                <TextView
                    android:id="@+id/transport_whatsapp_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/on_surface"
                    android:textSize="14sp"
                    tools:text="+54 9 3777 123456" />
                    
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_whatsapp_expanded"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:layout_marginStart="8dp"
                    android:text="WhatsApp"
                    android:textColor="#25D366"
                    android:minWidth="0dp"
                    android:padding="4dp" />
            </LinearLayout>
            
            <!-- Dirección detallada -->
            <LinearLayout
                android:id="@+id/address_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical"
                android:visibility="gone">
                
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_dialog_map"
                    app:tint="@color/primary" />
                    
                <TextView
                    android:id="@+id/transport_address_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/on_surface"
                    android:textSize="14sp"
                    tools:text="Terminal de Ómnibus, Corrientes" />
            </LinearLayout>
            
            <!-- Información adicional -->
            <TextView
                android:id="@+id/transport_additional_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textColor="@color/on_surface"
                android:textSize="14sp"
                tools:text="Información adicional del transporte" />
                
            <!-- Botón de ver detalles completos -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:text="Ver Ficha Completa"
                android:textColor="@color/primary"
                app:icon="@android:drawable/ic_menu_info_details"
                app:iconTint="@color/primary" />
        </LinearLayout>

        <!-- Elementos ocultos para mantener compatibilidad -->
        <TextView
            android:id="@+id/transport_details"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/action_buttons"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone">

            <Button
                android:id="@+id/btn_call"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btn_website"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone" />
        </LinearLayout>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>