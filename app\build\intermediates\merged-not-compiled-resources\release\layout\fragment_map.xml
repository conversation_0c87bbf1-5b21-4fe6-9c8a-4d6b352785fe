<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".fragments.MapFragment">

    <!-- Mapa OSMDroid ocupando toda la pantalla -->
    <org.osmdroid.views.MapView
        android:id="@+id/osmmap"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Panel de filtros flotante superior -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/filter_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:cardBackgroundColor="#22ffffff"
        app:cardCornerRadius="25dp"
        app:cardElevation="0dp"
        app:strokeWidth="1dp"
        app:strokeColor="#44ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:clipToPadding="false">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/category_chip_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:singleSelection="true"
                app:selectionRequired="true"
                app:chipSpacingHorizontal="12dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_todos"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Todos"
                    android:tag="todos"
                    android:checked="true"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_hoteles"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Hoteles"
                    android:tag="hotel"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_restaurantes"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Restaurantes"
                    android:tag="restaurante"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_iglesias"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Iglesias"
                    android:tag="iglesia"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_atracciones"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Atracciones"
                    android:tag="atraccion"
                    android:textColor="@color/white"
                    android:fontFamily="@font/montserrat"
                    app:chipBackgroundColor="#44ffffff"
                    app:chipStrokeWidth="0dp"
                    app:chipCornerRadius="20dp"
                    app:checkedIconVisible="false" />
            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>
    </com.google.android.material.card.MaterialCardView>

    <!-- Controles flotantes laterales -->
    <LinearLayout
        android:id="@+id/map_controls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="100dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Botón Mi Ubicación -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginBottom="12dp"
            app:cardBackgroundColor="#44ffffff"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="#66ffffff">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_my_location"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/baseline_my_location_24"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="24dp"
                app:iconTint="@color/primary"
                app:backgroundTint="@android:color/transparent"
                app:cornerRadius="25dp"
                app:strokeWidth="0dp" />
        </com.google.android.material.card.MaterialCardView>

        <!-- Botón Zoom In -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginBottom="8dp"
            app:cardBackgroundColor="#44ffffff"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="#66ffffff">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_zoom_in"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_zoom_in"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="24dp"
                app:iconTint="@color/white"
                app:backgroundTint="@android:color/transparent"
                app:cornerRadius="25dp"
                app:strokeWidth="0dp" />
        </com.google.android.material.card.MaterialCardView>

        <!-- Botón Zoom Out -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="50dp"
            android:layout_height="50dp"
            app:cardBackgroundColor="#44ffffff"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="#66ffffff">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_zoom_out"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_zoom_out"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="24dp"
                app:iconTint="@color/white"
                app:backgroundTint="@android:color/transparent"
                app:cornerRadius="25dp"
                app:strokeWidth="0dp" />
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- Indicador de carga con glass effect -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/map_loading_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:cardBackgroundColor="#44ffffff"
        app:cardCornerRadius="25dp"
        app:cardElevation="0dp"
        app:strokeWidth="1dp"
        app:strokeColor="#66ffffff"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="24dp">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                app:indicatorColor="@color/primary"
                app:indicatorSize="48dp"
                app:trackThickness="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="Cargando..."
                android:textColor="@color/white"
                android:textSize="14sp"
                android:fontFamily="@font/montserrat" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>