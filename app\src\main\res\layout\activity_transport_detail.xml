<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".activities.TransportDetailActivity">

    <!-- AppBar con imagen de fondo -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:fitsSystemWindows="true"
        android:theme="@style/ThemeOverlay.MaterialComponents.Dark">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="@color/primary"
            app:expandedTitleMarginBottom="64dp"
            app:expandedTitleMarginStart="16dp"
            app:expandedTitleTextAppearance="@style/TextAppearance.MaterialComponents.Headline4"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <!-- Imagen de fondo -->
            <ImageView
                android:id="@+id/transport_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="Imagen del transporte"
                android:fitsSystemWindows="true"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_transport"
                app:layout_collapseMode="parallax" />

            <!-- Gradiente para mejorar legibilidad del texto -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_scrim"
                android:fitsSystemWindows="true"
                app:layout_collapseMode="parallax" />

            <!-- Toolbar -->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:navigationIcon="@android:drawable/ic_menu_close_clear_cancel"
                app:popupTheme="@style/ThemeOverlay.MaterialComponents.Light" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Contenido principal -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Tarjeta de información principal -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Descripción -->
                    <TextView
                        android:id="@+id/transport_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat"
                        android:textColor="@color/on_surface"
                        android:textSize="16sp"
                        tools:text="Descripción detallada del servicio de transporte, incluyendo información general sobre la empresa y los servicios que ofrece." />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Tarjeta de horarios -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <!-- Título de la sección -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat"
                        android:text="Horarios"
                        android:textColor="#0066CC"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_gravity="center" />

                    <!-- Horarios -->
                    <TextView
                        android:id="@+id/transport_schedule"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="sans-serif"
                        android:textColor="@color/on_surface"
                        android:textSize="14sp"
                        android:gravity="center"
                        tools:text="Lunes a Viernes: 6:00 - 22:00\nSábados: 7:00 - 21:00\nDomingos y Feriados: 8:00 - 20:00" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Tarjeta de contacto -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Título de la sección -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat"
                        android:text="Contacto"
                        android:textColor="#0066CC"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_gravity="center" />

                    <!-- Contenedor para los 3 botones en fila -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:baselineAligned="false"
                        android:orientation="horizontal">

                        <!-- WhatsApp -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:contentDescription="Ícono de WhatsApp"
                                android:src="@drawable/ic_whatsapp" />

                            <TextView
                                android:id="@+id/transport_whatsapp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:fontFamily="sans-serif"
                                android:textColor="@color/on_surface"
                                android:textSize="13sp"
                                android:maxLines="1"
                                android:gravity="center"
                                android:ellipsize="end"
                                tools:text="+54 9 3777 123456" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_whatsapp"
                                style="@style/Widget.MaterialComponents.Button.TextButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="WhatsApp"
                                android:insetTop="0dp"
                                android:insetBottom="0dp"
                                android:textSize="12sp"
                                android:textColor="#0066CC" />
                        </LinearLayout>

                        <!-- Teléfono -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:contentDescription="Ícono de teléfono"
                                android:src="@drawable/ic_phone" />

                            <TextView
                                android:id="@+id/transport_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:fontFamily="sans-serif"
                                android:textColor="@color/on_surface"
                                android:textSize="13sp"
                                android:maxLines="1"
                                android:gravity="center"
                                android:ellipsize="end"
                                tools:text="+54 3777 123456" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_call"
                                style="@style/Widget.MaterialComponents.Button.TextButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Llamar"
                                android:insetTop="0dp"
                                android:insetBottom="0dp"
                                android:textSize="12sp"
                                android:textColor="#0066CC" />
                        </LinearLayout>

                        <!-- Galerías (ocultas) -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <!-- Mantenemos los elementos pero ocultamos toda la sección -->
                            <TextView
                                android:id="@+id/transport_website"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="gone" />
                                
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_website"
                                style="@style/Widget.MaterialComponents.Button.TextButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="gone" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Dirección centrada (sin icono) -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/transport_address"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif"
                            android:textColor="@color/on_surface"
                            android:textSize="14sp"
                            android:gravity="center"
                            tools:text="Av. Principal 123, Itatí, Corrientes" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Tarjeta de detalles adicionales -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <!-- Título de la sección -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif"
                        android:text="Información Adicional"
                        android:textColor="@color/primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_gravity="center" />

                    <!-- Detalles de la base de datos -->
                    <TextView
                        android:id="@+id/transport_additional_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="sans-serif"
                        android:textColor="@color/on_surface"
                        android:textSize="14sp"
                        android:gravity="center"
                        tools:text="Información de detalles de la base de datos" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Quitamos el botón flotante de compartir -->

</androidx.coordinatorlayout.widget.CoordinatorLayout>