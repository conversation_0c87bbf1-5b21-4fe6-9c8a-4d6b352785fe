<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".fragments.CommentsFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background_light">

        <!-- <PERSON><PERSON><PERSON><PERSON> con título y filtros -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/header_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="0dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <!-- Título con ícono y botón para comentar -->
                <LinearLayout
                    android:id="@+id/title_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/primary"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <!-- Título y contador -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="12dp"
                        android:paddingTop="16dp"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_comments"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/comments_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Opiniones"
                            android:textColor="@android:color/white"
                            android:textSize="18sp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textStyle="bold" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_open_comment_modal"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:backgroundTint="@color/white"
                            android:text="Comentar"
                            android:textSize="12sp"
                            android:minWidth="90dp"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="6dp"
                            android:textColor="@color/primary"
                            app:icon="@drawable/ic_add_comment"
                            app:iconSize="16dp"
                            app:iconGravity="textStart"
                            app:iconTint="@color/primary"
                            app:strokeColor="@color/white"
                            app:strokeWidth="1dp" />
                    </LinearLayout>

                    <!-- Filtros para comentarios -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginBottom="12dp"
                        android:orientation="vertical">
                        
                        <!-- Título de filtros -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Filtrar por:"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp"/>
                        
                        <!-- Chips de filtro horizontal -->
                        <HorizontalScrollView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:scrollbars="none">
                            
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">
                                
                                <!-- Chip Todos -->
                                <TextView
                                    android:id="@+id/chip_all"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background_selected"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Todos"
                                    android:textColor="@color/white"
                                    android:layout_marginEnd="8dp"/>
                                
                                <!-- Chip Basílica -->
                                <TextView
                                    android:id="@+id/chip_basilica"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Basílica"
                                    android:textColor="@color/primary"
                                    android:layout_marginEnd="8dp"/>
                                
                                <!-- Chip Plaza -->
                                <TextView
                                    android:id="@+id/chip_plaza"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Plaza"
                                    android:textColor="@color/primary"
                                    android:layout_marginEnd="8dp"/>
                                
                                <!-- Chip Mirador -->
                                <TextView
                                    android:id="@+id/chip_mirador"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Mirador"
                                    android:textColor="@color/primary"
                                    android:layout_marginEnd="8dp"/>
                                
                                <!-- Chip Restaurante -->
                                <TextView
                                    android:id="@+id/chip_restaurante"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Restaurante"
                                    android:textColor="@color/primary"
                                    android:layout_marginEnd="8dp"/>
                                
                                <!-- Chip Hotel -->
                                <TextView
                                    android:id="@+id/chip_hotel"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Hotel"
                                    android:textColor="@color/primary"
                                    android:layout_marginEnd="8dp"/>
                                
                                <!-- Botón Más filtros -->
                                <TextView
                                    android:id="@+id/btn_more_filters"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/chip_background"
                                    android:paddingHorizontal="16dp"
                                    android:paddingVertical="8dp"
                                    android:text="Más opciones"
                                    android:textColor="@color/primary"
                                    android:drawableEnd="@drawable/ic_sort"
                                    android:drawablePadding="4dp"
                                    android:drawableTint="@color/primary"/>
                            </LinearLayout>
                        </HorizontalScrollView>
                    </LinearLayout>
                </LinearLayout>

                <!-- Espacio para el contenido principal -->
                <Space
                    android:id="@+id/content_space"
                    android:layout_width="match_parent"
                    android:layout_height="16dp"
                    app:layout_constraintTop_toBottomOf="@id/title_container" />

                <!-- Espacio para separar el título de la lista de comentarios -->
                <Space
                    android:id="@+id/ratings_summary"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    app:layout_constraintTop_toBottomOf="@id/content_space" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Información del usuario actual -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/user_info_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_card"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:id="@+id/user_avatar_small"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/circle_background"
                    android:padding="4dp"
                    android:src="@drawable/ic_person"
                    app:tint="@color/white" />

                <TextView
                    android:id="@+id/user_info_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_weight="1"
                    android:textStyle="italic"
                    tools:text="Comentando como: Usuario" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_add_comment"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Comentar"
                    android:textColor="@color/primary"
                    app:icon="@drawable/ic_add"
                    app:iconTint="@color/primary" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Lista de comentarios -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/comments_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layout_constraintBottom_toTopOf="@+id/sign_in_card"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_card"
            tools:listitem="@layout/item_comment" />

        <!-- Tarjeta para iniciar sesión -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/sign_in_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/sign_in_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Para dejar un comentario, inicia sesión:"
                    android:textAlignment="center"
                    android:textSize="16sp" />

                <com.google.android.gms.common.SignInButton
                    android:id="@+id/sign_in_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Modal para agregar comentario (inicialmente oculto) -->
        <FrameLayout
            android:id="@+id/comment_modal_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#80000000"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone"
            android:elevation="10dp"
            android:translationZ="10dp"
            tools:visibility="visible">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/comment_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:id="@+id/comment_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Cabecera con título y botón de cerrar -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/primary"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp"
                        android:paddingTop="14dp"
                        android:paddingBottom="14dp">

                        <ImageView
                            android:id="@+id/comment_icon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_comments"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/comment_title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="16dp"
                            android:text="Comparte tu opinión"
                            android:textColor="@color/white"
                            android:textSize="15sp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textStyle="bold"
                            app:layout_constraintEnd_toStartOf="@+id/btn_close_comment"
                            app:layout_constraintStart_toEndOf="@+id/comment_icon"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="@+id/comment_icon" />

                        <ImageButton
                            android:id="@+id/btn_close_comment"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:contentDescription="Cerrar"
                            android:padding="2dp"
                            android:src="@android:drawable/ic_menu_close_clear_cancel"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="@+id/comment_icon"
                            app:tint="@color/white" />

                        <!-- Información del usuario -->
                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/user_avatar_modal"
                            android:layout_width="42dp"
                            android:layout_height="42dp"
                            android:layout_marginTop="16dp"
                            android:src="@drawable/ic_person"
                            app:civ_border_width="2dp"
                            app:civ_border_color="@color/white"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/comment_icon" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:orientation="vertical"
                            app:layout_constraintBottom_toBottomOf="@+id/user_avatar_modal"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/user_avatar_modal"
                            app:layout_constraintTop_toTopOf="@+id/user_avatar_modal">

                            <TextView
                                android:id="@+id/user_name_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                android:textSize="14sp"
                                android:maxLines="1"
                                android:ellipsize="end"
                                android:textStyle="bold"
                                tools:text="Nombre de Usuario" />

                            <TextView
                                android:id="@+id/user_info_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                android:textSize="11sp"
                                android:maxLines="1"
                                android:ellipsize="end"
                                android:alpha="0.8"
                                tools:text="<EMAIL>" />
                        </LinearLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!-- Contenido del formulario -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Título de selección de ubicación -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="¿Dónde estuviste? *"
                            android:textSize="15sp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textColor="@color/gray_700"
                            android:layout_marginBottom="8dp" />
                            
                        <!-- Selector de ubicación -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/comment_location_selector"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp"
                            app:strokeColor="@color/primary"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:padding="12dp"
                                android:background="?attr/selectableItemBackground"
                                android:clickable="true"
                                android:focusable="true">

                                <ImageView
                                    android:id="@+id/location_category_icon"
                                    android:layout_width="28dp"
                                    android:layout_height="28dp"
                                    android:layout_marginEnd="12dp"
                                    android:src="@drawable/ic_location"
                                    app:tint="@color/primary" />

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical">
                                    
                                    <Spinner
                                        android:id="@+id/spinner_location"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:minHeight="48dp" />
                                        
                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:text="Selecciona un lugar cercano"
                                        android:textColor="@color/gray_500"
                                        android:textSize="12sp" />
                                </LinearLayout>

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/gps_button"
                                    style="@style/Widget.MaterialComponents.Button.OutlinedButton.Icon"
                                    android:layout_width="48dp"
                                    android:layout_height="48dp"
                                    android:insetLeft="0dp"
                                    android:insetTop="0dp"
                                    android:insetRight="0dp"
                                    android:insetBottom="0dp"
                                    android:padding="12dp"
                                    app:iconGravity="textStart"
                                    app:iconPadding="0dp"
                                    app:iconSize="24dp"
                                    app:icon="@drawable/ic_my_location"
                                    app:cornerRadius="24dp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <!-- Calificación con estrellas -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="¿Cómo calificarías tu experiencia? *"
                            android:textSize="15sp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textColor="@color/gray_700" />

                        <RatingBar
                            android:id="@+id/rating_bar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="16dp"
                            android:numStars="5"
                            android:stepSize="0.5" />

                        <!-- Campo de comentario -->
                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/comment_edit_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="Escribe tu comentario"
                                android:inputType="textMultiLine"
                                android:minLines="3" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Botón de enviar comentario -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/submit_button"
                            style="@style/Widget.MaterialComponents.Button"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:text="Publicar"
                            android:textSize="14sp"
                            android:letterSpacing="0"
                            android:textAllCaps="false"
                            android:textColor="@android:color/white"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:paddingStart="12dp"
                            android:paddingEnd="12dp"
                            app:icon="@drawable/ic_send"
                            app:iconSize="14dp" 
                            app:iconPadding="4dp"
                            app:iconGravity="textStart"
                            android:layout_marginBottom="16dp" />

                        <!-- Opción para agregar fotos -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:layout_marginBottom="16dp">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_add_photo"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Agregar fotos"
                                app:icon="@android:drawable/ic_menu_camera" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:text="(opcional)"
                                android:textColor="@color/gray_500"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <!-- Contenedor para previsualizar fotos -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/photos_recycler_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:orientation="horizontal"
                            android:visibility="gone"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:listitem="@layout/item_photo_preview"
                            tools:visibility="visible" />

                        <!-- Nota de privacidad -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="* Campos obligatorios. Tu opinión será visible para todos los usuarios."
                            android:textColor="@color/gray_500"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </FrameLayout>

        <!-- Indicador de carga -->
        <ProgressBar
            android:id="@+id/loading_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>