package com.Rages.itatiexplore;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;
import android.widget.Toast;

import java.lang.reflect.Field;
import java.util.Locale;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.Rages.itatiexplore.fragments.EventsFragment;
import com.Rages.itatiexplore.fragments.MapFragment;
import com.Rages.itatiexplore.fragments.TransportFragment;
import com.Rages.itatiexplore.fragments.CommentsFragment;
import com.Rages.itatiexplore.fragments.EmptyFragment;
import com.Rages.itatiexplore.fragments.DonationsFragment;
import com.Rages.itatiexplore.utils.ImageCacheManager;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.navigation.NavigationView;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.FirebaseFirestore;
import com.bumptech.glide.Glide;
import com.Rages.itatiexplore.dialogs.LocationCommentsDialog;
import com.Rages.itatiexplore.utils.ImageUtils;
import org.osmdroid.util.GeoPoint;

/**
 * Actividad principal de la aplicación con navegación entre fragmentos
 */
public class MainAppActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private static final String TAG = "MainAppActivity";

    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private FloatingActionButton fabMenu;
    private MaterialCardView markerInfoContainer;
    private TextView markerTitle, markerCategory, markerDescription;
    private TextView markerRatingText;
    private RatingBar markerRating;
    private ImageView markerImage;
    private ImageButton btnCloseMarkerInfo;
    private Button btnShare;
    private String currentMarkerName;
    private double currentLatitude = 0;
    private double currentLongitude = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Configurar el layout
            setContentView(R.layout.activity_main_app);

            // Inicializar componentes
            // drawerLayout = findViewById(R.id.drawer_layout); // Ya no existe en el nuevo diseño
            // navigationView = findViewById(R.id.navigation_view); // Ya no existe en el nuevo diseño
            fabMenu = findViewById(R.id.fab_menu);
            markerInfoContainer = findViewById(R.id.marker_info_container);
            markerTitle = findViewById(R.id.marker_title);
            markerCategory = findViewById(R.id.marker_category);
            markerDescription = findViewById(R.id.marker_description);
            // markerImage = findViewById(R.id.marker_image); // Ya no existe en el nuevo diseño
            // markerRating = findViewById(R.id.marker_rating); // Ya no existe en el nuevo diseño
            // markerRatingText = findViewById(R.id.marker_rating_text); // Ya no existe en el nuevo diseño
            btnCloseMarkerInfo = findViewById(R.id.btn_close_marker_info);
            btnShare = findViewById(R.id.btn_share);

            // Configurar la navegación lateral (ya no existe en el nuevo diseño)
            // navigationView.setNavigationItemSelectedListener(this);

            // Configurar el botón de menú con animación (ahora es diferente)
            fabMenu.setOnClickListener(v -> {
                // Acción del FAB central - puede ser para agregar algo o mostrar opciones
                // TODO: Implementar nueva funcionalidad del FAB
            });

            // Añadir listener para el drawer (ya no existe en el nuevo diseño)
            // drawerLayout.addDrawerListener(new DrawerLayout.DrawerListener() {
            //     @Override
            //     public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {
            //         // Rotar el FAB según el desplazamiento del drawer
            //         fabMenu.setRotation(slideOffset * 90);
            //     }

            //     @Override
            //     public void onDrawerOpened(@NonNull View drawerView) {}

            //     @Override
            //     public void onDrawerClosed(@NonNull View drawerView) {}

            //     @Override
            //     public void onDrawerStateChanged(int newState) {}
            // });

            // Establecer el fragmento inicial (Mapa)
            if (savedInstanceState == null) {
                loadFragment(new MapFragment(), R.id.navigation_map);
                setTitle(R.string.map);
            }

            // Configurar el botón "Cómo llegar"
            Button btnViewDetails = findViewById(R.id.btn_view_details);
            btnViewDetails.setOnClickListener(v -> {
                if (currentLatitude != 0 && currentLongitude != 0) {
                    // Obtener la instancia actual del fragmento de mapa
                    MapFragment mapFragment = (MapFragment) getSupportFragmentManager()
                            .findFragmentByTag(String.valueOf(R.id.navigation_map));

                    // Si no estamos en el fragmento de mapa, cargarlo primero
                    if (mapFragment == null) {
                        mapFragment = new MapFragment();
                        loadFragment(mapFragment, R.id.navigation_map);
                        navigationView.setCheckedItem(R.id.navigation_map);
                        setTitle(R.string.map);
                    }

                    // Centrar el mapa en la ubicación del marcador
                    final MapFragment finalMapFragment = mapFragment;

                    // Pequeño retraso para asegurar que el fragmento esté completamente cargado
                    new Handler().postDelayed(() -> {
                        // Crear el punto de destino
                        if (finalMapFragment != null) {
                            GeoPoint destination = new GeoPoint(currentLatitude, currentLongitude);

                            // Mostrar la ruta entre la ubicación del usuario y el destino
                            finalMapFragment.showRouteToDestination(destination);

                            // Mostrar mensaje
                            Toast.makeText(this, "Calculando ruta hacia " + currentMarkerName,
                                    Toast.LENGTH_SHORT).show();
                        }
                    }, 300);

                    // Ocultar la tarjeta de información del marcador
                    hideMarkerInfo();
                }
            });

            // Configurar el botón "Ver comentarios" (antes "Compartir")
            btnShare.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (currentMarkerName != null && !currentMarkerName.isEmpty()) {
                        // Crear y mostrar el diálogo de comentarios
                        LocationCommentsDialog dialog = new LocationCommentsDialog(
                                MainAppActivity.this,
                                currentMarkerName,
                                currentLatitude,
                                currentLongitude);
                        dialog.show();
                    }
                }
            });

            // Configurar el botón para cerrar la información del marcador
            btnCloseMarkerInfo.setOnClickListener(v -> {
                hideMarkerInfo();
            });

            // Inicializar componentes y configuraciones
            checkAppUpdates();
            setupNavigation();
            setupAccountButton();

        } catch (Exception e) {
            // Capturar cualquier error y registrarlo
            Log.e(TAG, "Error al iniciar MainAppActivity", e);
        }
    }

    /**
     * Muestra la información de un marcador
     */
    public void showMarkerInfo(String title, String category, String description,
                              String imageUrl, double latitude, double longitude, float rating) {
        try {
            // Guardar coordenadas para el botón "Cómo llegar"
            this.currentLatitude = latitude;
            this.currentLongitude = longitude;

            // Guardar el nombre del marcador actual
            this.currentMarkerName = title;

            // Verificar si la tarjeta ya está visible
            boolean isCardAlreadyVisible = markerInfoContainer.getVisibility() == View.VISIBLE;

            // Configurar los datos
            markerCategory.setText(category);
            markerTitle.setText(title);
            markerDescription.setText(description);

            // Mostrar el rating
            if (markerRating != null) {
                markerRating.setRating(rating);
            }

            // Mostrar el texto del rating
            if (markerRatingText != null) {
                markerRatingText.setText(String.format("%.1f/5", rating));
            }

            // Cargar imagen con Glide
            if (imageUrl != null && !imageUrl.isEmpty()) {
                // Procesar la URL para obtener enlace directo especialmente para Google Drive
                String directUrl = ImageUtils.getDirectImageUrl(imageUrl);

                // Usar ImageCacheManager para gestión de caché consistente
                ImageCacheManager.cargarImagenConCacheLocal(
                    this,
                    directUrl,
                    markerImage,
                    R.drawable.placeholder_image
                );
            } else {
                // Si no hay URL, usar una imagen predeterminada
                markerImage.setImageResource(R.drawable.placeholder_image);
            }

            // Mostrar la tarjeta si no está visible
            if (!isCardAlreadyVisible) {
                showMarkerInfoCard();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar información del marcador", e);
        }
    }

    /**
     * Versión sobrecargada para compatibilidad con el código existente
     */
    public void showMarkerInfo(String title, String category, String description,
                              String imageUrl, double latitude, double longitude) {
        // Esta versión pasa 0 como rating por defecto
        showMarkerInfo(title, category, description, imageUrl, latitude, longitude, 0f);
    }

    /**
     * Versión sobrecargada para compatibilidad con el código existente
     */
    public void showMarkerInfo(String title, String category, String description, String imageUrl) {
        // Esta versión usa 0,0 como coordenadas y 0 como rating por defecto
        showMarkerInfo(title, category, description, imageUrl, 0, 0, 0f);
    }

    /**
     * Oculta la información del marcador
     */
    public void hideMarkerInfo() {
        try {
            if (markerInfoContainer != null) {
                markerInfoContainer.animate()
                    .translationY(300f)
                    .alpha(0f)
                    .setDuration(200)
                    .withEndAction(() -> markerInfoContainer.setVisibility(View.GONE))
                    .start();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al ocultar información del marcador", e);
        }
    }

    /**
     * Muestra la tarjeta de información con animación
     */
    private void showMarkerInfoCard() {
        try {
            // Mostrar el contenedor con animación
            markerInfoContainer.setVisibility(View.VISIBLE);

            // Preparar la animación
            markerInfoContainer.setTranslationY(300f);
            markerInfoContainer.setAlpha(0f);
            markerInfoContainer.setScaleX(0.9f);
            markerInfoContainer.setScaleY(0.9f);

            // Ejecutar la animación
            markerInfoContainer.animate()
                    .translationY(0f)
                    .alpha(1f)
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(350)
                    .setInterpolator(new DecelerateInterpolator(1.2f))
                    .start();
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar la tarjeta de información", e);
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Si el ítem ya está seleccionado, no hacer nada
        if (item.isChecked()) {
            drawerLayout.closeDrawer(GravityCompat.START);
            return true;
        }

        int itemId = item.getItemId();

        try {
            Fragment fragment = null;

            // Crear una nueva instancia del fragmento según el ítem seleccionado
            if (itemId == R.id.navigation_map) {
                fragment = new MapFragment();
                setTitle(R.string.map);
            } else if (itemId == R.id.navigation_events) {
                fragment = new EventsFragment();
                setTitle(R.string.events);
            } else if (itemId == R.id.navigation_transport) {
                fragment = new TransportFragment();
                setTitle(R.string.transport);
            } else if (itemId == R.id.navigation_comments) {
                fragment = new CommentsFragment();
                setTitle(R.string.comments);
            } else if (itemId == R.id.navigation_donations) {
                fragment = new DonationsFragment();
                setTitle(R.string.donations);
            } else if (itemId == R.id.navigation_settings) {
                // Just return without showing a toast message
                drawerLayout.closeDrawer(GravityCompat.START);
                return true;
            } else if (itemId == R.id.navigation_about) {
                // Just return without showing a toast message
                drawerLayout.closeDrawer(GravityCompat.START);
                return true;
            }

            // Cargar el fragmento
            if (fragment != null) {
                loadFragment(fragment, itemId);

                // Ocultar el panel de información del marcador al cambiar de fragmento
                if (markerInfoContainer != null && markerInfoContainer.getVisibility() == View.VISIBLE) {
                    hideMarkerInfo();
                }

                // Cerrar el drawer
                drawerLayout.closeDrawer(GravityCompat.START);
                return true;
            }

        } catch (Exception e) {
            Log.e(TAG, "Error al cambiar fragmento", e);
        }

        return false;
    }

    /**
     * Carga un fragmento en el contenedor
     */
    private void loadFragment(Fragment fragment, int itemId) {
        try {
            String fragmentTag = String.valueOf(itemId);

            // Usar beginTransaction().replace() pero con setReorderingAllowed(true) para optimizar
            getSupportFragmentManager().beginTransaction()
                    .setReorderingAllowed(true)
                    .replace(R.id.fragment_container, fragment, fragmentTag)
                    .commit();
        } catch (Exception e) {
            Log.e(TAG, "Error al cargar fragmento", e);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Restaurar la UI a su estado normal
        restoreUI();
    }

    /**
     * Restaura la interfaz de usuario a su estado normal
     */
    private void restoreUI() {
        // Restaurar la visibilidad normal de la interfaz
        if (getWindow() != null) {
            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_VISIBLE
            );
        }
    }

    @Override
    public void onBackPressed() {
        // Si el drawer está abierto, cerrarlo
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        // Si el panel de información del marcador está visible, ocultarlo
        else if (markerInfoContainer != null && markerInfoContainer.getVisibility() == View.VISIBLE) {
            hideMarkerInfo();
        }
        // Si estamos en el fragmento del mapa, salir de la aplicación
        else if (getSupportFragmentManager().findFragmentByTag(String.valueOf(R.id.navigation_map)) != null &&
                 getSupportFragmentManager().findFragmentByTag(String.valueOf(R.id.navigation_map)).isVisible()) {
            super.onBackPressed();
        }
        // Si estamos en otro fragmento, volver al mapa
        else {
            loadFragment(new MapFragment(), 0); // Ya no usamos IDs de navegación
            // navigationView.setCheckedItem(R.id.navigation_map); // Ya no existe
            setTitle(R.string.map);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Limpiar referencias y liberar recursos (ya no existen en el nuevo diseño)
        // if (navigationView != null) {
        //     navigationView.setNavigationItemSelectedListener(null);
        // }

        // drawerLayout = null;
        // navigationView = null;
        fabMenu = null;
        markerInfoContainer = null;
        markerTitle = null;
        markerCategory = null;
        markerDescription = null;
        markerImage = null;
        btnCloseMarkerInfo = null;
        btnShare = null;
        currentMarkerName = null;

        // Sistema de recolección de basura
        System.gc();
    }

    /**
     * Verifica si hay actualizaciones disponibles para la aplicación
     */
    private void checkAppUpdates() {
        // Método vacío para evitar errores de compilación
        // La funcionalidad se implementará en el futuro
    }

    /**
     * Configura la navegación
     */
    private void setupNavigation() {
        // Método vacío para evitar errores de compilación
        // La funcionalidad se implementará en el futuro
    }

    /**
     * Configura el botón de cuenta
     */
    private void setupAccountButton() {
        // Método vacío para evitar errores de compilación
        // La funcionalidad se implementará en el futuro
    }
}