package com.Rages.itatiexplore.fragments;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.PorterDuff;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.Toast;
import android.widget.TextView;
import android.widget.ImageView;
import android.widget.ImageButton;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.Rages.itatiexplore.MainAppActivity;
import com.Rages.itatiexplore.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.bumptech.glide.Glide;

import org.osmdroid.api.IMapController;
import org.osmdroid.config.Configuration;
import org.osmdroid.tileprovider.tilesource.TileSourceFactory;
import org.osmdroid.util.GeoPoint;
import org.osmdroid.views.MapView;
import org.osmdroid.views.overlay.Marker;
import org.osmdroid.views.overlay.Overlay;
import org.osmdroid.views.overlay.Polyline;
import org.osmdroid.views.overlay.mylocation.GpsMyLocationProvider;
import org.osmdroid.views.overlay.mylocation.MyLocationNewOverlay;

import java.text.Normalizer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.Rages.itatiexplore.utils.MapboxDirectionsClient;
import android.widget.Toast;

public class MapFragment extends Fragment implements LocationListener {
    private static final String TAG = "MapFragment";

    // Coordenadas de Itatí (centro del mapa)
    private static final double ITATI_LATITUDE = -27.268523905034662;
    private static final double ITATI_LONGITUDE = -58.243342041127114;
    private static final int DEFAULT_ZOOM = 14;

    // Variables para almacenar la ubicación actual del usuario
    private double currentLat = ITATI_LATITUDE;
    private double currentLng = ITATI_LONGITUDE;
    private Location lastKnownLocation = null;

    // Variables para el mapa
    private MapView mapView;
    private IMapController mapController;
    private MyLocationNewOverlay myLocationOverlay;
    private ProgressBar loadingIndicator;

    // Reemplazar el contenedor de categorías por el ChipGroup
    private com.google.android.material.chip.ChipGroup categoryChipGroup;

    // Elementos de la tarjeta de información
    private androidx.cardview.widget.CardView placeInfoCard;
    private android.widget.TextView placeCategory;
    private android.widget.ImageView placeImage;
    private android.widget.TextView placeTitle;
    private android.widget.TextView placeDescription;
    private android.widget.Button btnShare;
    private android.widget.Button btnDetails;
    private android.widget.ImageButton btnCloseCard;

    private String currentCategory = "todos";

    // Lista de puntos de interés y marcadores
    private List<PointOfInterest> pointsOfInterest;

    // Nuevo sistema de asociación: un mapa que asocia cada marcador con su POI directamente
    private Map<Marker, PointOfInterest> markerToPOI = new HashMap<>();

    // Marcador especial para Itatí (centro del mapa)
    private CustomMarker itatiMarker;

    // Lista para ayudar a limpiar todos los marcadores
    private List<Marker> allMarkers = new ArrayList<>();

    // Firebase
    private FirebaseFirestore db;

    // Componentes para la ubicación en tiempo real
    private LocationManager locationManager;
    private LocationListener locationListener;
    private static final int REQUEST_LOCATION_PERMISSION = 1;

    // Para solicitar permisos
    private ActivityResultLauncher<String[]> locationPermissionRequest;

    private boolean isFragmentActive = true;

    // Nuevo marcador personalizado para la ubicación del usuario
    private Marker userLocationMarker;
    private boolean isFollowingLocation = false; // Inicializado en false para que no siga automáticamente

    // Lista para almacenar categorías de Firebase
    private List<Map<String, Object>> categorias = new ArrayList<>();

    private static final String MAPBOX_ACCESS_TOKEN = "sk.eyJ1IjoicmFnZXMiLCJhIjoiY21hcnAzZG85MGU0YzJycHlkM3M5ZXNiNCJ9.vKqFM_HcsM081zerSSCKtg";
    private MapboxDirectionsClient directionsClient;
    private Polyline currentRoute;
    private GeoPoint destinationPoint;

    public MapFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Inicializar Firebase
            db = FirebaseFirestore.getInstance();

            // Inicializar lista de puntos de interés
            pointsOfInterest = new ArrayList<>();

            // Configurar osmdroid
            Context ctx = requireContext();
            Configuration.getInstance().load(ctx, PreferenceManager.getDefaultSharedPreferences(ctx));

            // Inicializar el cliente de direcciones de Mapbox
            directionsClient = new MapboxDirectionsClient(MAPBOX_ACCESS_TOKEN);

            // No intentamos deshabilitar InfoWindows globalmente aquí
            // En su lugar, usamos setInfoWindow(null) para cada marcador individualmente

            // Configurar el launcher para solicitar permisos de ubicación
            locationPermissionRequest = registerForActivityResult(
                new ActivityResultContracts.RequestMultiplePermissions(), result -> {
                    try {
                        Boolean fineLocationGranted = result.getOrDefault(
                            Manifest.permission.ACCESS_FINE_LOCATION, false);
                        Boolean coarseLocationGranted = result.getOrDefault(
                            Manifest.permission.ACCESS_COARSE_LOCATION, false);

                        if (fineLocationGranted != null && fineLocationGranted) {
                            // Permiso preciso concedido
                            startLocationUpdates();
                        } else if (coarseLocationGranted != null && coarseLocationGranted) {
                            // Solo permiso aproximado concedido
                            startLocationUpdates();
                        } else {
                            // Ningún permiso concedido
                            if (getContext() != null) {
                                Toast.makeText(getContext(), "Se requieren permisos de ubicación para mostrar tu posición en el mapa", Toast.LENGTH_LONG).show();
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error al procesar permisos de ubicación", e);
                    }
                }
            );
        } catch (Exception e) {
            Log.e(TAG, "Error en onCreate del MapFragment", e);
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_map, container, false);

        try {
            // Inicializar referencias de vistas
            mapView = rootView.findViewById(R.id.osmmap);
            // loadingIndicator = rootView.findViewById(R.id.map_loading_indicator); // Comentado: no existe este recurso
            // placeInfoCard = rootView.findViewById(R.id.place_info_card); // Ya no existe
            // placeCategory = rootView.findViewById(R.id.place_category); // Ya no existe
            // placeImage = rootView.findViewById(R.id.place_image); // Ya no existe
            // placeTitle = rootView.findViewById(R.id.place_title); // Ya no existe
            // placeDescription = rootView.findViewById(R.id.place_description); // Ya no existe
            // btnShare = rootView.findViewById(R.id.btn_share); // Ya no existe
            // btnDetails = rootView.findViewById(R.id.btn_details); // Ya no existe
            // btnCloseCard = rootView.findViewById(R.id.btn_close_card); // Ya no existe

            // La barra de búsqueda ha sido eliminada del diseño

            // Configuración del mapa
            if (mapView != null) {
                Configuration.getInstance().load(requireContext(),
                        PreferenceManager.getDefaultSharedPreferences(requireContext()));
                setupMap();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error en onCreateView", e);
        }

        return rootView;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        try {
            // Configurar el mapa después de que la actividad esté creada
            if (mapView != null) {
                // Configuración básica
                Configuration.getInstance().load(requireContext(), PreferenceManager.getDefaultSharedPreferences(requireContext()));

                // Desactivar completamente los popups
                SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

                // Configurar para que no muestre infowindows
                SafeInfoWindowHelper.safeCloseInfoWindows(mapView);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error en onActivityCreated", e);
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        try {
            // Inicializar Firestore
            db = FirebaseFirestore.getInstance();

            // Inicializar vistas
            mapView = view.findViewById(R.id.osmmap);
            // loadingIndicator = view.findViewById(R.id.loading_indicator); // Comentado: no existe este recurso
            // placeInfoCard = view.findViewById(R.id.place_info_card); // Ya no existe
            // placeCategory = view.findViewById(R.id.place_category); // Ya no existe
            // placeImage = view.findViewById(R.id.place_image); // Ya no existe
            // placeTitle = view.findViewById(R.id.place_title); // Ya no existe
            // placeDescription = view.findViewById(R.id.place_description); // Ya no existe
            // btnShare = view.findViewById(R.id.btn_share); // Ya no existe
            // btnDetails = view.findViewById(R.id.btn_details); // Ya no existe
            // btnCloseCard = view.findViewById(R.id.btn_close_card); // Ya no existe

            // Configurar mapa
            setupMap();

            // Asegurarse de que el marcador de ubicación esté visible
            if (userLocationMarker == null) {
                createUserLocationMarker();
            }

            // Configurar controles del mapa
            setupMapControls(view);

            // Inicializar RecyclerView y adaptador
            setupCategoryChips();

            // Cargar puntos de interés desde Firebase
            loadMarkersFromFirebase();

            // Cargar categorías desde Firebase (nuevo)
            loadCategoriasFromFirebase();

            // La barra de búsqueda ha sido eliminada del diseño

            // Botón para cerrar la tarjeta de información
            if (btnCloseCard != null) {
                btnCloseCard.setOnClickListener(v -> hidePlaceInfoCard());
            }

            // Botón para compartir
            if (btnShare != null) {
                btnShare.setOnClickListener(v -> {
                    // Obtener el POI actual
                    int poiIndex = (int) placeInfoCard.getTag();
                    if (poiIndex >= 0 && poiIndex < pointsOfInterest.size()) {
                        sharePlaceInfo(pointsOfInterest.get(poiIndex));
                    }
                });
            }

            // Configurar el botón de detalles
            setupDetailsButton();

            // Iniciar actualizaciones de ubicación
            if (ContextCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                startLocationUpdates();

                // Solicitar una actualización única para mostrar la ubicación actual
                requestSingleLocationUpdate();
            } else {
                requestLocationPermission();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error en onViewCreated", e);
        }
    }

    private void setupMap() {
        try {
            if (mapView == null) return;

            // Configurar el mapa
            mapView.setTileSource(TileSourceFactory.MAPNIK);
            mapView.setMultiTouchControls(true); // Permitir zoom con gestos

            // Desactivar los botones de zoom integrados de OSMDroid
            mapView.setBuiltInZoomControls(false);
            mapView.setMinZoomLevel(4.0);
            mapView.setMaxZoomLevel(21.0);
            mapView.setTilesScaledToDpi(true);

            // Deshabilitar globalmente los infowindows (popups) en todo el mapa
            SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

            // Cerrar TODOS los InfoWindows existentes y deshabilitar globalmente
            SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

            // Controlador del mapa
            mapController = mapView.getController();

            // Si ya tenemos una ubicación conocida, centrar ahí
            if (lastKnownLocation != null) {
                GeoPoint myPoint = new GeoPoint(lastKnownLocation.getLatitude(),
                                              lastKnownLocation.getLongitude());
                mapController.setCenter(myPoint);
            } else {
                // Caso contrario, centrar en Itatí
                GeoPoint itatiPoint = new GeoPoint(ITATI_LATITUDE, ITATI_LONGITUDE);
                mapController.setCenter(itatiPoint);
            }

            // Establecer zoom inicial
            mapController.setZoom(DEFAULT_ZOOM);

            // Configuramos el proveedor de ubicación en segundo plano
            GpsMyLocationProvider provider = new GpsMyLocationProvider(requireContext());
            provider.setLocationUpdateMinTime(500); // Actualizar cada 500ms para actualización en tiempo real más fluida

            // Creamos el overlay de ubicación, pero NO lo agregamos al mapa
            // Solo lo usamos para recibir actualizaciones de ubicación
            myLocationOverlay = new MyLocationNewOverlay(provider, mapView);
            myLocationOverlay.enableMyLocation();

            // Inicializar la lista de marcadores
            allMarkers = new ArrayList<>();

            // Configurar el overlay para manejar clics en el mapa
            setupMapClickOverlay();

            // Aplicar el método que elimina todos los infowindows
            removeAllInfoWindowsFromMap();

            // Inicializamos nuestro marcador personalizado para la ubicación del usuario
            // y lo hacemos después de todo lo demás para que esté en la capa superior
            createUserLocationMarker();

            // Actualizar el mapa
            mapView.invalidate();

            // Solicitar una actualización de ubicación para mostrar el marcador en la posición correcta
            if (ContextCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                requestSingleLocationUpdate();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error al configurar el mapa", e);
        }
    }

    /**
     * Crea un marcador personalizado para mostrar la ubicación del usuario
     */
    private void createUserLocationMarker() {
        try {
            // Crear un marcador nuevo
            userLocationMarker = new Marker(mapView);
            userLocationMarker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_CENTER);

            // Configurar para que no muestre infowindow al hacer clic
            userLocationMarker.setInfoWindow(null);

            // Si ya tenemos una ubicación, colocar el marcador ahí
            if (lastKnownLocation != null) {
                GeoPoint userPoint = new GeoPoint(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude());
                userLocationMarker.setPosition(userPoint);
                userLocationMarker.setVisible(true); // Forzar visibilidad
            } else if (currentLat != 0 && currentLng != 0) {
                GeoPoint userPoint = new GeoPoint(currentLat, currentLng);
                userLocationMarker.setPosition(userPoint);
                userLocationMarker.setVisible(true); // Forzar visibilidad
            } else {
                // Usar Itatí como ubicación inicial temporal
                GeoPoint defaultPoint = new GeoPoint(ITATI_LATITUDE, ITATI_LONGITUDE);
                userLocationMarker.setPosition(defaultPoint);
                userLocationMarker.setVisible(true); // Forzar visibilidad aunque sea en posición temporal
            }

            // Crear un punto azul estilo Google Maps
            GradientDrawable userDrawable = new GradientDrawable();
            userDrawable.setShape(GradientDrawable.OVAL);

            // Color azul para el punto principal
            int googleBlue = Color.parseColor("#4285F4"); // Azul de Google Maps
            userDrawable.setColor(googleBlue);

            // Agregar un borde blanco sutil
            userDrawable.setStroke(3, Color.WHITE);

            // Tamaño del punto (un poco más grande para mejor visibilidad)
            int markerSize = 36;

            // Convertir a bitmap
            Bitmap userBitmap = Bitmap.createBitmap(markerSize, markerSize, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(userBitmap);
            userDrawable.setBounds(0, 0, markerSize, markerSize);
            userDrawable.draw(canvas);

            // Crear un halo azul translúcido alrededor del punto
            GradientDrawable haloDrawable = new GradientDrawable();
            haloDrawable.setShape(GradientDrawable.OVAL);
            haloDrawable.setColor(Color.argb(50, 66, 133, 244)); // Azul translúcido

            // Tamaño del halo (más grande que el punto central)
            int haloSize = markerSize * 3;
            Bitmap haloBitmap = Bitmap.createBitmap(haloSize, haloSize, Bitmap.Config.ARGB_8888);
            Canvas haloCanvas = new Canvas(haloBitmap);
            haloDrawable.setBounds(0, 0, haloSize, haloSize);
            haloDrawable.draw(haloCanvas);

            // Combinar el punto y el halo
            Bitmap combinedBitmap = Bitmap.createBitmap(haloSize, haloSize, Bitmap.Config.ARGB_8888);
            Canvas combinedCanvas = new Canvas(combinedBitmap);

            // Dibujar primero el halo
            combinedCanvas.drawBitmap(haloBitmap, 0, 0, null);

            // Dibujar el punto central en el medio del halo
            int offset = (haloSize - markerSize) / 2;
            combinedCanvas.drawBitmap(userBitmap, offset, offset, null);

            // Establecer el icono combinado
            BitmapDrawable userIcon = new BitmapDrawable(getResources(), combinedBitmap);
            userLocationMarker.setIcon(userIcon);

            // Eliminar el marcador si ya existe en el mapa para evitar duplicados
            mapView.getOverlays().remove(userLocationMarker);

            // Agregar el marcador al mapa (añadirlo al final para asegurar que esté por encima de otros elementos)
            mapView.getOverlays().add(userLocationMarker);

            // Forzar la actualización del mapa para que aparezca inmediatamente
            mapView.invalidate();

            Log.d(TAG, "Marcador de ubicación creado y forzado a ser visible");

        } catch (Exception e) {
            Log.e(TAG, "Error al crear marcador de ubicación", e);
        }
    }

    /**
     * Actualiza la posición del marcador de ubicación del usuario
     */
    private void updateUserLocationMarker(double latitude, double longitude) {
        try {
            // Si el marcador no existe, crearlo
            if (userLocationMarker == null) {
                createUserLocationMarker();
                return; // El método createUserLocationMarker ya configura todo lo necesario
            }

            // Crear nuevo punto con la ubicación actual
            GeoPoint userPoint = new GeoPoint(latitude, longitude);

            // Actualizar posición
            userLocationMarker.setPosition(userPoint);
            userLocationMarker.setVisible(true);

            // Asegurarse de que el marcador esté en la parte superior de los overlays
            mapView.getOverlays().remove(userLocationMarker);
            mapView.getOverlays().add(userLocationMarker);

            // Si estamos siguiendo la ubicación, centrar el mapa
            if (isFollowingLocation) {
                mapController.animateTo(userPoint);
            }

            // Forzar redibujado del mapa inmediatamente
            mapView.invalidate();

            // Registrar la actualización para depuración
            Log.d(TAG, "Marcador de ubicación actualizado a: " + latitude + ", " + longitude);

        } catch (Exception e) {
            Log.e(TAG, "Error al actualizar marcador de ubicación", e);
        }
    }

    /**
     * Convierte un drawable a bitmap
     */
    private Bitmap drawableToBitmap(Drawable drawable) {
        if (drawable instanceof BitmapDrawable) {
            return ((BitmapDrawable) drawable).getBitmap();
        }

        int width = drawable.getIntrinsicWidth();
        width = width > 0 ? width : 24;
        int height = drawable.getIntrinsicHeight();
        height = height > 0 ? height : 24;

        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);

        return bitmap;
    }

    private void addItatiMarker() {
        try {
            if (!isFragmentActive || mapView == null) return;

            // Crear un marcador para Itatí
            Marker itatiMarker = new Marker(mapView);

            // Configurar posición
            GeoPoint itatiPoint = new GeoPoint(ITATI_LATITUDE, ITATI_LONGITUDE);
            itatiMarker.setPosition(itatiPoint);
            itatiMarker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM);

            // Configurar icono
            Drawable icon = ContextCompat.getDrawable(requireContext(), R.drawable.baseline_location_on_24);
            if (icon != null) {
                icon.setColorFilter(Color.parseColor("#4CAF50"), PorterDuff.Mode.SRC_IN);
                itatiMarker.setIcon(icon);
            }

            // Configurar título (solo para debug en logs)
            itatiMarker.setTitle("Itatí");

            // Asignar snippet para identificación
            itatiMarker.setSnippet("itati_central");

            // Configurar el listener de clic en el marcador
            itatiMarker.setOnMarkerClickListener((marker, mapView) -> {
                // Cerrar cualquier InfoWindow abierta
                SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

                // Registrar en el log
                Log.d(TAG, "Clic en marcador de Itatí");

                // Obtener la actividad
                if (getActivity() instanceof MainAppActivity) {
                    MainAppActivity activity = (MainAppActivity) getActivity();

                    // Mostrar información del marcador de Itatí
                    activity.showMarkerInfo(
                        "Itatí, Corrientes",
                        "Ciudad",
                        "Ciudad de Itatí, una hermosa ciudad ubicada en la provincia de Corrientes, Argentina. Conocida por su basílica y su rica historia cultural.",
                        "https://firebasestorage.googleapis.com/v0/b/itatiexplore.appspot.com/o/places%2Fitati_central.jpg?alt=media",
                        ITATI_LATITUDE,
                        ITATI_LONGITUDE
                    );
                }

                // Centrar el mapa en este marcador con una animación suave
                mapController.animateTo(marker.getPosition());

                return true;
            });

            // Agregar al mapa
            mapView.getOverlays().add(itatiMarker);

        } catch (Exception e) {
            Log.e(TAG, "Error al agregar marcador de Itatí", e);
        }
    }

    private void setupMapControls(View view) {
        try {
            // Botón para ir a ubicación actual
            MaterialButton btnMyLocation = view.findViewById(R.id.btn_my_location);
            if (btnMyLocation != null) {
                btnMyLocation.setOnClickListener(v -> {
                    // Verificar si tenemos permisos
                    if (ContextCompat.checkSelfPermission(requireContext(),
                            Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                        requestLocationPermission();
                        return;
                    }

                    // Verificar si el GPS está activado
                    if (locationManager != null) {
                        boolean isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
                        boolean isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);

                        if (!isGpsEnabled && !isNetworkEnabled) {
                            showLocationSettings();
                            return;
                        }
                    }

                    // Ir a la ubicación actual
                    if (lastKnownLocation != null) {
                        // Crear punto con la ubicación actual
                        GeoPoint myLocation = new GeoPoint(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude());

                        // Centrar el mapa en la ubicación con animación
                        mapController.animateTo(myLocation);
                        mapController.setZoom(18); // Zoom más cercano para ver mejor la ubicación

                        // Asegurarse de que el marcador de ubicación esté visible
                        updateUserLocationMarker(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude());

                        // Activar seguimiento momentáneamente
                        isFollowingLocation = true;

                        // Programar la desactivación del seguimiento después de unos segundos
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            isFollowingLocation = false;
                        }, 5000); // Desactivar seguimiento después de 5 segundos
                    } else {
                        // Si no hay ubicación actual, solicitar una
                        requestSingleLocationUpdate();
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al configurar controles del mapa", e);
        }
    }

    /**
     * Carga los marcadores desde Firebase Firestore
     */
    private void loadMarkersFromFirebase() {
        try {
            // Verificar si el fragmento sigue activo
            if (!isFragmentActive || getContext() == null) return;

            // Mostrar indicador de carga
            // if (loadingIndicator != null) {
            //     loadingIndicator.setVisibility(View.VISIBLE);
            // }

            // Verificar si tenemos acceso a Firestore
            if (db == null) {
                Log.e(TAG, "Firestore no está inicializado");
                // if (loadingIndicator != null) {
                //     loadingIndicator.setVisibility(View.GONE);
                // }
                return;
            }

            Log.d(TAG, "Iniciando carga de marcadores desde Firebase...");

            // Obtener los marcadores de Firebase sin filtrar por active
            db.collection("marcadores")
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    // Crear nueva lista de POIs
                    List<PointOfInterest> newPOIs = new ArrayList<>();

                    for (DocumentSnapshot document : queryDocumentSnapshots) {
                        try {
                            Log.d(TAG, "Procesando documento: " + document.getId());

                            // Obtener datos del documento
                            Boolean active = document.getBoolean("active");
                            String categoriaId = document.getString("categoriaId");
                            String description = document.getString("description");
                            String imageUrl = document.getString("imageUrl");
                            Double latitude = document.getDouble("latitude");
                            Double longitude = document.getDouble("longitude");
                            String name = document.getString("name");
                            Double rating = document.getDouble("rating");

                            // Log de los datos recibidos
                            Log.d(TAG, String.format("Datos del marcador: name=%s, active=%b, categoriaId=%s, lat=%f, lng=%f",
                                name, active, categoriaId, latitude != null ? latitude : 0, longitude != null ? longitude : 0));

                            // Validar datos obligatorios
                            if (name != null && latitude != null && longitude != null && categoriaId != null) {
                                float ratingValue = rating != null ? rating.floatValue() : 0.0f;

                                // Crear punto de interés
                                PointOfInterest poi = new PointOfInterest(
                                    name,
                                    latitude,
                                    longitude,
                                    imageUrl != null ? imageUrl : "",
                                    description != null ? description : "",
                                    categoriaId,
                                    ratingValue
                                );

                                // Añadir a la lista
                                newPOIs.add(poi);
                                Log.d(TAG, "Marcador añadido: " + name + " con categoría: " + categoriaId);
                            } else {
                                Log.w(TAG, "Marcador incompleto: " + document.getId());
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error al procesar documento: " + document.getId(), e);
                        }
                    }

                    // Actualizar la lista principal
                    pointsOfInterest = newPOIs;

                    // Añadir marcadores al mapa
                    addMarkersToMap();

                    // Ocultar indicador de carga
                    // if (loadingIndicator != null) {
                    //     loadingIndicator.setVisibility(View.GONE);
                    // }

                    Log.d(TAG, "Carga de marcadores completada. Total: " + pointsOfInterest.size());

                    // Si no hay marcadores, mostrar mensaje
                    if (pointsOfInterest.isEmpty() && getContext() != null) {
                        Toast.makeText(getContext(),
                            "No se encontraron marcadores en la base de datos",
                            Toast.LENGTH_LONG).show();
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al cargar marcadores desde Firebase", e);
                    // if (loadingIndicator != null) {
                    //     loadingIndicator.setVisibility(View.GONE);
                    // }
                    if (getContext() != null) {
                        Toast.makeText(getContext(),
                            "Error al cargar los marcadores: " + e.getMessage(),
                            Toast.LENGTH_LONG).show();
                    }
                });

        } catch (Exception e) {
            Log.e(TAG, "Error general en loadMarkersFromFirebase", e);
            // if (loadingIndicator != null) {
            //     loadingIndicator.setVisibility(View.GONE);
            // }
        }
    }

    /**
     * Añade marcadores al mapa basado en los puntos de interés
     */
    private void addMarkersToMap() {
        try {
            if (!isFragmentActive || mapView == null) return;

            Log.d(TAG, "Empezando a agregar marcadores al mapa");

            // Limpiar marcadores existentes del mapa
            clearAllMarkers();

            // Eliminar cualquier popup nativo que haya quedado
            SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

            // El marcador central de Itatí ha sido eliminado
            // addItatiMarker(); // Línea eliminada para quitar el marcador fijo

            // Recorrer todos los puntos de interés
            int markerCount = pointsOfInterest.size();
            Log.d(TAG, "Añadiendo " + markerCount + " marcadores al mapa");

            for (int i = 0; i < markerCount; i++) {
                PointOfInterest poi = pointsOfInterest.get(i);
                addMarkerToMap(poi);
            }

            // Mostrar todos los marcadores por defecto
            filterMarkersByCategory(currentCategory);

            // Actualizar el mapa y asegurarse de que no haya popups
            mapView.invalidate();
            SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

            // Log para debug
            Log.d(TAG, "Marcadores añadidos al mapa: " + allMarkers.size());
        } catch (Exception e) {
            Log.e(TAG, "Error al agregar marcadores al mapa", e);
        }
    }

    /**
     * Limpia todos los marcadores del mapa
     */
    private void clearAllMarkers() {
        try {
            // Crear una copia de la lista para evitar problemas de concurrencia
            List<Marker> markersToRemove = new ArrayList<>(allMarkers);

            // Remover todos los marcadores del mapa excepto el de ubicación del usuario
            for (Marker marker : markersToRemove) {
                if (marker != userLocationMarker) {
                    mapView.getOverlays().remove(marker);
                }
            }

            // Limpiar las estructuras de datos
            allMarkers.clear();
            markerToPOI.clear();

            // No limpiar referencia al marcador de ubicación del usuario

            Log.d(TAG, "Todos los marcadores eliminados del mapa (excepto ubicación de usuario)");

            // Asegurar que el marcador de ubicación esté en la capa superior
            if (userLocationMarker != null) {
                mapView.getOverlays().remove(userLocationMarker);
                mapView.getOverlays().add(userLocationMarker);
                userLocationMarker.setVisible(true);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error al limpiar marcadores", e);
        }
    }

    /**
     * Agrega un marcador individual al mapa
     */
    private void addMarkerToMap(PointOfInterest poi) {
        try {
            if (!isFragmentActive || mapView == null) return;

            // Usar CustomMarker en lugar de Marker
            CustomMarker marker = new CustomMarker(mapView);

            // Configurar la posición del marcador
            marker.setPosition(new GeoPoint(poi.getLatitude(), poi.getLongitude()));
            marker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM);

            // Almacenar detalles del POI como datos en el marcador para depuración
            marker.setRelatedObject(poi);

            // Establecer icono según la categoría
            int iconResource;
            switch (poi.getCategory().toLowerCase()) {
                case "restaurante":
                    iconResource = R.drawable.ic_restaurant;
                    break;
                case "iglesia":
                    iconResource = R.drawable.ic_church;
                    break;
                case "hotel":
                    iconResource = R.drawable.ic_hotel;
                    break;
                case "atraccion":
                    iconResource = R.drawable.ic_attraction;
                    break;
                case "bano":
                    iconResource = R.drawable.ic_bathroom;
                    break;
                case "transporte":
                    iconResource = R.drawable.ic_transport;
                    break;
                case "farmacia":
                    iconResource = R.drawable.ic_pharmacy;
                    break;
                case "cajero a.":
                case "cajero":
                case "atm":
                case "cajero automatico":
                    iconResource = R.drawable.ic_atm;
                    break;
                default:
                    iconResource = R.drawable.baseline_location_on_24;
                    break;
            }

            Drawable icon = ContextCompat.getDrawable(requireContext(), iconResource);
            if (icon != null) {
                try {
                    // Crear un fondo circular para el icono
                    GradientDrawable background = new GradientDrawable();
                    background.setShape(GradientDrawable.OVAL);
                    background.setColor(getCategoryColor(poi.getCategory()));

                    // Crear un drawable compuesto con fondo circular y el icono
                    int padding = 8; // Padding para el icono dentro del círculo
                    int size = 48;   // Tamaño del icono final

                    // Configurar el icono para que sea blanco (para mejor contraste con el fondo de color)
                    icon.setColorFilter(Color.WHITE, PorterDuff.Mode.SRC_ATOP);

                    // Crear un bitmap para el drawable compuesto
                    Bitmap bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888);
                    Canvas canvas = new Canvas(bitmap);

                    // Dibujar el fondo
                    background.setBounds(0, 0, size, size);
                    background.draw(canvas);

                    // Dibujar el icono centrado
                    icon.setBounds(padding, padding, size - padding, size - padding);
                    icon.draw(canvas);

                    // Establecer el bitmap como icono del marcador
                    marker.setIcon(new BitmapDrawable(requireContext().getResources(), bitmap));

                    // Configurar el anclaje del marcador
                    marker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM);
                    marker.setInfoWindowAnchor(Marker.ANCHOR_CENTER, 0.0f);
                } catch (Exception e) {
                    // Si hay algún error, usar el método simple
                    Log.e(TAG, "Error al crear icono personalizado", e);
                    icon.setColorFilter(getCategoryColor(poi.getCategory()), PorterDuff.Mode.SRC_ATOP);
                    marker.setIcon(icon);
                }
            }

            // Establecer un título para debug (visible solo en logs)
            marker.setTitle(poi.getName() + " [" + poi.getCategory() + "]");

            // Asociar este marcador directamente con su POI
            markerToPOI.put(marker, poi);

            // Establecer el listener del marcador con LOG detallado para depuración
            marker.setOnMarkerClickListener(new Marker.OnMarkerClickListener() {
                @Override
                public boolean onMarkerClick(Marker marker1, MapView mapView) {
                    try {
                        if (!isFragmentActive) return false;

                        // Limpiar ruta actual si existe
                        clearRoute();

                        // Obtener el POI asociado al marcador
                        CustomMarker customMarker = (CustomMarker) marker1;
                        Object relatedObject = customMarker.getRelatedObject();

                        if (relatedObject instanceof PointOfInterest) {
                            PointOfInterest poi = (PointOfInterest) relatedObject;
                            // Registrar detalladamente para depuración
                            Log.d(TAG, "MARCADOR CLICKEADO - Nombre: " + poi.getName() +
                                  ", Categoría: " + poi.getCategory() +
                                  ", Lat/Lng: " + poi.getLatitude() + "/" + poi.getLongitude());

                            // Mostrar directamente la vista detallada
                            showPOIDetails(poi);

                            // Centrar el mapa en este marcador con una animación suave
                            mapController.animateTo(marker1.getPosition());
                        } else {
                            Log.e(TAG, "ERROR CRÍTICO: No se encontró POI asociado al marcador " + marker1.getTitle());
                        }

                        return true; // Consumir el evento
                    } catch (Exception e) {
                        Log.e(TAG, "Error en el manejo de click en marcador", e);
                        return false;
                    }
                }
            });

            // Agregar el marcador a la lista de todos los marcadores
            allMarkers.add(marker);

            // Solo agregar al mapa si coincide con la categoría actual o si es "Todos"
            if (currentCategory.equals("todos") ||
                currentCategory.equalsIgnoreCase(poi.getCategory())) {
                mapView.getOverlays().add(marker);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error al agregar marcador: " + e.getMessage());
        }
    }

    /**
     * Muestra la información detallada de un POI
     */
    private void showPOIDetails(PointOfInterest poi) {
        try {
            if (getActivity() instanceof MainAppActivity) {
                MainAppActivity activity = (MainAppActivity) getActivity();

                // Convertir categoría a nombre legible
                String categoryName = getCategoryDisplayName(poi.getCategory());

                // Obtener la URL de la imagen
                String imageUrl = poi.getImageUrl();

                // Obtener calificación para logging
                float rating = poi.getRating();

                // Log detallado para depuración
                Log.d(TAG, "MOSTRANDO DETALLES DE POI - Nombre: " + poi.getName() +
                      ", Categoría: " + poi.getCategory() +
                      ", URL imagen: " + imageUrl +
                      ", Calificación: " + rating +
                      ", Coordenadas: " + poi.getLatitude() + "," + poi.getLongitude());

                // Llamar al método con los parámetros aceptados, incluyendo rating
                activity.showMarkerInfo(
                    poi.getName(),
                    categoryName,
                    poi.getDescription(),
                    imageUrl,
                    poi.getLatitude(),
                    poi.getLongitude(),
                    rating  // Añadir el rating como parámetro
                );
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar detalles del POI", e);
        }
    }

    /**
     * Filtra los marcadores por categoría
     */
    private void filterMarkersByCategory(String category) {
        try {
            if (!isFragmentActive || mapView == null) return;

            Log.d(TAG, "Filtrando marcadores por categoría: " + category);

            // Actualizar la categoría actual
            currentCategory = category;

            // Primero quitar todos los marcadores del mapa (excepto overlays especiales)
            List<Overlay> overlaysToKeep = new ArrayList<>();

            // Guardar overlays que no son marcadores
            for (Overlay overlay : mapView.getOverlays()) {
                if (!(overlay instanceof Marker) || overlay == userLocationMarker) {
                    // Mantener overlays que no son marcadores, o si es el marcador de ubicación del usuario
                    overlaysToKeep.add(overlay);
                }
            }

            // Limpiar todos los overlays
            mapView.getOverlays().clear();

            // Restaurar los overlays especiales
            mapView.getOverlays().addAll(overlaysToKeep);

            // Recorrer la asociación marcador-POI y agregar solo los que coincidan con la categoría
            int addedMarkers = 0;
            for (Map.Entry<Marker, PointOfInterest> entry : markerToPOI.entrySet()) {
                Marker marker = entry.getKey();
                PointOfInterest poi = entry.getValue();

                boolean showMarker = category.equals("todos") ||
                               category.equalsIgnoreCase(poi.getCategory());

                if (showMarker) {
                    mapView.getOverlays().add(marker);
                    addedMarkers++;
                }
            }

            // Asegurar que el marcador de ubicación esté visible y en la capa superior
            if (userLocationMarker != null) {
                // Asegurar que el marcador del usuario esté siempre visible y en la capa superior
                mapView.getOverlays().remove(userLocationMarker);
                mapView.getOverlays().add(userLocationMarker);
                userLocationMarker.setVisible(true);
            }

            Log.d(TAG, "Filtrado completado. Mostrando " + addedMarkers + " marcadores de categoría: " + category);

            // Actualizar el mapa
            mapView.invalidate();

        } catch (Exception e) {
            Log.e(TAG, "Error al filtrar marcadores por categoría", e);
        }
    }

    /**
     * Configura los listeners de los chips de categoría
     */
    private void setupCategoryChips() {
        try {
            // Encuentra el grupo de chips (ahora en filter_card)
            categoryChipGroup = getView().findViewById(R.id.category_chip_group);
            if (categoryChipGroup == null) {
                Log.e(TAG, "No se encontró el ChipGroup");
                return;
            }

            // Limpiar chips existentes
            categoryChipGroup.removeAllViews();

            // Añadir el chip "Todos" primero
            Chip chipTodos = new Chip(requireContext());
            chipTodos.setText("Todos");
            chipTodos.setTag("todos");
            chipTodos.setCheckable(true);
            chipTodos.setChecked(true); // Seleccionado por defecto
            // chipTodos.setChipBackgroundColorResource(R.color.chip_background_color_state_list); // Comentado: no existe este color
            // chipTodos.setTextColor(getResources().getColorStateList(R.color.chip_text_color_state_list, null)); // Comentado: no existe este color
            categoryChipGroup.addView(chipTodos);

            // Si no tenemos categorías cargadas todavía desde Firebase, agregar algunos chips predeterminados
            if (categorias.isEmpty()) {
                // Añadir categorías predeterminadas mientras se cargan desde Firebase
                String[] defaultCategories = {"hotel", "restaurante", "iglesia", "atraccion", "cajero"};

                for (String category : defaultCategories) {
                    Chip chip = new Chip(requireContext());
                    chip.setText(getCategoryDisplayName(category));
                    chip.setTag(category);
                    chip.setCheckable(true);
                    // chip.setChipBackgroundColorResource(R.color.chip_background_color_state_list);
                    // chip.setTextColor(getResources().getColorStateList(R.color.chip_text_color_state_list, null));
                    categoryChipGroup.addView(chip);
                }
            } else {
                // Usar las categorías cargadas desde Firebase
                for (Map<String, Object> categoria : categorias) {
                    String id = (String) categoria.get("id");
                    if (id != null && !id.isEmpty() && !id.equals("todos")) {
                        Chip chip = new Chip(requireContext());
                        chip.setText(getCategoryDisplayName(id));
                        chip.setTag(id);
                        chip.setCheckable(true);
                        // chip.setChipBackgroundColorResource(R.color.chip_background_color_state_list);
                        // chip.setTextColor(getResources().getColorStateList(R.color.chip_text_color_state_list, null));
                        categoryChipGroup.addView(chip);
                    }
                }
            }

            // Configurar listener para los chips
            categoryChipGroup.setOnCheckedChangeListener((group, checkedId) -> {
                // Obtener el chip seleccionado
                Chip selectedChip = group.findViewById(checkedId);
                if (selectedChip != null) {
                    String category = (String) selectedChip.getTag();

                    // Actualizar la categoría actual
                    currentCategory = category;

                    // Filtrar marcadores por la categoría seleccionada
                    filterMarkersByCategory(category);

                    // Log para debug
                    Log.d(TAG, "Categoría seleccionada: " + category);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error al configurar chips de categoría", e);
        }
    }

    /**
     * Actualiza los chips de categoría cuando se cargan los datos desde Firebase
     */
    private void updateCategoryChips() {
        try {
            if (categoryChipGroup != null && !categorias.isEmpty()) {
                // Guardar la categoría seleccionada actualmente
                String selectedCategory = currentCategory;

                // Reconstruir los chips
                setupCategoryChips();

                // Volver a seleccionar la categoría anterior si existe
                if (selectedCategory != null) {
                    for (int i = 0; i < categoryChipGroup.getChildCount(); i++) {
                        View view = categoryChipGroup.getChildAt(i);
                        if (view instanceof Chip) {
                            Chip chip = (Chip) view;
                            if (selectedCategory.equals(chip.getTag())) {
                                chip.setChecked(true);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al actualizar chips de categoría", e);
        }
    }

    /**
     * Muestra la tarjeta de información de un lugar
     */
    private void showPlaceInfoCard(PointOfInterest poi, int index) {
        try {
            // Obtener la actividad
            if (getActivity() instanceof MainAppActivity) {
                MainAppActivity activity = (MainAppActivity) getActivity();

                // Convertir categoría a nombre legible
                String categoryName = getCategoryDisplayName(poi.getCategory());

                // Obtener la URL de la imagen
                String imageUrl = poi.getImageUrl();

                // Mostrar la información en la tarjeta de la actividad principal
                activity.showMarkerInfo(
                    poi.getName(),
                    categoryName,
                    poi.getDescription(),
                    imageUrl,
                    poi.getLatitude(),
                    poi.getLongitude()
                );
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar tarjeta de información", e);
        }
    }

    /**
     * Oculta la tarjeta de información
     */
    private void hidePlaceInfoCard() {
        // No hace nada, ya que la tarjeta siempre está oculta
    }

    /**
     * Comparte la información de un lugar
     */
    private void sharePlaceInfo(PointOfInterest poi) {
        try {
            String shareText = poi.getName() + "\n" +
                    "Categoría: " + getCategoryDisplayName(poi.getCategory()) + "\n" +
                    "Ubicación: " + poi.getLatitude() + ", " + poi.getLongitude() + "\n\n" +
                    poi.getDescription() + "\n\n" +
                    "Compartido desde ItatiExplore";

            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_TEXT, shareText);
            startActivity(Intent.createChooser(shareIntent, "Compartir lugar"));

        } catch (Exception e) {
            Log.e(TAG, "Error al compartir información del lugar", e);
            Toast.makeText(getContext(), "No se pudo compartir", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Busca lugares por nombre
     */
    private void searchPlaces(String query) {
        try {
            if (pointsOfInterest == null || pointsOfInterest.isEmpty()) return;

            // Normalizar la consulta (quitar acentos, convertir a minúsculas)
            String normalizedQuery = Normalizer.normalize(query.toLowerCase(), Normalizer.Form.NFD)
                                              .replaceAll("\\p{InCombiningDiacriticalMarks}", "");

            // Buscar coincidencias
            for (PointOfInterest poi : pointsOfInterest) {
                String normalizedName = Normalizer.normalize(poi.getName().toLowerCase(), Normalizer.Form.NFD)
                                                 .replaceAll("\\p{InCombiningDiacriticalMarks}", "");

                if (normalizedName.contains(normalizedQuery)) {
                    // Centrar el mapa en el lugar encontrado
                    GeoPoint poiPoint = new GeoPoint(poi.getLatitude(), poi.getLongitude());
                    mapController.animateTo(poiPoint);
                    mapController.setZoom(18.0);

                    // Registrar para depuración
                    Log.d(TAG, "Búsqueda encontró: " + poi.getName() + " con categoría: " + poi.getCategory());

                    // Mostrar directamente los detalles
                    showPOIDetails(poi);
                    return;
                }
            }

            // Si no se encontraron coincidencias
            Toast.makeText(getContext(), "No se encontraron lugares con ese nombre", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Log.e(TAG, "Error al buscar lugares", e);
        }
    }

    /**
     * Solicita actualizaciones de ubicación
     */
    private void startLocationUpdates() {
        try {
            // Verificar si el fragmento sigue activo
            if (!isFragmentActive || !isAdded() || getActivity() == null) {
                Log.w(TAG, "No se pueden iniciar actualizaciones de ubicación: fragmento no activo");
                return;
            }

            // Verificar permiso
            if (ContextCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "No se pueden iniciar actualizaciones de ubicación: permiso no concedido");
                requestLocationPermission();
                return;
            }

            // Inicializar LocationManager si es necesario
            if (locationManager == null) {
                locationManager = (LocationManager) requireContext().getSystemService(Context.LOCATION_SERVICE);
            }

            if (locationManager == null) {
                Log.e(TAG, "No se pudo obtener LocationManager");
                return;
            }

            // Verificar si los proveedores están habilitados
            boolean isGpsEnabled = false;
            boolean isNetworkEnabled = false;

            try {
                isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar proveedor GPS", e);
            }

            try {
                isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar proveedor de red", e);
            }

            if (!isGpsEnabled && !isNetworkEnabled) {
                Log.w(TAG, "No hay proveedores de ubicación habilitados");
                showLocationSettings();
                return;
            }

            // Parar cualquier listener anterior antes de crear uno nuevo
            if (locationListener != null) {
                try {
                    locationManager.removeUpdates(locationListener);
                } catch (Exception e) {
                    Log.e(TAG, "Error al detener actualizaciones previas", e);
                }
            }

            // Crear nuevo listener
            locationListener = new LocationListener() {
                @Override
                public void onLocationChanged(@NonNull Location location) {
                    try {
                        if (!isFragmentActive) return;

                        // Actualizar variables con la nueva ubicación
                        currentLat = location.getLatitude();
                        currentLng = location.getLongitude();
                        lastKnownLocation = location;

                        Log.d(TAG, "Nueva ubicación: " + currentLat + ", " + currentLng);

                        // Ejecutar las actualizaciones en el hilo principal
                        new Handler(Looper.getMainLooper()).post(() -> {
                            try {
                                // Actualizar el marcador de usuario con la nueva posición
                                updateUserLocationMarker(currentLat, currentLng);

                                // Se eliminó la actualización automática de ruta
                                // para mantener la ruta original una vez calculada

                                // Forzar actualización del mapa
                                if (mapView != null) {
                                    mapView.invalidate();
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error en actualizaciones de UI", e);
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "Error al procesar cambio de ubicación", e);
                    }
                }

                @Override
                public void onStatusChanged(String provider, int status, Bundle extras) {}

                @Override
                public void onProviderEnabled(@NonNull String provider) {
                    try {
                        if (!isFragmentActive) return;

                        Log.d(TAG, "Proveedor de ubicación habilitado: " + provider);

                        // Solicitar ubicación inmediatamente
                        requestSingleLocationUpdate();
                    } catch (Exception e) {
                        Log.e(TAG, "Error al manejar proveedor habilitado", e);
                    }
                }

                @Override
                public void onProviderDisabled(@NonNull String provider) {
                    try {
                        if (!isFragmentActive) return;

                        Log.d(TAG, "Proveedor de ubicación deshabilitado: " + provider);

                        // Verificar si ambos proveedores están deshabilitados
                        if (LocationManager.GPS_PROVIDER.equals(provider) ||
                                LocationManager.NETWORK_PROVIDER.equals(provider)) {

                            boolean isGpsEnabled = false;
                            boolean isNetworkEnabled = false;

                            try {
                                isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
                            } catch (Exception e) {
                                Log.e(TAG, "Error al verificar proveedor GPS", e);
                            }

                            try {
                                isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
                            } catch (Exception e) {
                                Log.e(TAG, "Error al verificar proveedor de red", e);
                            }

                            if (!isGpsEnabled && !isNetworkEnabled) {
                                showLocationSettings();
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error al manejar proveedor deshabilitado", e);
                    }
                }
            };

            // Registrar las actualizaciones de ubicación con intervalos mucho más cortos para mejor seguimiento
            // Usar intervalos más pequeños (cada 500ms o 1 metro)
            if (isGpsEnabled) {
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER, 500, 1, locationListener);
                Log.d(TAG, "Solicitando actualizaciones de GPS (cada 500ms o 1m)");

                // Activar overlay de ubicación si lo tenemos
                if (myLocationOverlay != null) {
                    myLocationOverlay.enableMyLocation();
                    // No activamos followLocation para evitar el centrado automático
                }
            }

            if (isNetworkEnabled) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER, 500, 1, locationListener);
                Log.d(TAG, "Solicitando actualizaciones de red (cada 500ms o 1m)");

                // Activar overlay de ubicación si lo tenemos
                if (myLocationOverlay != null) {
                    myLocationOverlay.enableMyLocation();
                    // No activamos followLocation para evitar el centrado automático
                }
            }

            // Intentar obtener la última ubicación conocida
            requestSingleLocationUpdate();

            // Inicializar booleano de seguimiento
            isFollowingLocation = true;

            // Programar la desactivación del seguimiento después de unos segundos
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                isFollowingLocation = false;
            }, 8000); // 8 segundos de seguimiento automático

        } catch (SecurityException se) {
            Log.e(TAG, "Error de seguridad al iniciar actualizaciones de ubicación", se);
        } catch (Exception e) {
            Log.e(TAG, "Error al iniciar actualizaciones de ubicación", e);
        }
    }

    /**
     * Solicita una actualización única de ubicación
     */
    private void requestSingleLocationUpdate() {
        if (!isFragmentActive || !isAdded() || getActivity() == null || getContext() == null) {
            Log.w(TAG, "No se puede solicitar ubicación: fragmento no activo");
            return;
        }

        try {
            if (ContextCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {

                Log.w(TAG, "Permisos de ubicación no concedidos");
                requestLocationPermission();
                return;
            }

            // Obtener el servicio de ubicación
            LocationManager locationManager = null;
            try {
                locationManager = (LocationManager) requireContext().getSystemService(Context.LOCATION_SERVICE);
            } catch (Exception e) {
                Log.e(TAG, "Error al obtener LocationManager", e);
                return;
            }

            if (locationManager == null) {
                Log.e(TAG, "LocationManager es nulo");
                return;
            }

            // Verificar si los proveedores de ubicación están habilitados
            boolean isGpsEnabled = false;
            boolean isNetworkEnabled = false;

            try {
                isGpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar proveedor GPS", e);
            }

            try {
                isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar proveedor de red", e);
            }

            if (!isGpsEnabled && !isNetworkEnabled) {
                Log.w(TAG, "No hay proveedores de ubicación habilitados");
                showLocationSettings();
                return;
            }

            // Determinar el mejor proveedor disponible
            String provider = null;
            if (isGpsEnabled) {
                provider = LocationManager.GPS_PROVIDER;
                Log.d(TAG, "Usando proveedor GPS");
            } else if (isNetworkEnabled) {
                provider = LocationManager.NETWORK_PROVIDER;
                Log.d(TAG, "Usando proveedor de red");
            }

            if (provider == null) {
                Log.e(TAG, "No se pudo determinar un proveedor de ubicación");
                return;
            }

            // Intentar obtener la última ubicación conocida
            try {
                final Location lastLocation = locationManager.getLastKnownLocation(provider);
                if (lastLocation != null) {
                    // Verificar si la ubicación es reciente (menos de 30 segundos)
                    long locationAge = System.currentTimeMillis() - lastLocation.getTime();
                    if (locationAge < 30000) {
                        Log.d(TAG, "Usando ubicación reciente de " + provider);

                        // Actualizar variables con la ubicación
                        currentLat = lastLocation.getLatitude();
                        currentLng = lastLocation.getLongitude();

                        // Centrar el mapa en la ubicación
                        if (mapController != null) {
                            mapController.animateTo(new GeoPoint(currentLat, currentLng));
                        }

                        return;
                    } else {
                        Log.d(TAG, "Ubicación demasiado antigua (" + (locationAge / 1000) + " segundos), solicitando actualización");
                    }
                }
            } catch (SecurityException se) {
                Log.e(TAG, "Error de seguridad al obtener última ubicación", se);
            } catch (Exception e) {
                Log.e(TAG, "Error al obtener última ubicación", e);
            }

            // Solicitar una actualización única
            try {
                // Usar un listener temporal para esta solicitud específica
                final LocationManager finalLocationManager = locationManager;
                LocationListener singleUpdateListener = new LocationListener() {
                    @Override
                    public void onLocationChanged(@NonNull Location location) {
                        try {
                            // Detener las actualizaciones después de recibir ubicación
                            finalLocationManager.removeUpdates(this);

                            // Actualizar el mapa
                            if (isAdded()) {
                                Log.d(TAG, "Ubicación recibida de solicitud única");

                                // Actualizar variables con la ubicación
                                currentLat = location.getLatitude();
                                currentLng = location.getLongitude();

                                // Centrar el mapa en la ubicación
                                if (mapController != null) {
                                    mapController.animateTo(new GeoPoint(currentLat, currentLng));
                                }
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error al procesar ubicación recibida", e);
                        }
                    }

                    @Override
                    public void onStatusChanged(String provider, int status, Bundle extras) {}

                    @Override
                    public void onProviderEnabled(@NonNull String provider) {}

                    @Override
                    public void onProviderDisabled(@NonNull String provider) {}
                };

                // Programar un timeout para cancelar la solicitud si tarda demasiado
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    try {
                        if (isAdded()) {
                            finalLocationManager.removeUpdates(singleUpdateListener);
                            Log.d(TAG, "Timeout de solicitud de ubicación única");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error al cancelar solicitud de ubicación", e);
                    }
                }, 15000); // 15 segundos de timeout

                // Solicitar la actualización
                Log.d(TAG, "Solicitando actualización única de ubicación");
                locationManager.requestSingleUpdate(provider, singleUpdateListener, Looper.getMainLooper());
            } catch (SecurityException se) {
                Log.e(TAG, "Error de seguridad al solicitar ubicación", se);
            } catch (Exception e) {
                Log.e(TAG, "Error al solicitar actualización de ubicación", e);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error general en requestSingleLocationUpdate", e);
        }
    }

    /**
     * Muestra un diálogo para que el usuario active los servicios de ubicación
     */
    private void showLocationSettings() {
        try {
            // Verificar estado del fragmento
            if (!isAdded() || getContext() == null || getActivity() == null) {
                Log.w(TAG, "No se puede mostrar diálogo de servicios de ubicación: fragmento no activo");
                return;
            }

            // Verificar si la actividad está terminando
            if (getActivity().isFinishing() || getActivity().isDestroyed()) {
                Log.w(TAG, "No se puede mostrar diálogo de servicios de ubicación: actividad finalizando");
                return;
            }

            // Usar el contexto de la actividad para crear el diálogo en el hilo principal
            getActivity().runOnUiThread(() -> {
                try {
                    // Verificar de nuevo el estado del fragmento antes de mostrar el diálogo
                    if (!isAdded() || getContext() == null || getActivity() == null) {
                        return;
                    }

                    new AlertDialog.Builder(getContext())
                        .setTitle("Activar GPS")
                        .setMessage("Para ver tu ubicación en tiempo real en el mapa, necesitas activar el GPS. Sin GPS activo, no podrás usar todas las funciones del mapa.")
                        .setPositiveButton("Ir a configuración", (dialog, which) -> {
                            try {
                                // Verificar si el fragmento sigue activo
                                if (!isAdded() || getActivity() == null) {
                                    return;
                                }

                                Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                // Usar un enfoque más seguro para iniciar la actividad
                                if (intent.resolveActivity(requireActivity().getPackageManager()) != null) {
                                    startActivity(intent);
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error al abrir configuración de ubicación", e);
                            }
                        })
                        .setNegativeButton("No activar", null)
                        .setCancelable(false)
                        .create()
                        .show();
                } catch (Exception e) {
                    Log.e(TAG, "Error al crear diálogo de servicios de ubicación", e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error general en showLocationSettings", e);
        }
    }

    /**
     * Solicita permiso de ubicación
     */
    private void requestLocationPermission() {
        try {
            locationPermissionRequest.launch(new String[] {
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            });
        } catch (Exception e) {
            Log.e(TAG, "Error al solicitar permiso de ubicación", e);
        }
    }

    /**
     * Verifica si un string no está vacío
     */
    private boolean isNotEmpty(String str) {
        return str != null && !str.isEmpty();
    }

    @Override
    public void onLocationChanged(@NonNull Location location) {
        try {
            // Actualizar las variables con la nueva ubicación
            currentLat = location.getLatitude();
            currentLng = location.getLongitude();
            lastKnownLocation = location;

            Log.d(TAG, "Ubicación actualizada (onLocationChanged principal): " + currentLat + ", " + currentLng);

            // Ejecutar actualizaciones en el hilo principal para evitar problemas
            new Handler(Looper.getMainLooper()).post(() -> {
                try {
                    // Actualizar nuestro marcador personalizado de punto azul
                    updateUserLocationMarker(currentLat, currentLng);

                    // Asegurar que el mapa se actualice
                    if (mapView != null) {
                        mapView.invalidate();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error en actualizaciones de UI desde onLocationChanged principal", e);
                }
            });

            // Si tenemos un punto de destino y se está siguiendo la ubicación, actualizar la ruta
            if (isFollowingLocation && destinationPoint != null && currentRoute != null) {
                // Crear punto de origen con la ubicación actual
                GeoPoint origin = new GeoPoint(location.getLatitude(), location.getLongitude());

                // Solicitar nueva ruta
                showRouteToDestination(destinationPoint);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error en onLocationChanged principal", e);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isFragmentActive = true;

        try {
            // Reanudar mapa
            if (mapView != null) {
                mapView.onResume();

                // Asegurar que no haya popups nativos de OSMDroid
                SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

                // Aplicar el interceptor para todos los overlays
                removeAllInfoWindowsFromMap();
            }

            // Reactivar overlay de ubicación
            if (myLocationOverlay != null) {
                myLocationOverlay.enableMyLocation();
            }

            // Asegurar que el marcador de ubicación del usuario esté visible
            if (userLocationMarker == null) {
                createUserLocationMarker();
            } else {
                // Si ya existe, asegurarse de que esté en la parte superior
                if (mapView != null) {
                    mapView.getOverlays().remove(userLocationMarker);
                    mapView.getOverlays().add(userLocationMarker);
                    userLocationMarker.setVisible(true);
                    mapView.invalidate();
                }
            }

            // Recargar datos al volver a la pantalla (por si se agregaron nuevos lugares)
            loadMarkersFromFirebase();

            // Verificar los permisos de ubicación y el estado del GPS
            try {
                if (getContext() != null) {
                    // Comprobar si tenemos permiso de ubicación
                    boolean hasLocationPermission = ContextCompat.checkSelfPermission(getContext(),
                            Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;

                    if (hasLocationPermission) {
                        startLocationUpdates();
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar estado de ubicación en onResume", e);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error general en onResume", e);
        }
    }

    /**
     * Elimina completamente todos los InfoWindows del mapa
     */
    private void removeAllInfoWindowsFromMap() {
        try {
            if (mapView == null) return;

            // Cerrar todos los popups existentes de forma segura
            SafeInfoWindowHelper.safeCloseInfoWindows(mapView);

            // Recorrer todos los overlays y forzar a null los infowindows
            try {
                if (mapView.getOverlays() != null) {
                    for (Overlay overlay : mapView.getOverlays()) {
                        if (overlay instanceof Marker) {
                            Marker marker = (Marker) overlay;
                            marker.setInfoWindow(null);
                            // Asegurar que no tenga título ni snippet para evitar popups automáticos
                            marker.setTitle(null);
                            marker.setSnippet(null);
                        }
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error al procesar overlays", e);
            }

            // Actualizar el mapa
            mapView.invalidate();
        } catch (Exception e) {
            Log.e(TAG, "Error al eliminar InfoWindows", e);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        isFragmentActive = false;

        try {
            // Pausar mapa
            if (mapView != null) {
                mapView.onPause();
            }

            // Desactivar overlay de ubicación
            if (myLocationOverlay != null) {
                myLocationOverlay.disableMyLocation();
            }

            // Limpiar ruta al salir del fragmento
            clearRoute();

        } catch (Exception e) {
            Log.e(TAG, "Error en onPause", e);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        try {
            // Marcar el fragmento como inactivo
            isFragmentActive = false;

            // Parar actualizaciones de ubicación
            if (locationManager != null && locationListener != null) {
                try {
                    locationManager.removeUpdates(locationListener);
                } catch (Exception e) {
                    Log.e(TAG, "Error al detener actualizaciones de ubicación", e);
                }
            }

            // Limpiar el mapa
            if (mapView != null) {
                mapView.onDetach();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error en onDestroyView", e);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        try {
            // Limpiar referencias
            isFragmentActive = false;

            if (mapView != null) {
                mapView.onDetach();
                mapView = null;
            }

            if (locationManager != null && locationListener != null) {
                try {
                    locationManager.removeUpdates(locationListener);
                } catch (Exception e) {
                    Log.e(TAG, "Error al eliminar actualizaciones de ubicación", e);
                }
            }

            locationManager = null;
            locationListener = null;
            myLocationOverlay = null;

            // Referencia al contexto
            categoryChipGroup = null;
            loadingIndicator = null;

            // Liberar listas
            if (pointsOfInterest != null) {
                pointsOfInterest.clear();
                pointsOfInterest = null;
            }

            if (allMarkers != null) {
                allMarkers.clear();
                allMarkers = null;
            }

            // Limpiar el mapa de asociaciones de marcadores
            if (markerToPOI != null) {
                markerToPOI.clear();
                markerToPOI = null;
            }

            // Limpiar referencia al marcador de Itatí
            itatiMarker = null;
        } catch (Exception e) {
            Log.e(TAG, "Error en onDestroy", e);
        }
    }

    /**
     * Clase para representar un punto de interés en el mapa
     */
    private static class PointOfInterest {
        private final String name;
        private final double latitude;
        private final double longitude;
        private final String imageUrl;
        private final String description;
        private final String category;
        private final float rating;

        public PointOfInterest(String name, double latitude, double longitude, String imageUrl,
                           String description, String category, float rating) {
            this.name = name;
            this.latitude = latitude;
            this.longitude = longitude;
            this.imageUrl = imageUrl;
            this.description = description;
            this.category = category;
            this.rating = rating;
        }

        public String getName() {
            return name;
        }

        public double getLatitude() {
            return latitude;
        }

        public double getLongitude() {
            return longitude;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public String getDescription() {
            return description;
        }

        public String getCategory() {
            return category;
        }

        public float getRating() {
            return rating;
        }
    }

    /**
     * Obtiene el nombre de la categoría para mostrar en la interfaz de usuario
     * basándose en el ID de la categoría
     */
    private String getCategoryDisplayName(String categoryId) {
        try {
            // Buscar en la lista de categorías cargadas de Firebase
            for (Map<String, Object> categoria : categorias) {
                String id = (String) categoria.get("id");
                if (id != null && id.equals(categoryId)) {
                    // Verificar si existe el campo nombre
                    String nombre = (String) categoria.get("nombre");
                    if (nombre != null && !nombre.isEmpty()) {
                        return nombre;
                    }
                }
            }

            // Si no se encuentra en Firebase o no tiene el campo nombre, usar la lógica de respaldo
            switch (categoryId.toLowerCase()) {
                case "restaurante":
                    return "Restaurante";
                case "hotel":
                    return "Hotel";
                case "iglesia":
                    return "Iglesia";
                case "atraccion":
                    return "Atracción";
                case "todos":
                    return "Todos";
                case "farmacia":
                    return "Farmacia";
                case "cajeros a.":
                case "cajero":
                case "atm":
                case "cajero automatico":
                    return "Cajeros Automáticos";
                default:
                    // Convertir primera letra a mayúscula
                    if (categoryId.length() > 1) {
                        return categoryId.substring(0, 1).toUpperCase() + categoryId.substring(1);
                    } else {
                        return categoryId.toUpperCase();
                    }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al obtener nombre de categoría", e);
            return categoryId; // Devolver el ID como fallback
        }
    }

    /**
     * Agregar listener para manejar clics en el mapa
     */
    private void setupMapClickOverlay() {
        Overlay mapClickOverlay = new Overlay() {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent e, MapView mapView) {
                // Ya no es necesario ocultar la tarjeta ya que siempre está oculta
                // Cerrar cualquier InfoWindow que pueda estar abierta
                SafeInfoWindowHelper.safeCloseInfoWindows(mapView);
                return super.onSingleTapConfirmed(e, mapView);
            }
        };
        mapView.getOverlays().add(mapClickOverlay);
    }

    /**
     * Obtiene el recurso de icono correspondiente a una categoría
     */
    private int getCategoryIcon(String category) {
        try {
            // Normalizar la categoría a minúsculas
            String categoryLower = category.toLowerCase();

            // Primero, buscar en las categorías cargadas de Firebase si tienen un campo "icon_resource"
            for (Map<String, Object> categoria : categorias) {
                String id = (String) categoria.get("id");
                if (id != null && id.equalsIgnoreCase(category)) {
                    // Verificar si existe el campo icon_resource como String
                    Object iconResourceObj = categoria.get("icon_resource");
                    if (iconResourceObj instanceof String) {
                        String iconResourceName = (String) iconResourceObj;
                        // Intentar obtener el recurso por nombre
                        int resourceId = getResources().getIdentifier(
                            iconResourceName, "drawable", requireContext().getPackageName());
                        if (resourceId != 0) {
                            return resourceId;
                        }
                    }
                }
            }

            // Usar lógica de respaldo si no se encuentra en Firebase
            switch (categoryLower) {
                case "restaurante":
                    return R.drawable.ic_restaurant;
                case "hotel":
                    return R.drawable.ic_hotel;
                case "iglesia":
                    return R.drawable.ic_church;
                case "atraccion":
                    return R.drawable.ic_attraction;
                case "bano":
                    return R.drawable.ic_info; // Cambiado de ic_wc
                case "transporte":
                    return R.drawable.ic_location; // Cambiado de ic_bus
                case "farmacia":
                    return R.drawable.ic_info; // Usando ic_info para farmacia
                case "cajeros a.":
                case "cajero":
                case "atm":
                case "cajero automatico":
                    return R.drawable.ic_atm; // Usando ic_atm para cajeros
                default:
                    return R.drawable.ic_location;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al obtener icono de categoría", e);
            return R.drawable.ic_location; // Icono por defecto
        }
    }

    /**
     * Obtiene el color asociado a una categoría
     */
    private int getCategoryColor(String category) {
        switch (category.toLowerCase()) {
            case "iglesia":
                return Color.parseColor("#5C6BC0"); // Azul indigo más vibrante
            case "restaurante":
                return Color.parseColor("#EF5350"); // Rojo más vibrante
            case "hotel":
                return Color.parseColor("#26A69A"); // Verde teal más vibrante
            case "atraccion":
                return Color.parseColor("#AB47BC"); // Púrpura más vibrante
            case "bano":
                return Color.parseColor("#78909C"); // Azul gris más vibrante
            case "transporte":
                return Color.parseColor("#FFA726"); // Naranja más vibrante
            case "farmacia":
                return Color.parseColor("#4CAF50"); // Verde más vibrante para farmacias
            case "cajero a.":
            case "cajero":
            case "atm":
            case "cajero automatico":
                return Color.parseColor("#FF9800"); // Ámbar para cajeros automáticos
            default:
                return Color.parseColor("#42A5F5"); // Azul por defecto más vibrante
        }
    }

    /**
     * Clase CustomMarker que extiende Marker para permitir almacenar objetos relacionados
     */
    private static class CustomMarker extends Marker {
        private Object relatedObject;

        public CustomMarker(MapView mapView) {
            super(mapView);
        }

        public void setRelatedObject(Object object) {
            this.relatedObject = object;
        }

        public Object getRelatedObject() {
            return relatedObject;
        }
    }

    /**
     * Verifica si hay conexión a internet activa y funcional
     */
    private boolean isNetworkConnected() {
        try {
            if (getContext() == null) return false;

            // Obtener el servicio de conectividad
            android.net.ConnectivityManager cm =
                (android.net.ConnectivityManager) getContext().getSystemService(Context.CONNECTIVITY_SERVICE);

            if (cm == null) return false;

            // Verificar la conectividad de red
            android.net.NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            boolean isConnected = activeNetwork != null &&
                                 activeNetwork.isConnectedOrConnecting();

            if (!isConnected) return false;

            // Prueba adicional: verificar acceso a internet real
            try {
                // Intentar hacer una conexión de prueba
                java.net.URL url = new java.net.URL("https://www.google.com");
                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url.openConnection();
                conn.setConnectTimeout(3000); // 3 segundos
                conn.connect();
                boolean hasInternet = conn.getResponseCode() == 200;
                conn.disconnect();

                if (!hasInternet) {
                    Log.d(TAG, "Hay conectividad de red pero no acceso a internet");
                }

                return hasInternet;
            } catch (Exception e) {
                Log.e(TAG, "Error al verificar conectividad real a internet: " + e.getMessage());
                // Si falla la prueba pero hay conectividad, asumir que hay internet de todas formas
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error general al verificar conexión a internet", e);
            return false;
        }
    }

    /**
     * Carga las categorías desde Firestore
     */
    private void loadCategoriasFromFirebase() {
        try {
            // Verificar si el fragmento sigue activo
            if (!isFragmentActive || getContext() == null) return;

            // Verificar si tenemos acceso a Firestore
            if (db == null) {
                Log.e(TAG, "Firestore no está inicializado para cargar categorías");
                return;
            }

            Log.d(TAG, "Iniciando carga de categorías desde Firebase...");

            // Obtener las categorías de Firebase
            db.collection("categorias")
                .get()
                .addOnSuccessListener(queryDocumentSnapshots -> {
                    // Limpiar lista de categorías
                    categorias.clear();

                    int count = 0;
                    for (DocumentSnapshot document : queryDocumentSnapshots) {
                        try {
                            // Obtener el ID del documento
                            String id = document.getId();
                            Log.d(TAG, "Categoría encontrada: " + id);

                            // Crear un mapa con los datos
                            Map<String, Object> categoria = new HashMap<>();
                            categoria.put("id", id);

                            // Añadir todos los campos del documento
                            Map<String, Object> data = document.getData();
                            if (data != null) {
                                categoria.putAll(data);

                                // Mostrar todos los datos de la categoría
                                StringBuilder sb = new StringBuilder();
                                sb.append("Datos de categoría ").append(id).append(":\n");
                                for (Map.Entry<String, Object> entry : data.entrySet()) {
                                    sb.append("  - ").append(entry.getKey()).append(": ")
                                      .append(entry.getValue()).append("\n");
                                }
                                Log.d(TAG, sb.toString());

                                // Agregar a la lista de categorías
                                categorias.add(categoria);
                                count++;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error al procesar categoría: " + document.getId(), e);
                        }
                    }

                    Log.d(TAG, "Carga de categorías completada. Total: " + count);

                    // Actualizar los chips de categoría con los datos cargados
                    updateCategoryChips();
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error al cargar categorías desde Firebase", e);
                });

        } catch (Exception e) {
            Log.e(TAG, "Error general en loadCategoriasFromFirebase", e);
        }
    }

    /**
     * Configura el botón de detalles
     */
    private void setupDetailsButton() {
        if (btnDetails != null) {
            btnDetails.setOnClickListener(v -> {
                int poiIndex = (int) placeInfoCard.getTag();
                if (poiIndex >= 0 && poiIndex < pointsOfInterest.size()) {
                    PointOfInterest poi = pointsOfInterest.get(poiIndex);

                    // Mostrar información sobre el lugar
                    Toast.makeText(getContext(), "Mostrando detalles de: " + poi.getName(), Toast.LENGTH_SHORT).show();

                    // Centrar el mapa en la ubicación del POI
                    GeoPoint poiPoint = new GeoPoint(poi.getLatitude(), poi.getLongitude());
                    if (mapController != null) {
                        mapController.animateTo(poiPoint);
                        mapController.setZoom(18); // Aumentar el zoom para ver bien el lugar
                    }

                    // Ocultar la tarjeta de información del lugar
                    hidePlaceInfoCard();
                }
            });
        }
    }

    /**
     * Obtiene el controlador del mapa
     */
    public IMapController getMapController() {
        return mapController;
    }

    /**
     * Muestra una ruta desde la ubicación actual hasta un punto de destino
     * @param destination punto de destino
     */
    public void showRouteToDestination(GeoPoint destination) {
        try {
            // Guardar el punto de destino
            destinationPoint = destination;

            // Verificar si tenemos la ubicación del usuario
            if (lastKnownLocation == null) {
                Toast.makeText(getContext(), "Esperando ubicación actual...", Toast.LENGTH_SHORT).show();
                return;
            }

            // Crear punto de origen con la ubicación actual
            GeoPoint origin = new GeoPoint(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude());

            // Mostrar indicador de carga
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Calculando ruta...", Toast.LENGTH_SHORT).show();
                });
            }

            // Remover la ruta actual si existe
            if (currentRoute != null) {
                mapView.getOverlays().remove(currentRoute);
                mapView.invalidate();
            }

            // Solicitar ruta a la API de Mapbox
            directionsClient.getDirections("walking", origin, destination, new MapboxDirectionsClient.DirectionsCallback() {
                @Override
                public void onDirectionsSuccess(final Polyline route) {
                    // Ejecutar en el hilo principal
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            try {
                                if (!isFragmentActive || mapView == null) return;

                                // Guardar la ruta actual
                                currentRoute = route;

                                // Agregar la ruta al mapa
                                mapView.getOverlays().add(0, route); // Agregar al inicio para que esté debajo de los marcadores

                                // Hacer zoom al área que contiene la ruta
                                zoomToShowRoute(origin, destination);

                                // Actualizar la vista
                                mapView.invalidate();

                                // Notificar al usuario
                                Toast.makeText(getContext(), "Ruta cargada", Toast.LENGTH_SHORT).show();
                            } catch (Exception e) {
                                Log.e(TAG, "Error al mostrar ruta", e);
                            }
                        });
                    }
                }

                @Override
                public void onDirectionsFailure(final String errorMessage) {
                    // Mostrar error en el hilo principal
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "Error: " + errorMessage, Toast.LENGTH_SHORT).show();
                        });
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error al solicitar ruta", e);
        }
    }

    /**
     * Hace zoom para mostrar tanto el origen como el destino de la ruta
     */
    private void zoomToShowRoute(GeoPoint origin, GeoPoint destination) {
        try {
            // Calcular el centro entre los dos puntos
            double centerLat = (origin.getLatitude() + destination.getLatitude()) / 2;
            double centerLon = (origin.getLongitude() + destination.getLongitude()) / 2;
            GeoPoint center = new GeoPoint(centerLat, centerLon);

            // Calcular la distancia entre los puntos para determinar el nivel de zoom
            double distanceLat = Math.abs(origin.getLatitude() - destination.getLatitude());
            double distanceLon = Math.abs(origin.getLongitude() - destination.getLongitude());

            // Añadir un margen
            distanceLat *= 1.5;
            distanceLon *= 1.5;

            // Calcular un zoom aproximado que muestre ambos puntos con margen
            double maxDistance = Math.max(distanceLat, distanceLon);
            double zoom = 15.0; // Zoom predeterminado

            if (maxDistance > 0.05) zoom = 13.0;
            else if (maxDistance > 0.02) zoom = 14.0;
            else if (maxDistance > 0.01) zoom = 15.0;
            else if (maxDistance > 0.005) zoom = 16.0;
            else if (maxDistance > 0.002) zoom = 17.0;
            else zoom = 18.0;

            // Centrar el mapa con animación
            mapController.animateTo(center);
            mapController.setZoom(zoom);

        } catch (Exception e) {
            Log.e(TAG, "Error al hacer zoom a la ruta", e);
        }
    }

    /**
     * Limpia la ruta actual si existe
     */
    public void clearRoute() {
        try {
            if (currentRoute != null && mapView != null) {
                mapView.getOverlays().remove(currentRoute);
                currentRoute = null;
                destinationPoint = null;
                mapView.invalidate();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al limpiar ruta", e);
        }
    }
}