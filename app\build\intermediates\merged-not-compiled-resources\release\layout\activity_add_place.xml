<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.AddPlaceActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:titleTextColor="@android:color/white"
            app:title="Agregar nuevo lugar" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Nombre del lugar">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Descripción">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:inputType="textMultiLine"
                    android:lines="4" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_image_url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="URL de imagen">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_image_url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textUri" />

            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_latitude"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:hint="Latitud">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_latitude"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal|numberSigned" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_longitude"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:hint="Longitud">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_longitude"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal|numberSigned" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Categoría"
                android:textSize="16sp"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/rg_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp">

                <RadioButton
                    android:id="@+id/rb_hotel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hotel" />

                <RadioButton
                    android:id="@+id/rb_restaurant"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Restaurante" />

                <RadioButton
                    android:id="@+id/rb_bathroom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Baño" />

                <RadioButton
                    android:id="@+id/rb_church"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Iglesia" />

                <RadioButton
                    android:id="@+id/rb_transport"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Transporte" />

                <RadioButton
                    android:id="@+id/rb_attraction"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Atracción" />

            </RadioGroup>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Valoración"
                android:textSize="16sp"
                android:textStyle="bold" />

            <RatingBar
                android:id="@+id/rating_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:numStars="5"
                android:stepSize="0.5" />

            <Button
                android:id="@+id/btn_use_current_location"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Usar mi ubicación actual" />

            <Button
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:backgroundTint="@android:color/holo_green_dark"
                android:text="Guardar"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 