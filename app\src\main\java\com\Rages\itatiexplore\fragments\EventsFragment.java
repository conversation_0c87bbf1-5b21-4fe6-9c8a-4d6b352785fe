package com.Rages.itatiexplore.fragments;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.EditorInfo;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.Rages.itatiexplore.R;
import com.Rages.itatiexplore.EventDetailActivity;
import com.Rages.itatiexplore.adapters.EventAdapter;
import com.Rages.itatiexplore.adapters.FeaturedEventAdapter;
import com.Rages.itatiexplore.firebase.FirestoreManager;
import com.Rages.itatiexplore.models.Event;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;

import java.util.ArrayList;
import java.util.List;

public class EventsFragment extends Fragment implements EventAdapter.EventClickListener, FeaturedEventAdapter.EventClickListener {

    private static final String TAG = "EventsFragment";

    // Views
    private RecyclerView recyclerView;
    private RecyclerView featuredRecyclerView;
    private TextView emptyView;
    private ProgressBar progressBar;
    private TextInputEditText searchInput;
    private MaterialCardView searchPanel;
    private MaterialCardView filterPanel;
    private MaterialButton searchButton;
    private MaterialButton filterButton;
    private MaterialButton refreshButton;
    private FrameLayout loadingOverlay;
    private View emptyContainer;

    // Data
    private List<Event> allEventList; // Lista completa de eventos
    private List<Event> filteredEventList; // Lista filtrada para mostrar
    private List<Event> featuredEventList; // Lista de eventos destacados
    private EventAdapter adapter;
    private FeaturedEventAdapter featuredAdapter;
    private FirestoreManager firestoreManager;
    private boolean isSearchVisible = false;
    private boolean isFilterVisible = false;

    public EventsFragment() {
        // Required empty public constructor
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_events, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        try {
            // Inicializar vistas
            recyclerView = view.findViewById(R.id.recyclerView);
            featuredRecyclerView = view.findViewById(R.id.featured_recycler_view);
            emptyView = view.findViewById(R.id.empty_view);
            progressBar = view.findViewById(R.id.progressBar);
            searchInput = view.findViewById(R.id.search_input);
            searchPanel = view.findViewById(R.id.search_panel);
            filterPanel = view.findViewById(R.id.filter_panel);
            // searchButton = view.findViewById(R.id.search_button); // Ya no existe
            filterButton = view.findViewById(R.id.filter_button);
            // refreshButton = view.findViewById(R.id.btn_refresh); // Ya no existe
            loadingOverlay = view.findViewById(R.id.loading_overlay);
            emptyContainer = view.findViewById(R.id.empty_container);

            // Configurar RecyclerView para eventos normales
            recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
            recyclerView.setHasFixedSize(true);

            // Configurar RecyclerView para eventos destacados
            featuredRecyclerView.setHasFixedSize(true);

            // Inicializar datos
            allEventList = new ArrayList<>();
            filteredEventList = new ArrayList<>();
            featuredEventList = new ArrayList<>();

            // Inicializar adaptadores
            adapter = new EventAdapter(filteredEventList, this);
            featuredAdapter = new FeaturedEventAdapter(featuredEventList, this);

            // Asignar adaptadores
            recyclerView.setAdapter(adapter);
            featuredRecyclerView.setAdapter(featuredAdapter);

            // Configurar botones y paneles
            setupButtons();

            // Configurar el buscador
            setupSearchInput();

            // Configurar los chips de filtro
            setupFilterChips();

            // Configurar botón de actualizar (ya no existe en el nuevo diseño)
            // if (refreshButton != null) {
            //     refreshButton.setOnClickListener(v -> obtenerEventosDeFirestore());
            // }

            // Obtener instancia de FirestoreManager
            firestoreManager = FirestoreManager.getInstance();

            // Cargar eventos de Firestore
            obtenerEventosDeFirestore();

        } catch (Exception e) {
            Log.e(TAG, "Error al inicializar el fragmento", e);
            Toast.makeText(requireContext(), "Error al inicializar: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Configura los botones de búsqueda y filtro
     */
    private void setupButtons() {
        // Botón de búsqueda
        searchButton.setOnClickListener(v -> {
            if (isFilterVisible) {
                // Si el panel de filtros está visible, ocultarlo primero
                toggleFilterPanel(false);
            }
            toggleSearchPanel(!isSearchVisible);
        });

        // Botón de filtro
        filterButton.setOnClickListener(v -> {
            if (isSearchVisible) {
                // Si el panel de búsqueda está visible, ocultarlo primero
                toggleSearchPanel(false);
            }
            toggleFilterPanel(!isFilterVisible);
        });
    }

    /**
     * Muestra u oculta el panel de búsqueda
     */
    private void toggleSearchPanel(boolean show) {
        if (show) {
            searchPanel.setVisibility(View.VISIBLE);
            searchPanel.startAnimation(AnimationUtils.loadAnimation(requireContext(), android.R.anim.fade_in));
            searchInput.requestFocus();
            isSearchVisible = true;
        } else {
            searchPanel.startAnimation(AnimationUtils.loadAnimation(requireContext(), android.R.anim.fade_out));
            searchPanel.setVisibility(View.GONE);
            isSearchVisible = false;
        }
    }

    /**
     * Muestra u oculta el panel de filtros
     */
    private void toggleFilterPanel(boolean show) {
        if (show) {
            filterPanel.setVisibility(View.VISIBLE);
            filterPanel.startAnimation(AnimationUtils.loadAnimation(requireContext(), android.R.anim.fade_in));
            isFilterVisible = true;
        } else {
            filterPanel.startAnimation(AnimationUtils.loadAnimation(requireContext(), android.R.anim.fade_out));
            filterPanel.setVisibility(View.GONE);
            isFilterVisible = false;
        }
    }

    /**
     * Configura los chips de filtro con listeners
     */
    private void setupFilterChips() {
        try {
            com.google.android.material.chip.ChipGroup filterChipGroup = requireView().findViewById(R.id.filter_chip_group);

            // Configurar listener para los chips de filtro
            filterChipGroup.setOnCheckedChangeListener((group, checkedId) -> {
                if (checkedId == R.id.chip_all) {
                    filterEventsByPeriod("all");
                } else if (checkedId == R.id.chip_today) {
                    filterEventsByPeriod("today");
                } else if (checkedId == R.id.chip_week) {
                    filterEventsByPeriod("week");
                // } else if (checkedId == R.id.chip_month) {
                //     filterEventsByPeriod("month");
                } else if (checkedId == R.id.chip_religious) {
                    filterEventsByCategory("Religioso");
                // } else if (checkedId == R.id.chip_cultural) {
                //     filterEventsByCategory("Cultural");
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error al configurar chips de filtro", e);
        }
    }

    /**
     * Filtra eventos por período de tiempo
     */
    private void filterEventsByPeriod(String period) {
        if (allEventList == null) return;

        filteredEventList.clear();

        if (period.equals("all")) {
            // Mostrar todos los eventos
            filteredEventList.addAll(allEventList);
        } else {
            // Obtener la fecha actual
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            java.util.Date today = calendar.getTime();

            // Filtrar según el período
            for (Event event : allEventList) {
                try {
                    // Convertir la fecha del evento a un objeto Date
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault());
                    java.util.Date eventDate = sdf.parse(event.getDate());

                    if (eventDate != null) {
                        if (period.equals("today")) {
                            // Solo eventos de hoy
                            java.util.Calendar todayCal = java.util.Calendar.getInstance();
                            todayCal.setTime(today);

                            java.util.Calendar eventCal = java.util.Calendar.getInstance();
                            eventCal.setTime(eventDate);

                            if (todayCal.get(java.util.Calendar.YEAR) == eventCal.get(java.util.Calendar.YEAR) &&
                                todayCal.get(java.util.Calendar.DAY_OF_YEAR) == eventCal.get(java.util.Calendar.DAY_OF_YEAR)) {
                                filteredEventList.add(event);
                            }
                        } else if (period.equals("week")) {
                            // Eventos de esta semana
                            java.util.Calendar weekCal = java.util.Calendar.getInstance();
                            weekCal.setTime(today);
                            weekCal.add(java.util.Calendar.DAY_OF_YEAR, 7);

                            if (eventDate.after(today) && eventDate.before(weekCal.getTime())) {
                                filteredEventList.add(event);
                            }
                        } else if (period.equals("month")) {
                            // Eventos de este mes
                            java.util.Calendar monthCal = java.util.Calendar.getInstance();
                            monthCal.setTime(today);
                            monthCal.add(java.util.Calendar.MONTH, 1);

                            if (eventDate.after(today) && eventDate.before(monthCal.getTime())) {
                                filteredEventList.add(event);
                            }
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error al filtrar evento por fecha: " + e.getMessage());
                    // Si hay error, incluir el evento en la lista
                    filteredEventList.add(event);
                }
            }
        }

        // Actualizar la UI
        updateUIWithFilteredEvents();
    }

    /**
     * Filtra eventos por categoría
     */
    private void filterEventsByCategory(String category) {
        if (allEventList == null) return;

        filteredEventList.clear();

        // Filtrar por categoría
        for (Event event : allEventList) {
            if (event.getCategory() != null && event.getCategory().equalsIgnoreCase(category)) {
                filteredEventList.add(event);
            }
        }

        // Actualizar la UI
        updateUIWithFilteredEvents();
    }

    /**
     * Configura el campo de búsqueda con listeners
     */
    private void setupSearchInput() {
        // Listener para cambios de texto
        searchInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // No necesitamos implementar esto
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // Filtramos la lista cada vez que cambia el texto
                filterEvents(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
                // No necesitamos implementar esto
            }
        });

        // Listener para el botón de búsqueda en el teclado
        searchInput.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                filterEvents(searchInput.getText().toString());
                return true;
            }
            return false;
        });
    }

    /**
     * Filtra los eventos según el texto de búsqueda
     * @param query Texto a buscar (puede ser fecha o texto parcial)
     */
    private void filterEvents(String query) {
        if (allEventList == null) {
            return;
        }

        // Limpiar la lista filtrada
        filteredEventList.clear();

        if (query == null || query.isEmpty()) {
            // Si no hay consulta, mostrar todos los eventos
            filteredEventList.addAll(allEventList);
        } else {
            // Convertir a minúsculas para búsqueda sin distinción entre mayúsculas y minúsculas
            String lowerCaseQuery = query.toLowerCase();

            // Si es solo una letra, buscar eventos que comiencen con esa letra
            if (query.length() == 1) {
                for (Event event : allEventList) {
                    if (event.getTitle().toLowerCase().startsWith(lowerCaseQuery)) {
                        filteredEventList.add(event);
                    }
                }
            } else {
                // Buscar en título, fecha, descripción y ubicación
                for (Event event : allEventList) {
                    if (event.getTitle().toLowerCase().contains(lowerCaseQuery) ||
                        event.getDate().toLowerCase().contains(lowerCaseQuery) ||
                        (event.getDescription() != null && event.getDescription().toLowerCase().contains(lowerCaseQuery)) ||
                        (event.getLocation() != null && event.getLocation().toLowerCase().contains(lowerCaseQuery))) {

                        filteredEventList.add(event);
                    }
                }
            }
        }

        // Actualizar la UI
        updateUIWithFilteredEvents();
    }

    /**
     * Actualiza la UI después de filtrar eventos
     */
    private void updateUIWithFilteredEvents() {
        adapter.notifyDataSetChanged();

        // Obtener referencia a la sección de destacados y a la sección de próximos eventos (ya no existen en el nuevo diseño)
        // View featuredSectionParent = requireView().findViewById(R.id.featured_section);
        // View upcomingEventsSection = requireView().findViewById(R.id.upcoming_events_section);
        // TextView upcomingEventsTitle = requireView().findViewById(R.id.upcoming_events_title);
        // TextView noEventsMessage = requireView().findViewById(R.id.no_events_message);

        // Primero, manejamos la visibilidad de los eventos destacados
        if (featuredEventList.isEmpty()) {
            // Si no hay eventos destacados, ocultar esa sección
            // if (featuredSectionParent != null) {
            //     featuredSectionParent.setVisibility(View.GONE);
            // }
            featuredRecyclerView.setVisibility(View.GONE);
        } else {
            // Si hay eventos destacados, mostrarlos
            // if (featuredSectionParent != null) {
            //     featuredSectionParent.setVisibility(View.VISIBLE);
            // }
            featuredRecyclerView.setVisibility(View.VISIBLE);
        }

        // Ahora manejamos la sección de eventos regulares
        if (filteredEventList.isEmpty()) {
            // Si no hay eventos filtrados, ocultar la lista y mostrar mensaje
            recyclerView.setVisibility(View.GONE);

            // Mantener el título original pero mostrar un mensaje de "no hay eventos"
            // if (upcomingEventsTitle != null) {
            //     upcomingEventsTitle.setText("Próximos Eventos");
            // }

            // Mostrar el mensaje de que no hay eventos
            // if (noEventsMessage != null) {
            //     noEventsMessage.setVisibility(View.VISIBLE);
            //     noEventsMessage.setText("No se encontraron eventos que coincidan con el filtro seleccionado");
            // }

            // Ocultar el contenedor vacío ya que usaremos el mensaje específico
            emptyContainer.setVisibility(View.GONE);
        } else {
            // Si hay eventos filtrados, mostrar la lista normalmente
            recyclerView.setVisibility(View.VISIBLE);

            // Mantener el título original
            // if (upcomingEventsTitle != null) {
            //     upcomingEventsTitle.setText("Próximos Eventos");
            // }

            // Ocultar el mensaje de que no hay eventos
            // if (noEventsMessage != null) {
            //     noEventsMessage.setVisibility(View.GONE);
            // }

            // Ocultar el contenedor vacío
            emptyContainer.setVisibility(View.GONE);
        }
    }

    /**
     * Obtiene los eventos principales de Firestore
     */
    private void obtenerEventosDeFirestore() {
        Log.d(TAG, "Obteniendo eventos de Firestore");

        // Mostrar progreso
        loadingOverlay.setVisibility(View.VISIBLE);
        emptyContainer.setVisibility(View.GONE);

        // Solo mostrar eventos principales (categorías)
        firestoreManager.obtenerEventosPrincipales(new FirestoreManager.OnEventosObtainedListener() {
            @Override
            public void onEventosObtenidos(List<Event> eventos) {
                if (isAdded()) {
                    requireActivity().runOnUiThread(() -> {
                        loadingOverlay.setVisibility(View.GONE);

                        // Actualizar listas
                        allEventList.clear();
                        allEventList.addAll(eventos);

                        // Separar eventos destacados
                        separateFeaturedEvents();

                        // Aplicar filtro actual
                        filterEvents(searchInput.getText() != null ? searchInput.getText().toString() : "");

                        Log.d(TAG, "Eventos principales cargados: " + allEventList.size());
                    });
                }
            }

            @Override
            public void onError(String errorMessage) {
                if (isAdded()) {
                    requireActivity().runOnUiThread(() -> {
                        loadingOverlay.setVisibility(View.GONE);
                        emptyContainer.setVisibility(View.VISIBLE);
                        recyclerView.setVisibility(View.GONE);
                        featuredRecyclerView.setVisibility(View.GONE);

                        Toast.makeText(requireContext(), "Error al cargar eventos: " + errorMessage, Toast.LENGTH_LONG).show();
                        Log.e(TAG, "Error al obtener eventos: " + errorMessage);
                    });
                }
            }
        });
    }

    /**
     * Separa los eventos destacados de los eventos normales
     */
    private void separateFeaturedEvents() {
        featuredEventList.clear();

        // Buscar eventos destacados (mainEvent = true o con categoría especial)
        for (Event event : allEventList) {
            if (event.isMainEvent() ||
                (event.getCategory() != null && event.getCategory().equalsIgnoreCase("Destacado"))) {
                featuredEventList.add(event);
            }
        }

        // Actualizar adaptador de eventos destacados
        featuredAdapter.notifyDataSetChanged();

        // Mostrar u ocultar sección de destacados según si hay eventos
        if (featuredEventList.isEmpty()) {
            featuredRecyclerView.setVisibility(View.GONE);
            // Buscar el contenedor padre
            // View featuredSectionParent = requireView().findViewById(R.id.featured_section);
            // if (featuredSectionParent != null) {
            //     featuredSectionParent.setVisibility(View.GONE);
            // }
        } else {
            featuredRecyclerView.setVisibility(View.VISIBLE);
            // Buscar el contenedor padre
            // View featuredSectionParent = requireView().findViewById(R.id.featured_section);
            // if (featuredSectionParent != null) {
            //     featuredSectionParent.setVisibility(View.VISIBLE);
            // }
        }
    }

    @Override
    public void onEventClick(Event event) {
        // Abrir detalles del evento
        Intent intent = new Intent(requireContext(), EventDetailActivity.class);
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_ID, event.getId());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_TITLE, event.getTitle());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_DATE, event.getDate());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_TIME, event.getTime());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_LOCATION, event.getLocation());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_DESCRIPTION, event.getDescription());
        intent.putExtra(EventDetailActivity.EXTRA_EVENT_IMAGE_URL, event.getImageUrl());
        intent.putExtra(EventDetailActivity.EXTRA_IS_MAIN_EVENT, event.isMainEvent());
        intent.putExtra(EventDetailActivity.EXTRA_MAIN_EVENT_ID, event.getMainEventId());
        startActivity(intent);
    }

    @Override
    public void onResume() {
        super.onResume();
        // Recargar eventos al volver al fragmento
        obtenerEventosDeFirestore();
    }
}