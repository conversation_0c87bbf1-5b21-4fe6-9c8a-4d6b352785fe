<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="20dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/ripple"
    android:checkable="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Imagen del evento -->
        <ImageView
            android:id="@+id/event_image"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:contentDescription="@string/event_image_description"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/backgrounds/scenic" />

        <!-- Contenedor de información del evento -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/event_image"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Etiqueta de categoría -->
            <com.google.android.material.chip.Chip
                android:id="@+id/event_category_chip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Religioso"
                android:textColor="@color/white"
                android:textSize="10sp"
                app:chipBackgroundColor="@color/primary"
                app:chipMinHeight="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Título del evento -->
            <TextView
                android:id="@+id/event_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/montserrat"
                android:maxLines="1"
                android:textColor="@color/on_surface"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@+id/event_category_chip"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Festival de la Virgen de Itatí" />

            <!-- Fecha del evento -->
            <TextView
                android:id="@+id/event_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/event_title"
                tools:text="10 de Septiembre, 2023" />

            <!-- Hora del evento -->
            <TextView
                android:id="@+id/event_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/montserrat"
                android:textColor="@color/primary_variant"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/event_date"
                app:layout_constraintStart_toEndOf="@+id/event_date"
                app:layout_constraintTop_toTopOf="@+id/event_date"
                tools:text="• 15:00 hs" />

            <!-- Ubicación del evento -->
            <TextView
                android:id="@+id/event_location"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:drawableStart="@android:drawable/ic_menu_mylocation"
                android:drawablePadding="4dp"
                android:drawableTint="@color/secondary"
                android:ellipsize="end"
                android:fontFamily="@font/montserrat"
                android:maxLines="1"
                android:textColor="@color/secondary"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/event_date"
                tools:text="Basílica de Itatí" />

            <!-- Botones de acción -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/event_location">



                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_details"
                    style="@style/Widget.MaterialComponents.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:text="Ver Detalles"
                    android:textColor="@color/primary"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- Descripción del evento (oculta en esta vista) -->
            <TextView
                android:id="@+id/event_description"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>