<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".fragments.DonationsFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- <PERSON><PERSON><PERSON><PERSON> con título y logo -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/header_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="0dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/title_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/primary"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingBottom="16dp">

                <!-- Título con logo -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="8dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/ic_mercado_pago"
                        app:tint="@color/white" />

                    <TextView
                        android:id="@+id/donations_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Donaciones"
                        android:textColor="@android:color/white"
                        android:textSize="18sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textStyle="bold" />

                    <!-- Indicador de estado -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@color/white"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        android:paddingTop="4dp"
                        android:paddingBottom="4dp"
                        android:layout_marginStart="8dp"
                        app:cornerRadius="12dp">

                        <View
                            android:id="@+id/status_indicator"
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@drawable/circle_shape"
                            android:backgroundTint="@color/success"/>

                        <TextView
                            android:id="@+id/status_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="En línea"
                            android:textColor="@color/success"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:layout_marginStart="4dp"/>
                    </LinearLayout>
                </LinearLayout>

                <!-- Subtítulo -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Apoya el desarrollo de Itatí mediante transferencia"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:paddingHorizontal="16dp"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Contenido principal -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="24dp">

                <!-- Tarjeta de CVU -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="12dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="CVU Mercado Pago"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary"
                            android:layout_marginBottom="12dp"/>

                        <!-- Número de CVU -->
                        <TextView
                            android:id="@+id/cvu_number"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0000003100019372211224"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary"
                            android:textIsSelectable="true"
                            android:background="@color/surface_variant"
                            android:padding="16dp"
                            android:gravity="center"
                            android:layout_marginBottom="16dp"/>

                        <!-- Botón de copiar -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_copy_cvu"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Copiar CVU"
                            android:textAllCaps="false"
                            android:fontFamily="@font/montserrat"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            app:backgroundTint="@color/success"
                            app:cornerRadius="8dp"
                            app:icon="@android:drawable/ic_menu_save"
                            app:iconGravity="textStart"
                            app:iconTint="@color/white" />

                        <!-- Texto de estado de copia -->
                        <TextView
                            android:id="@+id/copy_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Listo para copiar"
                            android:textSize="14sp"
                            android:fontFamily="@font/montserrat"
                            android:textColor="@color/success"
                            android:gravity="center"
                            android:layout_marginTop="8dp"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Tarjeta de instrucciones -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="12dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Cómo realizar la transferencia"
                            android:textSize="16sp"
                            android:fontFamily="@font/montserrat"
                            android:textStyle="bold"
                            android:textColor="@color/primary"
                            android:layout_marginBottom="16dp"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:background="@drawable/circle_shape"
                                android:backgroundTint="@color/primary_light"
                                android:text="1"
                                android:textColor="@color/primary"
                                android:textStyle="bold"
                                android:gravity="center"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Copia el CVU usando el botón de arriba"
                                android:textSize="14sp"
                                android:fontFamily="@font/montserrat"
                                android:layout_marginStart="12dp"
                                android:layout_gravity="center_vertical"/>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:background="@drawable/circle_shape"
                                android:backgroundTint="@color/primary_light"
                                android:text="2"
                                android:textColor="@color/primary"
                                android:textStyle="bold"
                                android:gravity="center"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Abre tu aplicación de Mercado Pago"
                                android:textSize="14sp"
                                android:fontFamily="@font/montserrat"
                                android:layout_marginStart="12dp"
                                android:layout_gravity="center_vertical"/>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:background="@drawable/circle_shape"
                                android:backgroundTint="@color/primary_light"
                                android:text="3"
                                android:textColor="@color/primary"
                                android:textStyle="bold"
                                android:gravity="center"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Selecciona 'Transferir' y pega el CVU"
                                android:textSize="14sp"
                                android:fontFamily="@font/montserrat"
                                android:layout_marginStart="12dp"
                                android:layout_gravity="center_vertical"/>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:background="@drawable/circle_shape"
                                android:backgroundTint="@color/primary_light"
                                android:text="4"
                                android:textColor="@color/primary"
                                android:textStyle="bold"
                                android:gravity="center"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Ingresa el monto y confirma la operación"
                                android:textSize="14sp"
                                android:fontFamily="@font/montserrat"
                                android:layout_marginStart="12dp"
                                android:layout_gravity="center_vertical"/>
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>